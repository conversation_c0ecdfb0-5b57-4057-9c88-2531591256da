
CH32V003F4U6.elf:     file format elf32-littleriscv
CH32V003F4U6.elf
architecture: riscv:rv32, flags 0x00000112:
EXEC_P, HAS_SYMS, D_PAGED
start address 0x00000000

Program Header:
    LOAD off    0x00001000 vaddr 0x00000000 paddr 0x00000000 align 2**12
         filesz 0x0000282c memsz 0x0000282c flags r-x
    LOAD off    0x00004000 vaddr 0x20000000 paddr 0x0000282c align 2**12
         filesz 0x00000040 memsz 0x000001ec flags rw-
    LOAD off    0x00004700 vaddr 0x20000700 paddr 0x20000700 align 2**12
         filesz 0x00000000 memsz 0x00000100 flags rw-

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .init         000000a0  00000000  00000000  00001000  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  1 .highcodelalign 00000000  000000a0  000000a0  00004040  2**0
                  CONTENTS
  2 .highcode     00000000  20000000  20000000  00004040  2**0
                  CONTENTS
  3 .text         0000278c  000000a0  000000a0  000010a0  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  4 .fini         00000000  0000282c  0000282c  00004040  2**0
                  CONTENTS, ALLOC, LOAD, CODE
  5 .dalign       00000000  20000000  20000000  00004040  2**0
                  CONTENTS
  6 .dlalign      00000000  0000282c  0000282c  00004040  2**0
                  CONTENTS
  7 .data         00000040  20000000  0000282c  00004000  2**2
                  CONTENTS, ALLOC, LOAD, DATA
  8 .bss          000001ac  20000040  0000286c  00004040  2**2
                  ALLOC
  9 .stack        00000100  20000700  20000700  00004700  2**0
                  ALLOC
 10 .debug_info   0001438a  00000000  00000000  00004040  2**0
                  CONTENTS, READONLY, DEBUGGING
 11 .debug_abbrev 000038d4  00000000  00000000  000183ca  2**0
                  CONTENTS, READONLY, DEBUGGING
 12 .debug_loc    00005134  00000000  00000000  0001bc9e  2**0
                  CONTENTS, READONLY, DEBUGGING
 13 .debug_aranges 00000b08  00000000  00000000  00020dd8  2**3
                  CONTENTS, READONLY, DEBUGGING
 14 .debug_ranges 00000c90  00000000  00000000  000218e0  2**3
                  CONTENTS, READONLY, DEBUGGING
 15 .debug_line   0000d6b5  00000000  00000000  00022570  2**0
                  CONTENTS, READONLY, DEBUGGING
 16 .debug_str    000034c9  00000000  00000000  0002fc25  2**0
                  CONTENTS, READONLY, DEBUGGING
 17 .comment      00000033  00000000  00000000  000330ee  2**0
                  CONTENTS, READONLY
 18 .debug_frame  00001908  00000000  00000000  00033124  2**2
                  CONTENTS, READONLY, DEBUGGING
SYMBOL TABLE:
00000000 l    d  .init	00000000 .init
000000a0 l    d  .highcodelalign	00000000 .highcodelalign
20000000 l    d  .highcode	00000000 .highcode
000000a0 l    d  .text	00000000 .text
0000282c l    d  .fini	00000000 .fini
20000000 l    d  .dalign	00000000 .dalign
0000282c l    d  .dlalign	00000000 .dlalign
20000000 l    d  .data	00000000 .data
20000040 l    d  .bss	00000000 .bss
20000700 l    d  .stack	00000000 .stack
00000000 l    d  .debug_info	00000000 .debug_info
00000000 l    d  .debug_abbrev	00000000 .debug_abbrev
00000000 l    d  .debug_loc	00000000 .debug_loc
00000000 l    d  .debug_aranges	00000000 .debug_aranges
00000000 l    d  .debug_ranges	00000000 .debug_ranges
00000000 l    d  .debug_line	00000000 .debug_line
00000000 l    d  .debug_str	00000000 .debug_str
00000000 l    d  .comment	00000000 .comment
00000000 l    d  .debug_frame	00000000 .debug_frame
00000000 l    df *ABS*	00000000 adc_config.c
00000000 l    df *ABS*	00000000 adc_display.c
0000216c l     O .text	00000008 CSWTCH.2
00000000 l    df *ABS*	00000000 ch32v00x_it.c
00000000 l    df *ABS*	00000000 display_control.c
00000000 l    df *ABS*	00000000 main.c
00000000 l    df *ABS*	00000000 pwm_config.c
00000000 l    df *ABS*	00000000 st7735.c
000009f8 l     F .text	0000003e SPI_send_DMA
00000a36 l     F .text	00000012 SPI_send
00000a48 l     F .text	00000016 write_command_8
00000a5e l     F .text	00000020 write_data_16
00000a7e l     F .text	0000003c tft_set_window
2000007a l     O .bss	00000002 _bg_color
2000007c l     O .bss	00000140 _buffer
200001bc l     O .bss	00000002 _cursor_x
200001be l     O .bss	00000002 _cursor_y
200001c0 l     O .bss	0000000c str.4169
20000000 l     O .data	00000002 _color
00002324 l     O .text	00000500 font
00000000 l    df *ABS*	00000000 system_ch32v00x.c
00000000 l    df *ABS*	00000000 touch_button.c
00000000 l    df *ABS*	00000000 ch32v00x_adc.c
00000000 l    df *ABS*	00000000 ch32v00x_dbgmcu.c
00000000 l    df *ABS*	00000000 ch32v00x_exti.c
00000000 l    df *ABS*	00000000 ch32v00x_gpio.c
00000000 l    df *ABS*	00000000 ch32v00x_misc.c
00000000 l    df *ABS*	00000000 ch32v00x_rcc.c
20000018 l     O .data	00000014 ADCPrescTable
2000002c l     O .data	00000010 APBAHBPrescTable
00000000 l    df *ABS*	00000000 ch32v00x_tim.c
00000000 l    df *ABS*	00000000 ch32v00x_usart.c
00000000 l    df *ABS*	00000000 debug.c
200001e8 l     O .bss	00000002 p_ms
200001ea l     O .bss	00000001 p_us
00000000 l    df *ABS*	00000000 wchprintf.c
00000000 l    df *ABS*	00000000 memcpy.c
00001c56  w    F .text	00000004 printDouble
000003d8 g     F .text	00000026 ADC_Display_Init
20000040 g     O .bss	0000000c adc_display_config
00001c5a  w    F .text	00000360 print
00001fba  w    F .text	00000024 printf
20000840 g       .data	00000000 __global_pointer$
0000069e g     F .text	00000026 Display_Control_Init
00001200  w      .text	00000000 TIM1_CC_IRQHandler
00000582 g     F .text	00000010 HardFault_Handler
00001b3a  w    F .text	00000118 printInt
000017ce g     F .text	0000000e TIM_OC1PreloadConfig
0000110c g     F .text	0000002a Touch_Button_Init
00001200  w      .text	00000000 SysTick_Handler
00001322 g     F .text	0000000a ADC_StartCalibration
0000052a g     F .text	00000020 ADC_Display_Should_Update
0000156a g     F .text	00000062 NVIC_Init
00001200  w      .text	00000000 PVD_IRQHandler
00001fde g     F .text	0000002a snprintf
00000580 g     F .text	00000002 NMI_Handler
00001420 g     F .text	0000000a DBGMCU_GetCHIPID
200001cc g     O .bss	00000004 system_tick_ms
00000c4a g     F .text	0000000e tft_set_cursor
000018f0 g     F .text	0000000a USART_GetFlagStatus
20000040 g       .bss	00000000 _sbss
00000100 g       *ABS*	00000000 __stack_size
0000140e g     F .text	0000000a ADC_GetFlagStatus
00001968 g     F .text	00000058 USART_Printf_Init
000000aa g     F .text	0000000a .hidden __riscv_restore_2
0000178c g     F .text	00000016 TIM_CtrlPWMOutputs
00002052 g     F .text	000000d2 memcpy
00001300 g     F .text	00000010 ADC_Cmd
00001c52  w    F .text	00000004 printLongLongInt
0000037c g     F .text	0000005c ADC_Display_Draw_Channel_Labels
00001774 g     F .text	00000018 TIM_Cmd
00002008 g     F .text	0000004a puts
00000354 g     F .text	00000028 ADC_Display_Draw_Header
20000014 g     O .data	00000004 SystemCoreClock
2000004c g     O .bss	0000000c display_control
000005be g     F .text	00000016 Display_Control_Turn_On
000000d4 g     F .text	0000002c .hidden __udivsi3
000000a0 g       .init	00000000 _einit
000017f8 g     F .text	0000000c TIM_ClearITPendingBit
00001680 g     F .text	0000001e RCC_APB2PeriphClockCmd
000014bc g     F .text	0000007c GPIO_Init
00000234 g     F .text	00000080 ADC_Config_Init
200001e4 g     O .bss	00000004 NVIC_Priority_Group
00001200  w      .text	00000000 SPI1_IRQHandler
00001310 g     F .text	0000000a ADC_ResetCalibration
000018d2 g     F .text	00000016 USART_Cmd
000000a0 g     F .text	0000000a .hidden __riscv_save_1
00000d4c g     F .text	00000020 tft_print
0000054a g     F .text	00000036 ADC_Display_Update
000000aa g     F .text	0000000a .hidden __riscv_restore_0
00001200  w      .text	00000000 AWU_IRQHandler
00000592 g     F .text	00000008 EXTI7_0_IRQHandler
0000169e g     F .text	0000001e RCC_APB1PeriphClockCmd
20000700 g       .stack	00000000 _heap_end
000006c4 g     F .text	0000002a Display_Control_Show_Off_Message
000006ee g     F .text	00000040 Display_Control_Update
00001200  w      .text	00000000 DMA1_Channel4_IRQHandler
0000134c g     F .text	000000ba ADC_RegularChannelConfig
00001200  w      .text	00000000 ADC1_IRQHandler
00001406 g     F .text	00000008 ADC_GetConversionValue
000001f2 g     F .text	00000042 ADC_GPIO_Config
00001136 g     F .text	00000072 Touch_Button_Update
000014b2 g     F .text	0000000a EXTI_ClearITPendingBit
0000132c g     F .text	00000008 ADC_GetCalibrationStatus
200001ec g       .bss	00000000 _ebss
00001200  w      .text	00000000 DMA1_Channel7_IRQHandler
0000170a g     F .text	0000006a TIM_OC1Init
00001334 g     F .text	00000018 ADC_SoftwareStartConvCmd
0000131a g     F .text	00000008 ADC_GetResetCalibrationStatus
000018fa g     F .text	00000034 Delay_Init
000010ca g     F .text	00000042 Touch_Button_EXTI_Config
000017b4 g     F .text	0000001a TIM_ARRPreloadConfig
00000c62 g     F .text	00000006 tft_set_background_color
00000100 g     F .text	00000008 .hidden __umodsi3
0000089a g     F .text	0000009a PWM_Timer_Config
00001200  w      .text	00000000 I2C1_EV_IRQHandler
000002b4 g     F .text	00000046 ADC_Read_Channel
000017e0 g     F .text	00000018 TIM_GetITStatus
000015de g     F .text	000000a2 RCC_GetClocksFreq
00001044 g     F .text	00000030 Touch_Button_GPIO_Config
00001200  w      .text	00000000 DMA1_Channel6_IRQHandler
00001804 g     F .text	000000ce USART_Init
00001200  w      .text	00000000 RCC_IRQHandler
00001200  w      .text	00000000 TIM1_TRG_COM_IRQHandler
00001200  w      .text	00000000 DMA1_Channel1_IRQHandler
00000000 g       .init	00000000 _start
20000004 g     O .data	00000010 AHBPrescTable
000009e4 g     F .text	0000000e PWM_Turn_Off
00000612 g     F .text	00000018 Display_Control_Clear_Screen
00001a54  w    F .text	000000e6 prints
000017a2 g     F .text	00000012 TIM_ITConfig
20000000 g       .highcode	00000000 _highcode_vma_start
0000142a g     F .text	0000006a EXTI_Init
000000b4 g     F .text	00000014 .hidden __mulsi3
000005ee g     F .text	00000018 Display_Control_Toggle
20000000 g       .dalign	00000000 _data_vma
00000960 g     F .text	00000022 PWM_Config_Init
000011a8 g     F .text	0000000c Touch_Button_Get_Event
00001542 g     F .text	00000022 GPIO_EXTILineConfig
000005d4 g     F .text	0000001a Display_Control_Turn_Off
2000006c g     O .bss	00000004 last_adc_read_time
00000606 g     F .text	0000000c Display_Control_Is_On
00001494 g     F .text	0000001e EXTI_GetITStatus
0000014a g     F .text	000000a8 memset
000015cc g     F .text	00000012 RCC_AdjustHSICalibrationValue
00000790 g     F .text	000000d4 main
20000058 g     O .bss	00000014 adc_data
0000072e g     F .text	00000062 System_Init
00001200  w      .text	00000000 DMA1_Channel5_IRQHandler
000000cc g     F .text	00000058 .hidden __divsi3
0000192e g     F .text	0000003a Delay_Ms
00000d6c g     F .text	000000aa tft_print_number
000002fa g     F .text	0000005a ADC_Read_All_Channels
00000aba g     F .text	00000190 tft_init
000000a0 g       .highcodelalign	00000000 _highcode_lma
00000ea4 g     F .text	00000134 SystemInit
000009f2 g     F .text	00000006 PWM_Get_Brightness
00001a0c  w    F .text	00000048 printchar
00000000 g       .init	00000000 _sinit
00000e16 g     F .text	0000008e tft_fill_rect
00001200  w      .text	00000000 DMA1_Channel3_IRQHandler
000009d4 g     F .text	00000010 PWM_Turn_On
00001200  w      .text	00000000 TIM1_UP_IRQHandler
20000070 g     O .bss	00000001 system_initialized
00001200  w      .text	00000000 WWDG_IRQHandler
00001418 g     F .text	00000008 ADC_ClearFlag
00000864 g     F .text	00000036 PWM_GPIO_Config
0000059a g     F .text	00000024 TIM2_IRQHandler
20000800 g       .stack	00000000 _eusrstack
000000a0 g     F .text	0000000a .hidden __riscv_save_2
00001200  w      .text	00000000 SW_Handler
20000074 g     O .bss	00000006 pwm_control
00001200  w      .text	00000000 TIM1_BRK_IRQHandler
000012b2 g     F .text	0000004e ADC_Init
000018e8 g     F .text	00000008 USART_SendData
000019c0 g     F .text	0000004c _write
20000040 g       .data	00000000 _edata
200001ec g       .bss	00000000 _end
20000000 g       .highcode	00000000 _highcode_vma_end
000016bc g     F .text	0000004e TIM_TimeBaseInit
0000282c g       .dlalign	00000000 _data_lma
00000fd8 g     F .text	0000006c SystemCoreClockUpdate
000011ba g     F .text	00000046 Touch_Button_IRQ_Handler
00000124 g     F .text	00000024 .hidden __modsi3
00001200  w      .text	00000000 DMA1_Channel2_IRQHandler
000011b4 g     F .text	00000006 Touch_Button_Get_Time_Ms
00001202  w      .text	00000000 handle_reset
00001200  w      .text	00000000 FLASH_IRQHandler
000000a0 g     F .text	0000000a .hidden __riscv_save_0
00001200  w      .text	00000000 USART1_IRQHandler
00001074 g     F .text	00000056 Touch_Button_Timer_Init
00000c58 g     F .text	0000000a tft_set_color
00000934 g     F .text	0000002c PWM_Set_Brightness
00001200  w      .text	00000000 I2C1_ER_IRQHandler
000004f4 g     F .text	0000001c ADC_Display_Draw_All_Channels
00001564 g     F .text	00000006 NVIC_PriorityGroupConfig
00000c68 g     F .text	000000e4 tft_print_char
00000982 g     F .text	00000052 PWM_Update_Fade
0000062a g     F .text	00000074 Display_Control_Show_Startup_Message
000000aa g     F .text	0000000a .hidden __riscv_restore_1
000017dc g     F .text	00000004 TIM_SetCompare1
00000510 g     F .text	0000001a ADC_Display_Clear_Values_Area
00001538 g     F .text	0000000a GPIO_ReadInputDataBit
000003fe g     F .text	000000f6 ADC_Display_Draw_Channel_Data
200001d0 g     O .bss	00000014 touch_button



Disassembly of section .init:

00000000 <_sinit>:
   0:	2020106f          	j	1202 <handle_reset>
   4:	0000                	unimp
   6:	0000                	unimp
   8:	0580                	addi	s0,sp,704
   a:	0000                	unimp
   c:	0582                	c.slli64	a1
	...
  2e:	0000                	unimp
  30:	1200                	addi	s0,sp,288
  32:	0000                	unimp
  34:	0000                	unimp
  36:	0000                	unimp
  38:	1200                	addi	s0,sp,288
  3a:	0000                	unimp
  3c:	0000                	unimp
  3e:	0000                	unimp
  40:	1200                	addi	s0,sp,288
  42:	0000                	unimp
  44:	1200                	addi	s0,sp,288
  46:	0000                	unimp
  48:	1200                	addi	s0,sp,288
  4a:	0000                	unimp
  4c:	1200                	addi	s0,sp,288
  4e:	0000                	unimp
  50:	0592                	slli	a1,a1,0x4
  52:	0000                	unimp
  54:	1200                	addi	s0,sp,288
  56:	0000                	unimp
  58:	1200                	addi	s0,sp,288
  5a:	0000                	unimp
  5c:	1200                	addi	s0,sp,288
  5e:	0000                	unimp
  60:	1200                	addi	s0,sp,288
  62:	0000                	unimp
  64:	1200                	addi	s0,sp,288
  66:	0000                	unimp
  68:	1200                	addi	s0,sp,288
  6a:	0000                	unimp
  6c:	1200                	addi	s0,sp,288
  6e:	0000                	unimp
  70:	1200                	addi	s0,sp,288
  72:	0000                	unimp
  74:	1200                	addi	s0,sp,288
  76:	0000                	unimp
  78:	1200                	addi	s0,sp,288
  7a:	0000                	unimp
  7c:	1200                	addi	s0,sp,288
  7e:	0000                	unimp
  80:	1200                	addi	s0,sp,288
  82:	0000                	unimp
  84:	1200                	addi	s0,sp,288
  86:	0000                	unimp
  88:	1200                	addi	s0,sp,288
  8a:	0000                	unimp
  8c:	1200                	addi	s0,sp,288
  8e:	0000                	unimp
  90:	1200                	addi	s0,sp,288
  92:	0000                	unimp
  94:	1200                	addi	s0,sp,288
  96:	0000                	unimp
  98:	059a                	slli	a1,a1,0x6
  9a:	0000                	unimp
  9c:	0000                	unimp
	...

Disassembly of section .text:

000000a0 <__riscv_save_0>:
      a0:	1151                	addi	sp,sp,-12
      a2:	c026                	sw	s1,0(sp)
      a4:	c222                	sw	s0,4(sp)
      a6:	c406                	sw	ra,8(sp)
      a8:	8282                	jr	t0

000000aa <__riscv_restore_0>:
      aa:	4482                	lw	s1,0(sp)
      ac:	4412                	lw	s0,4(sp)
      ae:	40a2                	lw	ra,8(sp)
      b0:	0131                	addi	sp,sp,12
      b2:	8082                	ret

000000b4 <__mulsi3>:
      b4:	862a                	mv	a2,a0
      b6:	4501                	li	a0,0
      b8:	0015f693          	andi	a3,a1,1
      bc:	c291                	beqz	a3,c0 <__mulsi3+0xc>
      be:	9532                	add	a0,a0,a2
      c0:	8185                	srli	a1,a1,0x1
      c2:	0606                	slli	a2,a2,0x1
      c4:	f9f5                	bnez	a1,b8 <__mulsi3+0x4>
      c6:	8082                	ret
      c8:	0000                	unimp
	...

000000cc <__divsi3>:
      cc:	02054e63          	bltz	a0,108 <__umodsi3+0x8>
      d0:	0405c363          	bltz	a1,116 <__umodsi3+0x16>

000000d4 <__udivsi3>:
      d4:	862e                	mv	a2,a1
      d6:	85aa                	mv	a1,a0
      d8:	557d                	li	a0,-1
      da:	c215                	beqz	a2,fe <__udivsi3+0x2a>
      dc:	4685                	li	a3,1
      de:	00b67863          	bgeu	a2,a1,ee <__udivsi3+0x1a>
      e2:	00c05663          	blez	a2,ee <__udivsi3+0x1a>
      e6:	0606                	slli	a2,a2,0x1
      e8:	0686                	slli	a3,a3,0x1
      ea:	feb66ce3          	bltu	a2,a1,e2 <__udivsi3+0xe>
      ee:	4501                	li	a0,0
      f0:	00c5e463          	bltu	a1,a2,f8 <__udivsi3+0x24>
      f4:	8d91                	sub	a1,a1,a2
      f6:	8d55                	or	a0,a0,a3
      f8:	8285                	srli	a3,a3,0x1
      fa:	8205                	srli	a2,a2,0x1
      fc:	faf5                	bnez	a3,f0 <__udivsi3+0x1c>
      fe:	8082                	ret

00000100 <__umodsi3>:
     100:	8286                	mv	t0,ra
     102:	3fc9                	jal	d4 <__udivsi3>
     104:	852e                	mv	a0,a1
     106:	8282                	jr	t0
     108:	40a00533          	neg	a0,a0
     10c:	0005d763          	bgez	a1,11a <__umodsi3+0x1a>
     110:	40b005b3          	neg	a1,a1
     114:	b7c1                	j	d4 <__udivsi3>
     116:	40b005b3          	neg	a1,a1
     11a:	8286                	mv	t0,ra
     11c:	3f65                	jal	d4 <__udivsi3>
     11e:	40a00533          	neg	a0,a0
     122:	8282                	jr	t0

00000124 <__modsi3>:
     124:	8286                	mv	t0,ra
     126:	0005c763          	bltz	a1,134 <__modsi3+0x10>
     12a:	00054963          	bltz	a0,13c <__modsi3+0x18>
     12e:	375d                	jal	d4 <__udivsi3>
     130:	852e                	mv	a0,a1
     132:	8282                	jr	t0
     134:	40b005b3          	neg	a1,a1
     138:	fe055be3          	bgez	a0,12e <__modsi3+0xa>
     13c:	40a00533          	neg	a0,a0
     140:	3f51                	jal	d4 <__udivsi3>
     142:	40b00533          	neg	a0,a1
     146:	8282                	jr	t0
	...

0000014a <memset>:
     14a:	433d                	li	t1,15
     14c:	872a                	mv	a4,a0
     14e:	02c37363          	bgeu	t1,a2,174 <memset+0x2a>
     152:	00f77793          	andi	a5,a4,15
     156:	efbd                	bnez	a5,1d4 <memset+0x8a>
     158:	e5ad                	bnez	a1,1c2 <memset+0x78>
     15a:	ff067693          	andi	a3,a2,-16
     15e:	8a3d                	andi	a2,a2,15
     160:	96ba                	add	a3,a3,a4
     162:	c30c                	sw	a1,0(a4)
     164:	c34c                	sw	a1,4(a4)
     166:	c70c                	sw	a1,8(a4)
     168:	c74c                	sw	a1,12(a4)
     16a:	0741                	addi	a4,a4,16
     16c:	fed76be3          	bltu	a4,a3,162 <memset+0x18>
     170:	e211                	bnez	a2,174 <memset+0x2a>
     172:	8082                	ret
     174:	40c306b3          	sub	a3,t1,a2
     178:	068a                	slli	a3,a3,0x2
     17a:	00000297          	auipc	t0,0x0
     17e:	9696                	add	a3,a3,t0
     180:	00a68067          	jr	10(a3)
     184:	00b70723          	sb	a1,14(a4)
     188:	00b706a3          	sb	a1,13(a4)
     18c:	00b70623          	sb	a1,12(a4)
     190:	00b705a3          	sb	a1,11(a4)
     194:	00b70523          	sb	a1,10(a4)
     198:	00b704a3          	sb	a1,9(a4)
     19c:	00b70423          	sb	a1,8(a4)
     1a0:	00b703a3          	sb	a1,7(a4)
     1a4:	00b70323          	sb	a1,6(a4)
     1a8:	00b702a3          	sb	a1,5(a4)
     1ac:	00b70223          	sb	a1,4(a4)
     1b0:	00b701a3          	sb	a1,3(a4)
     1b4:	00b70123          	sb	a1,2(a4)
     1b8:	00b700a3          	sb	a1,1(a4)
     1bc:	00b70023          	sb	a1,0(a4)
     1c0:	8082                	ret
     1c2:	0ff5f593          	andi	a1,a1,255
     1c6:	00859693          	slli	a3,a1,0x8
     1ca:	8dd5                	or	a1,a1,a3
     1cc:	01059693          	slli	a3,a1,0x10
     1d0:	8dd5                	or	a1,a1,a3
     1d2:	b761                	j	15a <memset+0x10>
     1d4:	00279693          	slli	a3,a5,0x2
     1d8:	00000297          	auipc	t0,0x0
     1dc:	9696                	add	a3,a3,t0
     1de:	8286                	mv	t0,ra
     1e0:	fa8680e7          	jalr	-88(a3)
     1e4:	8096                	mv	ra,t0
     1e6:	17c1                	addi	a5,a5,-16
     1e8:	8f1d                	sub	a4,a4,a5
     1ea:	963e                	add	a2,a2,a5
     1ec:	f8c374e3          	bgeu	t1,a2,174 <memset+0x2a>
     1f0:	b7a5                	j	158 <memset+0xe>

000001f2 <ADC_GPIO_Config>:
     1f2:	eafff2ef          	jal	t0,a0 <__riscv_save_0>
     1f6:	1151                	addi	sp,sp,-12
     1f8:	40011537          	lui	a0,0x40011
     1fc:	09800793          	li	a5,152
     200:	858a                	mv	a1,sp
     202:	40050513          	addi	a0,a0,1024 # 40011400 <__global_pointer$+0x20010bc0>
     206:	807c                	sh	a5,0(sp)
     208:	00011123          	sh	zero,2(sp)
     20c:	00011223          	sh	zero,4(sp)
     210:	00011323          	sh	zero,6(sp)
     214:	00011423          	sh	zero,8(sp)
     218:	00011523          	sh	zero,10(sp)
     21c:	2a0010ef          	jal	ra,14bc <GPIO_Init>
     220:	47c1                	li	a5,16
     222:	858a                	mv	a1,sp
     224:	40011537          	lui	a0,0x40011
     228:	807c                	sh	a5,0(sp)
     22a:	c402                	sw	zero,8(sp)
     22c:	290010ef          	jal	ra,14bc <GPIO_Init>
     230:	0131                	addi	sp,sp,12
     232:	bda5                	j	aa <__riscv_restore_0>

00000234 <ADC_Config_Init>:
     234:	e6dff2ef          	jal	t0,a0 <__riscv_save_0>
     238:	1121                	addi	sp,sp,-24
     23a:	4661                	li	a2,24
     23c:	4581                	li	a1,0
     23e:	850a                	mv	a0,sp
     240:	3729                	jal	14a <memset>
     242:	4585                	li	a1,1
     244:	23000513          	li	a0,560
     248:	438010ef          	jal	ra,1680 <RCC_APB2PeriphClockCmd>
     24c:	375d                	jal	1f2 <ADC_GPIO_Config>
     24e:	40012537          	lui	a0,0x40012
     252:	40050513          	addi	a0,a0,1024 # 40012400 <__global_pointer$+0x20011bc0>
     256:	40012437          	lui	s0,0x40012
     25a:	0b6010ef          	jal	ra,1310 <ADC_ResetCalibration>
     25e:	40040493          	addi	s1,s0,1024 # 40012400 <__global_pointer$+0x20011bc0>
     262:	8526                	mv	a0,s1
     264:	0b6010ef          	jal	ra,131a <ADC_GetResetCalibrationStatus>
     268:	fd6d                	bnez	a0,262 <ADC_Config_Init+0x2e>
     26a:	40040513          	addi	a0,s0,1024
     26e:	40012437          	lui	s0,0x40012
     272:	0b0010ef          	jal	ra,1322 <ADC_StartCalibration>
     276:	40040493          	addi	s1,s0,1024 # 40012400 <__global_pointer$+0x20011bc0>
     27a:	8526                	mv	a0,s1
     27c:	0b0010ef          	jal	ra,132c <ADC_GetCalibrationStatus>
     280:	fd6d                	bnez	a0,27a <ADC_Config_Init+0x46>
     282:	000e07b7          	lui	a5,0xe0
     286:	c63e                	sw	a5,12(sp)
     288:	858a                	mv	a1,sp
     28a:	4785                	li	a5,1
     28c:	40040513          	addi	a0,s0,1024
     290:	00f10a23          	sb	a5,20(sp)
     294:	c002                	sw	zero,0(sp)
     296:	c202                	sw	zero,4(sp)
     298:	c402                	sw	zero,8(sp)
     29a:	c802                	sw	zero,16(sp)
     29c:	016010ef          	jal	ra,12b2 <ADC_Init>
     2a0:	4585                	li	a1,1
     2a2:	40040513          	addi	a0,s0,1024
     2a6:	05a010ef          	jal	ra,1300 <ADC_Cmd>
     2aa:	4529                	li	a0,10
     2ac:	682010ef          	jal	ra,192e <Delay_Ms>
     2b0:	0161                	addi	sp,sp,24
     2b2:	bbe5                	j	aa <__riscv_restore_0>

000002b4 <ADC_Read_Channel>:
     2b4:	dedff2ef          	jal	t0,a0 <__riscv_save_0>
     2b8:	40012437          	lui	s0,0x40012
     2bc:	85aa                	mv	a1,a0
     2be:	469d                	li	a3,7
     2c0:	4605                	li	a2,1
     2c2:	40040513          	addi	a0,s0,1024 # 40012400 <__global_pointer$+0x20011bc0>
     2c6:	086010ef          	jal	ra,134c <ADC_RegularChannelConfig>
     2ca:	4585                	li	a1,1
     2cc:	40040513          	addi	a0,s0,1024
     2d0:	064010ef          	jal	ra,1334 <ADC_SoftwareStartConvCmd>
     2d4:	40040493          	addi	s1,s0,1024
     2d8:	4589                	li	a1,2
     2da:	8526                	mv	a0,s1
     2dc:	132010ef          	jal	ra,140e <ADC_GetFlagStatus>
     2e0:	dd65                	beqz	a0,2d8 <ADC_Read_Channel+0x24>
     2e2:	40040513          	addi	a0,s0,1024
     2e6:	120010ef          	jal	ra,1406 <ADC_GetConversionValue>
     2ea:	84aa                	mv	s1,a0
     2ec:	4589                	li	a1,2
     2ee:	40040513          	addi	a0,s0,1024
     2f2:	126010ef          	jal	ra,1418 <ADC_ClearFlag>
     2f6:	8526                	mv	a0,s1
     2f8:	bb4d                	j	aa <__riscv_restore_0>

000002fa <ADC_Read_All_Channels>:
     2fa:	cd21                	beqz	a0,352 <ADC_Read_All_Channels+0x58>
     2fc:	da5ff2ef          	jal	t0,a0 <__riscv_save_0>
     300:	842a                	mv	s0,a0
     302:	1161                	addi	sp,sp,-8
     304:	4511                	li	a0,4
     306:	377d                	jal	2b4 <ADC_Read_Channel>
     308:	a00a                	sh	a0,0(s0)
     30a:	450d                	li	a0,3
     30c:	3765                	jal	2b4 <ADC_Read_Channel>
     30e:	a02a                	sh	a0,2(s0)
     310:	451d                	li	a0,7
     312:	374d                	jal	2b4 <ADC_Read_Channel>
     314:	a04a                	sh	a0,4(s0)
     316:	4509                	li	a0,2
     318:	3f71                	jal	2b4 <ADC_Read_Channel>
     31a:	6785                	lui	a5,0x1
     31c:	a06a                	sh	a0,6(s0)
     31e:	01040493          	addi	s1,s0,16
     322:	00840713          	addi	a4,s0,8
     326:	ce478793          	addi	a5,a5,-796 # ce4 <tft_print_char+0x7c>
     32a:	4685                	li	a3,1
     32c:	200a                	lhu	a0,0(s0)
     32e:	85be                	mv	a1,a5
     330:	c236                	sw	a3,4(sp)
     332:	c03a                	sw	a4,0(sp)
     334:	3341                	jal	b4 <__mulsi3>
     336:	4692                	lw	a3,4(sp)
     338:	8131                	srli	a0,a0,0xc
     33a:	4702                	lw	a4,0(sp)
     33c:	a40a                	sh	a0,8(s0)
     33e:	a094                	sb	a3,0(s1)
     340:	6785                	lui	a5,0x1
     342:	0409                	addi	s0,s0,2
     344:	0485                	addi	s1,s1,1
     346:	ce478793          	addi	a5,a5,-796 # ce4 <tft_print_char+0x7c>
     34a:	fee411e3          	bne	s0,a4,32c <ADC_Read_All_Channels+0x32>
     34e:	0121                	addi	sp,sp,8
     350:	bba9                	j	aa <__riscv_restore_0>
     352:	8082                	ret

00000354 <ADC_Display_Draw_Header>:
     354:	d4dff2ef          	jal	t0,a0 <__riscv_save_0>
     358:	6541                	lui	a0,0x10
     35a:	157d                	addi	a0,a0,-1
     35c:	0fd000ef          	jal	ra,c58 <tft_set_color>
     360:	4501                	li	a0,0
     362:	101000ef          	jal	ra,c62 <tft_set_background_color>
     366:	4581                	li	a1,0
     368:	4515                	li	a0,5
     36a:	0e1000ef          	jal	ra,c4a <tft_set_cursor>
     36e:	00002537          	lui	a0,0x2
     372:	16050513          	addi	a0,a0,352 # 2160 <memcpy+0x10e>
     376:	1d7000ef          	jal	ra,d4c <tft_print>
     37a:	bb05                	j	aa <__riscv_restore_0>

0000037c <ADC_Display_Draw_Channel_Labels>:
     37c:	d25ff2ef          	jal	t0,a0 <__riscv_save_0>
     380:	1171                	addi	sp,sp,-4
     382:	4501                	li	a0,0
     384:	0df000ef          	jal	ra,c62 <tft_set_background_color>
     388:	6789                	lui	a5,0x2
     38a:	16c78793          	addi	a5,a5,364 # 216c <CSWTCH.2>
     38e:	4451                	li	s0,20
     390:	4485                	li	s1,1
     392:	238a                	lhu	a0,0(a5)
     394:	c03e                	sw	a5,0(sp)
     396:	0c3000ef          	jal	ra,c58 <tft_set_color>
     39a:	85a2                	mv	a1,s0
     39c:	4515                	li	a0,5
     39e:	0ad000ef          	jal	ra,c4a <tft_set_cursor>
     3a2:	000027b7          	lui	a5,0x2
     3a6:	15878513          	addi	a0,a5,344 # 2158 <memcpy+0x106>
     3aa:	1a3000ef          	jal	ra,d4c <tft_print>
     3ae:	8526                	mv	a0,s1
     3b0:	4585                	li	a1,1
     3b2:	1bb000ef          	jal	ra,d6c <tft_print_number>
     3b6:	00002537          	lui	a0,0x2
     3ba:	15c50513          	addi	a0,a0,348 # 215c <memcpy+0x10a>
     3be:	18f000ef          	jal	ra,d4c <tft_print>
     3c2:	4782                	lw	a5,0(sp)
     3c4:	0449                	addi	s0,s0,18
     3c6:	0442                	slli	s0,s0,0x10
     3c8:	0485                	addi	s1,s1,1
     3ca:	4695                	li	a3,5
     3cc:	0789                	addi	a5,a5,2
     3ce:	8041                	srli	s0,s0,0x10
     3d0:	fcd491e3          	bne	s1,a3,392 <ADC_Display_Draw_Channel_Labels+0x16>
     3d4:	0111                	addi	sp,sp,4
     3d6:	b9d1                	j	aa <__riscv_restore_0>

000003d8 <ADC_Display_Init>:
     3d8:	cc9ff2ef          	jal	t0,a0 <__riscv_save_0>
     3dc:	200007b7          	lui	a5,0x20000
     3e0:	02010737          	lui	a4,0x2010
     3e4:	04078793          	addi	a5,a5,64 # 20000040 <_edata>
     3e8:	10070713          	addi	a4,a4,256 # 2010100 <_data_lma+0x200d8d4>
     3ec:	c398                	sw	a4,0(a5)
     3ee:	06400713          	li	a4,100
     3f2:	a3da                	sh	a4,4(a5)
     3f4:	0007a423          	sw	zero,8(a5)
     3f8:	3fb1                	jal	354 <ADC_Display_Draw_Header>
     3fa:	3749                	jal	37c <ADC_Display_Draw_Channel_Labels>
     3fc:	b17d                	j	aa <__riscv_restore_0>

000003fe <ADC_Display_Draw_Channel_Data>:
     3fe:	478d                	li	a5,3
     400:	0ea7e963          	bltu	a5,a0,4f2 <ADC_Display_Draw_Channel_Data+0xf4>
     404:	0e058763          	beqz	a1,4f2 <ADC_Display_Draw_Channel_Data+0xf4>
     408:	c99ff2ef          	jal	t0,a0 <__riscv_save_0>
     40c:	00351493          	slli	s1,a0,0x3
     410:	94aa                	add	s1,s1,a0
     412:	842a                	mv	s0,a0
     414:	6531                	lui	a0,0xc
     416:	1101                	addi	sp,sp,-32
     418:	61850513          	addi	a0,a0,1560 # c618 <_data_lma+0x9dec>
     41c:	c02e                	sw	a1,0(sp)
     41e:	03b000ef          	jal	ra,c58 <tft_set_color>
     422:	4501                	li	a0,0
     424:	03f000ef          	jal	ra,c62 <tft_set_background_color>
     428:	200007b7          	lui	a5,0x20000
     42c:	04078713          	addi	a4,a5,64 # 20000040 <_edata>
     430:	3318                	lbu	a4,1(a4)
     432:	04078793          	addi	a5,a5,64
     436:	0486                	slli	s1,s1,0x1
     438:	c23e                	sw	a5,4(sp)
     43a:	04d1                	addi	s1,s1,20
     43c:	cf21                	beqz	a4,494 <ADC_Display_Draw_Channel_Data+0x96>
     43e:	85a6                	mv	a1,s1
     440:	03c00513          	li	a0,60
     444:	007000ef          	jal	ra,c4a <tft_set_cursor>
     448:	4702                	lw	a4,0(sp)
     44a:	00141793          	slli	a5,s0,0x1
     44e:	3e800593          	li	a1,1000
     452:	97ba                	add	a5,a5,a4
     454:	279e                	lhu	a5,8(a5)
     456:	853e                	mv	a0,a5
     458:	c63e                	sw	a5,12(sp)
     45a:	315d                	jal	100 <__umodsi3>
     45c:	0542                	slli	a0,a0,0x10
     45e:	45a9                	li	a1,10
     460:	8141                	srli	a0,a0,0x10
     462:	398d                	jal	d4 <__udivsi3>
     464:	47b2                	lw	a5,12(sp)
     466:	01051713          	slli	a4,a0,0x10
     46a:	8341                	srli	a4,a4,0x10
     46c:	3e800593          	li	a1,1000
     470:	853e                	mv	a0,a5
     472:	c43a                	sw	a4,8(sp)
     474:	3185                	jal	d4 <__udivsi3>
     476:	4722                	lw	a4,8(sp)
     478:	01051693          	slli	a3,a0,0x10
     47c:	00002637          	lui	a2,0x2
     480:	82c1                	srli	a3,a3,0x10
     482:	14460613          	addi	a2,a2,324 # 2144 <memcpy+0xf2>
     486:	45c1                	li	a1,16
     488:	0808                	addi	a0,sp,16
     48a:	355010ef          	jal	ra,1fde <snprintf>
     48e:	0808                	addi	a0,sp,16
     490:	0bd000ef          	jal	ra,d4c <tft_print>
     494:	4792                	lw	a5,4(sp)
     496:	23bc                	lbu	a5,2(a5)
     498:	cf85                	beqz	a5,4d0 <ADC_Display_Draw_Channel_Data+0xd2>
     49a:	85a6                	mv	a1,s1
     49c:	06e00513          	li	a0,110
     4a0:	7aa000ef          	jal	ra,c4a <tft_set_cursor>
     4a4:	4702                	lw	a4,0(sp)
     4a6:	00141793          	slli	a5,s0,0x1
     4aa:	00002637          	lui	a2,0x2
     4ae:	97ba                	add	a5,a5,a4
     4b0:	239e                	lhu	a5,0(a5)
     4b2:	15060613          	addi	a2,a2,336 # 2150 <memcpy+0xfe>
     4b6:	45c1                	li	a1,16
     4b8:	00179693          	slli	a3,a5,0x1
     4bc:	96be                	add	a3,a3,a5
     4be:	068e                	slli	a3,a3,0x3
     4c0:	96be                	add	a3,a3,a5
     4c2:	82a9                	srli	a3,a3,0xa
     4c4:	0808                	addi	a0,sp,16
     4c6:	319010ef          	jal	ra,1fde <snprintf>
     4ca:	0808                	addi	a0,sp,16
     4cc:	081000ef          	jal	ra,d4c <tft_print>
     4d0:	4792                	lw	a5,4(sp)
     4d2:	239c                	lbu	a5,0(a5)
     4d4:	cf89                	beqz	a5,4ee <ADC_Display_Draw_Channel_Data+0xf0>
     4d6:	85a6                	mv	a1,s1
     4d8:	08c00513          	li	a0,140
     4dc:	76e000ef          	jal	ra,c4a <tft_set_cursor>
     4e0:	4782                	lw	a5,0(sp)
     4e2:	0406                	slli	s0,s0,0x1
     4e4:	4591                	li	a1,4
     4e6:	943e                	add	s0,s0,a5
     4e8:	200a                	lhu	a0,0(s0)
     4ea:	083000ef          	jal	ra,d6c <tft_print_number>
     4ee:	6105                	addi	sp,sp,32
     4f0:	be6d                	j	aa <__riscv_restore_0>
     4f2:	8082                	ret

000004f4 <ADC_Display_Draw_All_Channels>:
     4f4:	badff2ef          	jal	t0,a0 <__riscv_save_0>
     4f8:	84aa                	mv	s1,a0
     4fa:	4401                	li	s0,0
     4fc:	8522                	mv	a0,s0
     4fe:	85a6                	mv	a1,s1
     500:	3dfd                	jal	3fe <ADC_Display_Draw_Channel_Data>
     502:	0405                	addi	s0,s0,1
     504:	0ff47413          	andi	s0,s0,255
     508:	4791                	li	a5,4
     50a:	fef419e3          	bne	s0,a5,4fc <ADC_Display_Draw_All_Channels+0x8>
     50e:	be71                	j	aa <__riscv_restore_0>

00000510 <ADC_Display_Clear_Values_Area>:
     510:	b91ff2ef          	jal	t0,a0 <__riscv_save_0>
     514:	4701                	li	a4,0
     516:	04800693          	li	a3,72
     51a:	06900613          	li	a2,105
     51e:	45d1                	li	a1,20
     520:	03700513          	li	a0,55
     524:	0f3000ef          	jal	ra,e16 <tft_fill_rect>
     528:	b649                	j	aa <__riscv_restore_0>

0000052a <ADC_Display_Should_Update>:
     52a:	b77ff2ef          	jal	t0,a0 <__riscv_save_0>
     52e:	487000ef          	jal	ra,11b4 <Touch_Button_Get_Time_Ms>
     532:	20000737          	lui	a4,0x20000
     536:	04070713          	addi	a4,a4,64 # 20000040 <_edata>
     53a:	471c                	lw	a5,8(a4)
     53c:	8d1d                	sub	a0,a0,a5
     53e:	235e                	lhu	a5,4(a4)
     540:	00f53533          	sltu	a0,a0,a5
     544:	00154513          	xori	a0,a0,1
     548:	b68d                	j	aa <__riscv_restore_0>

0000054a <ADC_Display_Update>:
     54a:	c915                	beqz	a0,57e <ADC_Display_Update+0x34>
     54c:	b55ff2ef          	jal	t0,a0 <__riscv_save_0>
     550:	84ae                	mv	s1,a1
     552:	842a                	mv	s0,a0
     554:	3fd9                	jal	52a <ADC_Display_Should_Update>
     556:	cd01                	beqz	a0,56e <ADC_Display_Update+0x24>
     558:	4785                	li	a5,1
     55a:	02f48063          	beq	s1,a5,57a <ADC_Display_Update+0x30>
     55e:	c889                	beqz	s1,570 <ADC_Display_Update+0x26>
     560:	4789                	li	a5,2
     562:	00f48963          	beq	s1,a5,574 <ADC_Display_Update+0x2a>
     566:	44f000ef          	jal	ra,11b4 <Touch_Button_Get_Time_Ms>
     56a:	80a1a423          	sw	a0,-2040(gp) # 20000048 <_edata+0x8>
     56e:	be35                	j	aa <__riscv_restore_0>
     570:	33d5                	jal	354 <ADC_Display_Draw_Header>
     572:	3529                	jal	37c <ADC_Display_Draw_Channel_Labels>
     574:	8522                	mv	a0,s0
     576:	3fbd                	jal	4f4 <ADC_Display_Draw_All_Channels>
     578:	b7fd                	j	566 <ADC_Display_Update+0x1c>
     57a:	3f59                	jal	510 <ADC_Display_Clear_Values_Area>
     57c:	bfe5                	j	574 <ADC_Display_Update+0x2a>
     57e:	8082                	ret

00000580 <NMI_Handler>:
     580:	a001                	j	580 <NMI_Handler>

00000582 <HardFault_Handler>:
     582:	beef07b7          	lui	a5,0xbeef0
     586:	e000e737          	lui	a4,0xe000e
     58a:	08078793          	addi	a5,a5,128 # beef0080 <__global_pointer$+0x9eeef840>
     58e:	c73c                	sw	a5,72(a4)
     590:	a001                	j	590 <HardFault_Handler+0xe>

00000592 <EXTI7_0_IRQHandler>:
     592:	429000ef          	jal	ra,11ba <Touch_Button_IRQ_Handler>
     596:	30200073          	mret

0000059a <TIM2_IRQHandler>:
     59a:	4585                	li	a1,1
     59c:	40000537          	lui	a0,0x40000
     5a0:	240010ef          	jal	ra,17e0 <TIM_GetITStatus>
     5a4:	c919                	beqz	a0,5ba <TIM2_IRQHandler+0x20>
     5a6:	98c1a783          	lw	a5,-1652(gp) # 200001cc <system_tick_ms>
     5aa:	4585                	li	a1,1
     5ac:	40000537          	lui	a0,0x40000
     5b0:	0785                	addi	a5,a5,1
     5b2:	98f1a623          	sw	a5,-1652(gp) # 200001cc <system_tick_ms>
     5b6:	242010ef          	jal	ra,17f8 <TIM_ClearITPendingBit>
     5ba:	30200073          	mret

000005be <Display_Control_Turn_On>:
     5be:	80c18793          	addi	a5,gp,-2036 # 2000004c <display_control>
     5c2:	4398                	lw	a4,0(a5)
     5c4:	e719                	bnez	a4,5d2 <Display_Control_Turn_On+0x14>
     5c6:	adbff2ef          	jal	t0,a0 <__riscv_save_0>
     5ca:	4705                	li	a4,1
     5cc:	c398                	sw	a4,0(a5)
     5ce:	2119                	jal	9d4 <PWM_Turn_On>
     5d0:	bce9                	j	aa <__riscv_restore_0>
     5d2:	8082                	ret

000005d4 <Display_Control_Turn_Off>:
     5d4:	80c18793          	addi	a5,gp,-2036 # 2000004c <display_control>
     5d8:	4394                	lw	a3,0(a5)
     5da:	4709                	li	a4,2
     5dc:	00e69863          	bne	a3,a4,5ec <Display_Control_Turn_Off+0x18>
     5e0:	ac1ff2ef          	jal	t0,a0 <__riscv_save_0>
     5e4:	470d                	li	a4,3
     5e6:	c398                	sw	a4,0(a5)
     5e8:	2ef5                	jal	9e4 <PWM_Turn_Off>
     5ea:	b4c1                	j	aa <__riscv_restore_0>
     5ec:	8082                	ret

000005ee <Display_Control_Toggle>:
     5ee:	ab3ff2ef          	jal	t0,a0 <__riscv_save_0>
     5f2:	80c1a783          	lw	a5,-2036(gp) # 2000004c <display_control>
     5f6:	e399                	bnez	a5,5fc <Display_Control_Toggle+0xe>
     5f8:	37d9                	jal	5be <Display_Control_Turn_On>
     5fa:	bc45                	j	aa <__riscv_restore_0>
     5fc:	4709                	li	a4,2
     5fe:	fee79ee3          	bne	a5,a4,5fa <Display_Control_Toggle+0xc>
     602:	3fc9                	jal	5d4 <Display_Control_Turn_Off>
     604:	bfdd                	j	5fa <Display_Control_Toggle+0xc>

00000606 <Display_Control_Is_On>:
     606:	80c1a503          	lw	a0,-2036(gp) # 2000004c <display_control>
     60a:	157d                	addi	a0,a0,-1
     60c:	00253513          	sltiu	a0,a0,2
     610:	8082                	ret

00000612 <Display_Control_Clear_Screen>:
     612:	a8fff2ef          	jal	t0,a0 <__riscv_save_0>
     616:	4701                	li	a4,0
     618:	05000693          	li	a3,80
     61c:	0a000613          	li	a2,160
     620:	4581                	li	a1,0
     622:	4501                	li	a0,0
     624:	7f2000ef          	jal	ra,e16 <tft_fill_rect>
     628:	b449                	j	aa <__riscv_restore_0>

0000062a <Display_Control_Show_Startup_Message>:
     62a:	a77ff2ef          	jal	t0,a0 <__riscv_save_0>
     62e:	37d5                	jal	612 <Display_Control_Clear_Screen>
     630:	6441                	lui	s0,0x10
     632:	fff40513          	addi	a0,s0,-1 # ffff <_data_lma+0xd7d3>
     636:	622000ef          	jal	ra,c58 <tft_set_color>
     63a:	4501                	li	a0,0
     63c:	626000ef          	jal	ra,c62 <tft_set_background_color>
     640:	45a9                	li	a1,10
     642:	4529                	li	a0,10
     644:	606000ef          	jal	ra,c4a <tft_set_cursor>
     648:	00002537          	lui	a0,0x2
     64c:	18050513          	addi	a0,a0,384 # 2180 <CSWTCH.2+0x14>
     650:	6fc000ef          	jal	ra,d4c <tft_print>
     654:	45e5                	li	a1,25
     656:	4529                	li	a0,10
     658:	5f2000ef          	jal	ra,c4a <tft_set_cursor>
     65c:	00002537          	lui	a0,0x2
     660:	19050513          	addi	a0,a0,400 # 2190 <CSWTCH.2+0x24>
     664:	6e8000ef          	jal	ra,d4c <tft_print>
     668:	02d00593          	li	a1,45
     66c:	4515                	li	a0,5
     66e:	5dc000ef          	jal	ra,c4a <tft_set_cursor>
     672:	fe040513          	addi	a0,s0,-32
     676:	5e2000ef          	jal	ra,c58 <tft_set_color>
     67a:	00002537          	lui	a0,0x2
     67e:	1a050513          	addi	a0,a0,416 # 21a0 <CSWTCH.2+0x34>
     682:	6ca000ef          	jal	ra,d4c <tft_print>
     686:	03c00593          	li	a1,60
     68a:	4515                	li	a0,5
     68c:	5be000ef          	jal	ra,c4a <tft_set_cursor>
     690:	00002537          	lui	a0,0x2
     694:	1b050513          	addi	a0,a0,432 # 21b0 <CSWTCH.2+0x44>
     698:	6b4000ef          	jal	ra,d4c <tft_print>
     69c:	b439                	j	aa <__riscv_restore_0>

0000069e <Display_Control_Init>:
     69e:	a03ff2ef          	jal	t0,a0 <__riscv_save_0>
     6a2:	80c18413          	addi	s0,gp,-2036 # 2000004c <display_control>
     6a6:	10000793          	li	a5,256
     6aa:	a05e                	sh	a5,4(s0)
     6ac:	00042023          	sw	zero,0(s0)
     6b0:	00042423          	sw	zero,8(s0)
     6b4:	2119                	jal	aba <tft_init>
     6b6:	3fb1                	jal	612 <Display_Control_Clear_Screen>
     6b8:	3f8d                	jal	62a <Display_Control_Show_Startup_Message>
     6ba:	4785                	li	a5,1
     6bc:	a05c                	sb	a5,4(s0)
     6be:	261d                	jal	9e4 <PWM_Turn_Off>
     6c0:	9ebff06f          	j	aa <__riscv_restore_0>

000006c4 <Display_Control_Show_Off_Message>:
     6c4:	9ddff2ef          	jal	t0,a0 <__riscv_save_0>
     6c8:	37a9                	jal	612 <Display_Control_Clear_Screen>
     6ca:	6521                	lui	a0,0x8
     6cc:	bef50513          	addi	a0,a0,-1041 # 7bef <_data_lma+0x53c3>
     6d0:	2361                	jal	c58 <tft_set_color>
     6d2:	4501                	li	a0,0
     6d4:	2379                	jal	c62 <tft_set_background_color>
     6d6:	02300593          	li	a1,35
     6da:	4551                	li	a0,20
     6dc:	23bd                	jal	c4a <tft_set_cursor>
     6de:	00002537          	lui	a0,0x2
     6e2:	17450513          	addi	a0,a0,372 # 2174 <CSWTCH.2+0x8>
     6e6:	666000ef          	jal	ra,d4c <tft_print>
     6ea:	9c1ff06f          	j	aa <__riscv_restore_0>

000006ee <Display_Control_Update>:
     6ee:	9b3ff2ef          	jal	t0,a0 <__riscv_save_0>
     6f2:	80c18413          	addi	s0,gp,-2036 # 2000004c <display_control>
     6f6:	205c                	lbu	a5,4(s0)
     6f8:	cb89                	beqz	a5,70a <Display_Control_Update+0x1c>
     6fa:	2461                	jal	982 <PWM_Update_Fade>
     6fc:	401c                	lw	a5,0(s0)
     6fe:	4705                	li	a4,1
     700:	00e78763          	beq	a5,a4,70e <Display_Control_Update+0x20>
     704:	470d                	li	a4,3
     706:	00e78b63          	beq	a5,a4,71c <Display_Control_Update+0x2e>
     70a:	9a1ff06f          	j	aa <__riscv_restore_0>
     70e:	8381c703          	lbu	a4,-1992(gp) # 20000078 <pwm_control+0x4>
     712:	ff65                	bnez	a4,70a <Display_Control_Update+0x1c>
     714:	4709                	li	a4,2
     716:	c018                	sw	a4,0(s0)
     718:	b05c                	sb	a5,5(s0)
     71a:	bfc5                	j	70a <Display_Control_Update+0x1c>
     71c:	8381c783          	lbu	a5,-1992(gp) # 20000078 <pwm_control+0x4>
     720:	f7ed                	bnez	a5,70a <Display_Control_Update+0x1c>
     722:	2cc1                	jal	9f2 <PWM_Get_Brightness>
     724:	f17d                	bnez	a0,70a <Display_Control_Update+0x1c>
     726:	00042023          	sw	zero,0(s0)
     72a:	3f69                	jal	6c4 <Display_Control_Show_Off_Message>
     72c:	bff9                	j	70a <Display_Control_Update+0x1c>

0000072e <System_Init>:
     72e:	973ff2ef          	jal	t0,a0 <__riscv_save_0>
     732:	3609                	jal	234 <ADC_Config_Init>
     734:	00002537          	lui	a0,0x2
     738:	1c050513          	addi	a0,a0,448 # 21c0 <CSWTCH.2+0x54>
     73c:	0cd010ef          	jal	ra,2008 <puts>
     740:	2405                	jal	960 <PWM_Config_Init>
     742:	00002537          	lui	a0,0x2
     746:	1d450513          	addi	a0,a0,468 # 21d4 <CSWTCH.2+0x68>
     74a:	0bf010ef          	jal	ra,2008 <puts>
     74e:	1bf000ef          	jal	ra,110c <Touch_Button_Init>
     752:	00002537          	lui	a0,0x2
     756:	1e850513          	addi	a0,a0,488 # 21e8 <CSWTCH.2+0x7c>
     75a:	0af010ef          	jal	ra,2008 <puts>
     75e:	3781                	jal	69e <Display_Control_Init>
     760:	00002537          	lui	a0,0x2
     764:	20450513          	addi	a0,a0,516 # 2204 <CSWTCH.2+0x98>
     768:	0a1010ef          	jal	ra,2008 <puts>
     76c:	31b5                	jal	3d8 <ADC_Display_Init>
     76e:	00002537          	lui	a0,0x2
     772:	22450513          	addi	a0,a0,548 # 2224 <CSWTCH.2+0xb8>
     776:	093010ef          	jal	ra,2008 <puts>
     77a:	00002537          	lui	a0,0x2
     77e:	4705                	li	a4,1
     780:	24050513          	addi	a0,a0,576 # 2240 <CSWTCH.2+0xd4>
     784:	82e18823          	sb	a4,-2000(gp) # 20000070 <system_initialized>
     788:	081010ef          	jal	ra,2008 <puts>
     78c:	91fff06f          	j	aa <__riscv_restore_0>

00000790 <main>:
     790:	911ff2ef          	jal	t0,a0 <__riscv_save_0>
     794:	1171                	addi	sp,sp,-4
     796:	4505                	li	a0,1
     798:	5cd000ef          	jal	ra,1564 <NVIC_PriorityGroupConfig>
     79c:	03d000ef          	jal	ra,fd8 <SystemCoreClockUpdate>
     7a0:	15a010ef          	jal	ra,18fa <Delay_Init>
     7a4:	6571                	lui	a0,0x1c
     7a6:	20050513          	addi	a0,a0,512 # 1c200 <_data_lma+0x199d4>
     7aa:	1be010ef          	jal	ra,1968 <USART_Printf_Init>
     7ae:	00002537          	lui	a0,0x2
     7b2:	26050513          	addi	a0,a0,608 # 2260 <CSWTCH.2+0xf4>
     7b6:	053010ef          	jal	ra,2008 <puts>
     7ba:	200007b7          	lui	a5,0x20000
     7be:	0147a583          	lw	a1,20(a5) # 20000014 <SystemCoreClock>
     7c2:	00002537          	lui	a0,0x2
     7c6:	28050513          	addi	a0,a0,640 # 2280 <CSWTCH.2+0x114>
     7ca:	7f0010ef          	jal	ra,1fba <printf>
     7ce:	453000ef          	jal	ra,1420 <DBGMCU_GetCHIPID>
     7d2:	85aa                	mv	a1,a0
     7d4:	00002537          	lui	a0,0x2
     7d8:	29450513          	addi	a0,a0,660 # 2294 <CSWTCH.2+0x128>
     7dc:	7de010ef          	jal	ra,1fba <printf>
     7e0:	37b9                	jal	72e <System_Init>
     7e2:	000024b7          	lui	s1,0x2
     7e6:	151000ef          	jal	ra,1136 <Touch_Button_Update>
     7ea:	1bf000ef          	jal	ra,11a8 <Touch_Button_Get_Event>
     7ee:	4789                	li	a5,2
     7f0:	04f50c63          	beq	a0,a5,848 <main+0xb8>
     7f4:	478d                	li	a5,3
     7f6:	04f50f63          	beq	a0,a5,854 <main+0xc4>
     7fa:	4785                	li	a5,1
     7fc:	00f51963          	bne	a0,a5,80e <main+0x7e>
     800:	00002537          	lui	a0,0x2
     804:	2a450513          	addi	a0,a0,676 # 22a4 <CSWTCH.2+0x138>
     808:	001010ef          	jal	ra,2008 <puts>
     80c:	3b4d                	jal	5be <Display_Control_Turn_On>
     80e:	35c5                	jal	6ee <Display_Control_Update>
     810:	1a5000ef          	jal	ra,11b4 <Touch_Button_Get_Time_Ms>
     814:	82c18413          	addi	s0,gp,-2004 # 2000006c <last_adc_read_time>
     818:	401c                	lw	a5,0(s0)
     81a:	c02a                	sw	a0,0(sp)
     81c:	06300693          	li	a3,99
     820:	40f507b3          	sub	a5,a0,a5
     824:	00f6fe63          	bgeu	a3,a5,840 <main+0xb0>
     828:	81818513          	addi	a0,gp,-2024 # 20000058 <adc_data>
     82c:	acfff0ef          	jal	ra,2fa <ADC_Read_All_Channels>
     830:	4702                	lw	a4,0(sp)
     832:	c018                	sw	a4,0(s0)
     834:	3bc9                	jal	606 <Display_Control_Is_On>
     836:	c509                	beqz	a0,840 <main+0xb0>
     838:	4585                	li	a1,1
     83a:	81818513          	addi	a0,gp,-2024 # 20000058 <adc_data>
     83e:	3331                	jal	54a <ADC_Display_Update>
     840:	4505                	li	a0,1
     842:	0ec010ef          	jal	ra,192e <Delay_Ms>
     846:	b745                	j	7e6 <main+0x56>
     848:	2d048513          	addi	a0,s1,720 # 22d0 <CSWTCH.2+0x164>
     84c:	7bc010ef          	jal	ra,2008 <puts>
     850:	3b79                	jal	5ee <Display_Control_Toggle>
     852:	bf75                	j	80e <main+0x7e>
     854:	00002537          	lui	a0,0x2
     858:	2fc50513          	addi	a0,a0,764 # 22fc <CSWTCH.2+0x190>
     85c:	7ac010ef          	jal	ra,2008 <puts>
     860:	3b95                	jal	5d4 <Display_Control_Turn_Off>
     862:	b775                	j	80e <main+0x7e>

00000864 <PWM_GPIO_Config>:
     864:	83dff2ef          	jal	t0,a0 <__riscv_save_0>
     868:	1151                	addi	sp,sp,-12
     86a:	4585                	li	a1,1
     86c:	02000513          	li	a0,32
     870:	c002                	sw	zero,0(sp)
     872:	c202                	sw	zero,4(sp)
     874:	c402                	sw	zero,8(sp)
     876:	60b000ef          	jal	ra,1680 <RCC_APB2PeriphClockCmd>
     87a:	4791                	li	a5,4
     87c:	807c                	sh	a5,0(sp)
     87e:	40011537          	lui	a0,0x40011
     882:	47e1                	li	a5,24
     884:	c43e                	sw	a5,8(sp)
     886:	858a                	mv	a1,sp
     888:	478d                	li	a5,3
     88a:	40050513          	addi	a0,a0,1024 # 40011400 <__global_pointer$+0x20010bc0>
     88e:	c23e                	sw	a5,4(sp)
     890:	42d000ef          	jal	ra,14bc <GPIO_Init>
     894:	0131                	addi	sp,sp,12
     896:	815ff06f          	j	aa <__riscv_restore_0>

0000089a <PWM_Timer_Config>:
     89a:	807ff2ef          	jal	t0,a0 <__riscv_save_0>
     89e:	6505                	lui	a0,0x1
     8a0:	1111                	addi	sp,sp,-28
     8a2:	4585                	li	a1,1
     8a4:	80050513          	addi	a0,a0,-2048 # 800 <main+0x70>
     8a8:	c002                	sw	zero,0(sp)
     8aa:	c202                	sw	zero,4(sp)
     8ac:	00011423          	sh	zero,8(sp)
     8b0:	c602                	sw	zero,12(sp)
     8b2:	c802                	sw	zero,16(sp)
     8b4:	ca02                	sw	zero,20(sp)
     8b6:	cc02                	sw	zero,24(sp)
     8b8:	5c9000ef          	jal	ra,1680 <RCC_APB2PeriphClockCmd>
     8bc:	200007b7          	lui	a5,0x20000
     8c0:	0147a503          	lw	a0,20(a5) # 20000014 <SystemCoreClock>
     8c4:	000f45b7          	lui	a1,0xf4
     8c8:	24058593          	addi	a1,a1,576 # f4240 <_data_lma+0xf1a14>
     8cc:	809ff0ef          	jal	ra,d4 <__udivsi3>
     8d0:	40013437          	lui	s0,0x40013
     8d4:	157d                	addi	a0,a0,-1
     8d6:	8068                	sh	a0,0(sp)
     8d8:	3e700793          	li	a5,999
     8dc:	858a                	mv	a1,sp
     8de:	c0040513          	addi	a0,s0,-1024 # 40012c00 <__global_pointer$+0x200123c0>
     8e2:	c23e                	sw	a5,4(sp)
     8e4:	00011123          	sh	zero,2(sp)
     8e8:	5d5000ef          	jal	ra,16bc <TIM_TimeBaseInit>
     8ec:	67c1                	lui	a5,0x10
     8ee:	06078793          	addi	a5,a5,96 # 10060 <_data_lma+0xd834>
     8f2:	006c                	addi	a1,sp,12
     8f4:	c0040513          	addi	a0,s0,-1024
     8f8:	c63e                	sw	a5,12(sp)
     8fa:	00011923          	sh	zero,18(sp)
     8fe:	00011a23          	sh	zero,20(sp)
     902:	609000ef          	jal	ra,170a <TIM_OC1Init>
     906:	c0040513          	addi	a0,s0,-1024
     90a:	45a1                	li	a1,8
     90c:	6c3000ef          	jal	ra,17ce <TIM_OC1PreloadConfig>
     910:	c0040513          	addi	a0,s0,-1024
     914:	4585                	li	a1,1
     916:	69f000ef          	jal	ra,17b4 <TIM_ARRPreloadConfig>
     91a:	c0040513          	addi	a0,s0,-1024
     91e:	4585                	li	a1,1
     920:	655000ef          	jal	ra,1774 <TIM_Cmd>
     924:	4585                	li	a1,1
     926:	c0040513          	addi	a0,s0,-1024
     92a:	663000ef          	jal	ra,178c <TIM_CtrlPWMOutputs>
     92e:	0171                	addi	sp,sp,28
     930:	f7aff06f          	j	aa <__riscv_restore_0>

00000934 <PWM_Set_Brightness>:
     934:	f6cff2ef          	jal	t0,a0 <__riscv_save_0>
     938:	3e800793          	li	a5,1000
     93c:	3e800413          	li	s0,1000
     940:	00a7e363          	bltu	a5,a0,946 <PWM_Set_Brightness+0x12>
     944:	842a                	mv	s0,a0
     946:	01041593          	slli	a1,s0,0x10
     94a:	40013537          	lui	a0,0x40013
     94e:	81c1                	srli	a1,a1,0x10
     950:	c0050513          	addi	a0,a0,-1024 # 40012c00 <__global_pointer$+0x200123c0>
     954:	689000ef          	jal	ra,17dc <TIM_SetCompare1>
     958:	82819a23          	sh	s0,-1996(gp) # 20000074 <pwm_control>
     95c:	f4eff06f          	j	aa <__riscv_restore_0>

00000960 <PWM_Config_Init>:
     960:	f40ff2ef          	jal	t0,a0 <__riscv_save_0>
     964:	83418793          	addi	a5,gp,-1996 # 20000074 <pwm_control>
     968:	01f40737          	lui	a4,0x1f40
     96c:	c398                	sw	a4,0(a5)
     96e:	6705                	lui	a4,0x1
     970:	a0070713          	addi	a4,a4,-1536 # a00 <SPI_send_DMA+0x8>
     974:	a3da                	sh	a4,4(a5)
     976:	35fd                	jal	864 <PWM_GPIO_Config>
     978:	370d                	jal	89a <PWM_Timer_Config>
     97a:	4501                	li	a0,0
     97c:	3f65                	jal	934 <PWM_Set_Brightness>
     97e:	f2cff06f          	j	aa <__riscv_restore_0>

00000982 <PWM_Update_Fade>:
     982:	83418793          	addi	a5,gp,-1996 # 20000074 <pwm_control>
     986:	23d8                	lbu	a4,4(a5)
     988:	c729                	beqz	a4,9d2 <PWM_Update_Fade+0x50>
     98a:	f16ff2ef          	jal	t0,a0 <__riscv_save_0>
     98e:	238a                	lhu	a0,0(a5)
     990:	23ba                	lhu	a4,2(a5)
     992:	00e57e63          	bgeu	a0,a4,9ae <PWM_Update_Fade+0x2c>
     996:	33d4                	lbu	a3,5(a5)
     998:	9536                	add	a0,a0,a3
     99a:	0542                	slli	a0,a0,0x10
     99c:	8141                	srli	a0,a0,0x10
     99e:	00e56563          	bltu	a0,a4,9a8 <PWM_Update_Fade+0x26>
     9a2:	00078223          	sb	zero,4(a5)
     9a6:	853a                	mv	a0,a4
     9a8:	3771                	jal	934 <PWM_Set_Brightness>
     9aa:	f00ff06f          	j	aa <__riscv_restore_0>
     9ae:	00a77f63          	bgeu	a4,a0,9cc <PWM_Update_Fade+0x4a>
     9b2:	83418693          	addi	a3,gp,-1996 # 20000074 <pwm_control>
     9b6:	32dc                	lbu	a5,5(a3)
     9b8:	00f56763          	bltu	a0,a5,9c6 <PWM_Update_Fade+0x44>
     9bc:	8d1d                	sub	a0,a0,a5
     9be:	0542                	slli	a0,a0,0x10
     9c0:	8141                	srli	a0,a0,0x10
     9c2:	fea763e3          	bltu	a4,a0,9a8 <PWM_Update_Fade+0x26>
     9c6:	00068223          	sb	zero,4(a3)
     9ca:	bff1                	j	9a6 <PWM_Update_Fade+0x24>
     9cc:	00078223          	sb	zero,4(a5)
     9d0:	bfe9                	j	9aa <PWM_Update_Fade+0x28>
     9d2:	8082                	ret

000009d4 <PWM_Turn_On>:
     9d4:	83418793          	addi	a5,gp,-1996 # 20000074 <pwm_control>
     9d8:	1f400713          	li	a4,500
     9dc:	a3ba                	sh	a4,2(a5)
     9de:	4705                	li	a4,1
     9e0:	a3d8                	sb	a4,4(a5)
     9e2:	8082                	ret

000009e4 <PWM_Turn_Off>:
     9e4:	83418793          	addi	a5,gp,-1996 # 20000074 <pwm_control>
     9e8:	4705                	li	a4,1
     9ea:	00079123          	sh	zero,2(a5)
     9ee:	a3d8                	sb	a4,4(a5)
     9f0:	8082                	ret

000009f2 <PWM_Get_Brightness>:
     9f2:	8341d503          	lhu	a0,-1996(gp) # 20000074 <pwm_control>
     9f6:	8082                	ret

000009f8 <SPI_send_DMA>:
     9f8:	400207b7          	lui	a5,0x40020
     9fc:	dfc8                	sw	a0,60(a5)
     9fe:	dbcc                	sw	a1,52(a5)
     a00:	5b98                	lw	a4,48(a5)
     a02:	400206b7          	lui	a3,0x40020
     a06:	20000593          	li	a1,512
     a0a:	00176713          	ori	a4,a4,1
     a0e:	db98                	sw	a4,48(a5)
     a10:	67c1                	lui	a5,0x10
     a12:	17fd                	addi	a5,a5,-1
     a14:	167d                	addi	a2,a2,-1
     a16:	0642                	slli	a2,a2,0x10
     a18:	8241                	srli	a2,a2,0x10
     a1a:	00f61863          	bne	a2,a5,a2a <SPI_send_DMA+0x32>
     a1e:	40020737          	lui	a4,0x40020
     a22:	5b1c                	lw	a5,48(a4)
     a24:	9bf9                	andi	a5,a5,-2
     a26:	db1c                	sw	a5,48(a4)
     a28:	8082                	ret
     a2a:	c2cc                	sw	a1,4(a3)
     a2c:	4298                	lw	a4,0(a3)
     a2e:	20077713          	andi	a4,a4,512
     a32:	df6d                	beqz	a4,a2c <SPI_send_DMA+0x34>
     a34:	b7c5                	j	a14 <SPI_send_DMA+0x1c>

00000a36 <SPI_send>:
     a36:	400137b7          	lui	a5,0x40013
     a3a:	a7ca                	sh	a0,12(a5)
     a3c:	40013737          	lui	a4,0x40013
     a40:	271e                	lhu	a5,8(a4)
     a42:	8b89                	andi	a5,a5,2
     a44:	dff5                	beqz	a5,a40 <SPI_send+0xa>
     a46:	8082                	ret

00000a48 <write_command_8>:
     a48:	e58ff2ef          	jal	t0,a0 <__riscv_save_0>
     a4c:	40011737          	lui	a4,0x40011
     a50:	4b5c                	lw	a5,20(a4)
     a52:	0087e793          	ori	a5,a5,8
     a56:	cb5c                	sw	a5,20(a4)
     a58:	3ff9                	jal	a36 <SPI_send>
     a5a:	e50ff06f          	j	aa <__riscv_restore_0>

00000a5e <write_data_16>:
     a5e:	e42ff2ef          	jal	t0,a0 <__riscv_save_0>
     a62:	40011737          	lui	a4,0x40011
     a66:	4b1c                	lw	a5,16(a4)
     a68:	842a                	mv	s0,a0
     a6a:	8121                	srli	a0,a0,0x8
     a6c:	0087e793          	ori	a5,a5,8
     a70:	cb1c                	sw	a5,16(a4)
     a72:	37d1                	jal	a36 <SPI_send>
     a74:	0ff47513          	andi	a0,s0,255
     a78:	3f7d                	jal	a36 <SPI_send>
     a7a:	e30ff06f          	j	aa <__riscv_restore_0>

00000a7e <tft_set_window>:
     a7e:	e22ff2ef          	jal	t0,a0 <__riscv_save_0>
     a82:	1151                	addi	sp,sp,-12
     a84:	842a                	mv	s0,a0
     a86:	02a00513          	li	a0,42
     a8a:	c036                	sw	a3,0(sp)
     a8c:	c42e                	sw	a1,8(sp)
     a8e:	c232                	sw	a2,4(sp)
     a90:	3f65                	jal	a48 <write_command_8>
     a92:	8522                	mv	a0,s0
     a94:	37e9                	jal	a5e <write_data_16>
     a96:	4612                	lw	a2,4(sp)
     a98:	8532                	mv	a0,a2
     a9a:	37d1                	jal	a5e <write_data_16>
     a9c:	02b00513          	li	a0,43
     aa0:	3765                	jal	a48 <write_command_8>
     aa2:	45a2                	lw	a1,8(sp)
     aa4:	852e                	mv	a0,a1
     aa6:	3f65                	jal	a5e <write_data_16>
     aa8:	4682                	lw	a3,0(sp)
     aaa:	8536                	mv	a0,a3
     aac:	3f4d                	jal	a5e <write_data_16>
     aae:	02c00513          	li	a0,44
     ab2:	3f59                	jal	a48 <write_command_8>
     ab4:	0131                	addi	sp,sp,12
     ab6:	df4ff06f          	j	aa <__riscv_restore_0>

00000aba <tft_init>:
     aba:	de6ff2ef          	jal	t0,a0 <__riscv_save_0>
     abe:	400216b7          	lui	a3,0x40021
     ac2:	4e9c                	lw	a5,24(a3)
     ac4:	6705                	lui	a4,0x1
     ac6:	0741                	addi	a4,a4,16
     ac8:	8fd9                	or	a5,a5,a4
     aca:	40011437          	lui	s0,0x40011
     ace:	ce9c                	sw	a5,24(a3)
     ad0:	401c                	lw	a5,0(s0)
     ad2:	777d                	lui	a4,0xfffff
     ad4:	0ff70713          	addi	a4,a4,255 # fffff0ff <__global_pointer$+0xdfffe8bf>
     ad8:	8ff9                	and	a5,a5,a4
     ada:	c01c                	sw	a5,0(s0)
     adc:	401c                	lw	a5,0(s0)
     ade:	7745                	lui	a4,0xffff1
     ae0:	177d                	addi	a4,a4,-1
     ae2:	3007e793          	ori	a5,a5,768
     ae6:	c01c                	sw	a5,0(s0)
     ae8:	401c                	lw	a5,0(s0)
     aea:	fff10637          	lui	a2,0xfff10
     aee:	167d                	addi	a2,a2,-1
     af0:	8ff9                	and	a5,a5,a4
     af2:	c01c                	sw	a5,0(s0)
     af4:	401c                	lw	a5,0(s0)
     af6:	670d                	lui	a4,0x3
     af8:	1101                	addi	sp,sp,-32
     afa:	8fd9                	or	a5,a5,a4
     afc:	c01c                	sw	a5,0(s0)
     afe:	401c                	lw	a5,0(s0)
     b00:	0b070713          	addi	a4,a4,176 # 30b0 <_data_lma+0x884>
     b04:	03200513          	li	a0,50
     b08:	8ff1                	and	a5,a5,a2
     b0a:	c01c                	sw	a5,0(s0)
     b0c:	401c                	lw	a5,0(s0)
     b0e:	00030637          	lui	a2,0x30
     b12:	8fd1                	or	a5,a5,a2
     b14:	c01c                	sw	a5,0(s0)
     b16:	401c                	lw	a5,0(s0)
     b18:	ff100637          	lui	a2,0xff100
     b1c:	167d                	addi	a2,a2,-1
     b1e:	8ff1                	and	a5,a5,a2
     b20:	c01c                	sw	a5,0(s0)
     b22:	401c                	lw	a5,0(s0)
     b24:	00b00637          	lui	a2,0xb00
     b28:	8fd1                	or	a5,a5,a2
     b2a:	c01c                	sw	a5,0(s0)
     b2c:	401c                	lw	a5,0(s0)
     b2e:	f1000637          	lui	a2,0xf1000
     b32:	167d                	addi	a2,a2,-1
     b34:	8ff1                	and	a5,a5,a2
     b36:	c01c                	sw	a5,0(s0)
     b38:	401c                	lw	a5,0(s0)
     b3a:	0b000637          	lui	a2,0xb000
     b3e:	8fd1                	or	a5,a5,a2
     b40:	7671                	lui	a2,0xffffc
     b42:	c01c                	sw	a5,0(s0)
     b44:	30460613          	addi	a2,a2,772 # ffffc304 <__global_pointer$+0xdfffbac4>
     b48:	400137b7          	lui	a5,0x40013
     b4c:	a392                	sh	a2,0(a5)
     b4e:	461d                	li	a2,7
     b50:	ab92                	sh	a2,16(a5)
     b52:	23d2                	lhu	a2,4(a5)
     b54:	07b1                	addi	a5,a5,12
     b56:	00266613          	ori	a2,a2,2
     b5a:	fec79c23          	sh	a2,-8(a5) # 40012ff8 <__global_pointer$+0x200127b8>
     b5e:	ff47d603          	lhu	a2,-12(a5)
     b62:	04066613          	ori	a2,a2,64
     b66:	fec79a23          	sh	a2,-12(a5)
     b6a:	4ad0                	lw	a2,20(a3)
     b6c:	00166613          	ori	a2,a2,1
     b70:	cad0                	sw	a2,20(a3)
     b72:	400206b7          	lui	a3,0x40020
     b76:	da98                	sw	a4,48(a3)
     b78:	de9c                	sw	a5,56(a3)
     b7a:	485c                	lw	a5,20(s0)
     b7c:	0047e793          	ori	a5,a5,4
     b80:	c85c                	sw	a5,20(s0)
     b82:	5ad000ef          	jal	ra,192e <Delay_Ms>
     b86:	481c                	lw	a5,16(s0)
     b88:	03200513          	li	a0,50
     b8c:	0047e793          	ori	a5,a5,4
     b90:	c81c                	sw	a5,16(s0)
     b92:	59d000ef          	jal	ra,192e <Delay_Ms>
     b96:	485c                	lw	a5,20(s0)
     b98:	4545                	li	a0,17
     b9a:	0107e793          	ori	a5,a5,16
     b9e:	c85c                	sw	a5,20(s0)
     ba0:	3565                	jal	a48 <write_command_8>
     ba2:	07800513          	li	a0,120
     ba6:	589000ef          	jal	ra,192e <Delay_Ms>
     baa:	03600513          	li	a0,54
     bae:	3d69                	jal	a48 <write_command_8>
     bb0:	481c                	lw	a5,16(s0)
     bb2:	0a800513          	li	a0,168
     bb6:	0087e793          	ori	a5,a5,8
     bba:	c81c                	sw	a5,16(s0)
     bbc:	3dad                	jal	a36 <SPI_send>
     bbe:	03a00513          	li	a0,58
     bc2:	3559                	jal	a48 <write_command_8>
     bc4:	481c                	lw	a5,16(s0)
     bc6:	4515                	li	a0,5
     bc8:	0087e793          	ori	a5,a5,8
     bcc:	c81c                	sw	a5,16(s0)
     bce:	35a5                	jal	a36 <SPI_send>
     bd0:	6589                	lui	a1,0x2
     bd2:	12458493          	addi	s1,a1,292 # 2124 <memcpy+0xd2>
     bd6:	4641                	li	a2,16
     bd8:	12458593          	addi	a1,a1,292
     bdc:	850a                	mv	a0,sp
     bde:	474010ef          	jal	ra,2052 <memcpy>
     be2:	0e000513          	li	a0,224
     be6:	358d                	jal	a48 <write_command_8>
     be8:	481c                	lw	a5,16(s0)
     bea:	850a                	mv	a0,sp
     bec:	4605                	li	a2,1
     bee:	0087e793          	ori	a5,a5,8
     bf2:	c81c                	sw	a5,16(s0)
     bf4:	45c1                	li	a1,16
     bf6:	3509                	jal	9f8 <SPI_send_DMA>
     bf8:	01048593          	addi	a1,s1,16
     bfc:	4641                	li	a2,16
     bfe:	0808                	addi	a0,sp,16
     c00:	452010ef          	jal	ra,2052 <memcpy>
     c04:	0e100513          	li	a0,225
     c08:	3581                	jal	a48 <write_command_8>
     c0a:	481c                	lw	a5,16(s0)
     c0c:	4605                	li	a2,1
     c0e:	45c1                	li	a1,16
     c10:	0087e793          	ori	a5,a5,8
     c14:	c81c                	sw	a5,16(s0)
     c16:	0808                	addi	a0,sp,16
     c18:	33c5                	jal	9f8 <SPI_send_DMA>
     c1a:	4529                	li	a0,10
     c1c:	513000ef          	jal	ra,192e <Delay_Ms>
     c20:	02100513          	li	a0,33
     c24:	3515                	jal	a48 <write_command_8>
     c26:	454d                	li	a0,19
     c28:	3505                	jal	a48 <write_command_8>
     c2a:	4529                	li	a0,10
     c2c:	503000ef          	jal	ra,192e <Delay_Ms>
     c30:	02900513          	li	a0,41
     c34:	3d11                	jal	a48 <write_command_8>
     c36:	4529                	li	a0,10
     c38:	4f7000ef          	jal	ra,192e <Delay_Ms>
     c3c:	481c                	lw	a5,16(s0)
     c3e:	0107e793          	ori	a5,a5,16
     c42:	c81c                	sw	a5,16(s0)
     c44:	6105                	addi	sp,sp,32
     c46:	c64ff06f          	j	aa <__riscv_restore_0>

00000c4a <tft_set_cursor>:
     c4a:	0505                	addi	a0,a0,1
     c4c:	96a19e23          	sh	a0,-1668(gp) # 200001bc <_cursor_x>
     c50:	05e9                	addi	a1,a1,26
     c52:	96b19f23          	sh	a1,-1666(gp) # 200001be <_cursor_y>
     c56:	8082                	ret

00000c58 <tft_set_color>:
     c58:	200007b7          	lui	a5,0x20000
     c5c:	00a79023          	sh	a0,0(a5) # 20000000 <_highcode_vma_end>
     c60:	8082                	ret

00000c62 <tft_set_background_color>:
     c62:	82a19d23          	sh	a0,-1990(gp) # 2000007a <_bg_color>
     c66:	8082                	ret

00000c68 <tft_print_char>:
     c68:	c38ff2ef          	jal	t0,a0 <__riscv_save_0>
     c6c:	00251793          	slli	a5,a0,0x2
     c70:	953e                	add	a0,a0,a5
     c72:	83a1d783          	lhu	a5,-1990(gp) # 2000007a <_bg_color>
     c76:	1131                	addi	sp,sp,-20
     c78:	0087d713          	srli	a4,a5,0x8
     c7c:	0ff7f793          	andi	a5,a5,255
     c80:	c63e                	sw	a5,12(sp)
     c82:	200007b7          	lui	a5,0x20000
     c86:	0007d783          	lhu	a5,0(a5) # 20000000 <_highcode_vma_end>
     c8a:	c43a                	sw	a4,8(sp)
     c8c:	4281                	li	t0,0
     c8e:	0087d713          	srli	a4,a5,0x8
     c92:	0ff7f793          	andi	a5,a5,255
     c96:	c23e                	sw	a5,4(sp)
     c98:	6789                	lui	a5,0x2
     c9a:	32478793          	addi	a5,a5,804 # 2324 <font>
     c9e:	c03a                	sw	a4,0(sp)
     ca0:	4681                	li	a3,0
     ca2:	c83e                	sw	a5,16(sp)
     ca4:	83c18313          	addi	t1,gp,-1988 # 2000007c <_buffer>
     ca8:	4785                	li	a5,1
     caa:	005790b3          	sll	ra,a5,t0
     cae:	85b6                	mv	a1,a3
     cb0:	4601                	li	a2,0
     cb2:	44c2                	lw	s1,16(sp)
     cb4:	00c503b3          	add	t2,a0,a2
     cb8:	872e                	mv	a4,a1
     cba:	93a6                	add	t2,t2,s1
     cbc:	0003c383          	lbu	t2,0(t2)
     cc0:	00158793          	addi	a5,a1,1
     cc4:	0589                	addi	a1,a1,2
     cc6:	07c2                	slli	a5,a5,0x10
     cc8:	05c2                	slli	a1,a1,0x10
     cca:	0013f3b3          	and	t2,t2,ra
     cce:	83c1                	srli	a5,a5,0x10
     cd0:	81c1                	srli	a1,a1,0x10
     cd2:	971a                	add	a4,a4,t1
     cd4:	06038763          	beqz	t2,d42 <tft_print_char+0xda>
     cd8:	4482                	lw	s1,0(sp)
     cda:	979a                	add	a5,a5,t1
     cdc:	a304                	sb	s1,0(a4)
     cde:	4712                	lw	a4,4(sp)
     ce0:	a398                	sb	a4,0(a5)
     ce2:	0605                	addi	a2,a2,1
     ce4:	4795                	li	a5,5
     ce6:	fcf616e3          	bne	a2,a5,cb2 <tft_print_char+0x4a>
     cea:	06a9                	addi	a3,a3,10
     cec:	06c2                	slli	a3,a3,0x10
     cee:	82c1                	srli	a3,a3,0x10
     cf0:	04600793          	li	a5,70
     cf4:	0285                	addi	t0,t0,1
     cf6:	faf699e3          	bne	a3,a5,ca8 <tft_print_char+0x40>
     cfa:	400114b7          	lui	s1,0x40011
     cfe:	48dc                	lw	a5,20(s1)
     d00:	0107e793          	ori	a5,a5,16
     d04:	c8dc                	sw	a5,20(s1)
     d06:	97c1d503          	lhu	a0,-1668(gp) # 200001bc <_cursor_x>
     d0a:	97e1d583          	lhu	a1,-1666(gp) # 200001be <_cursor_y>
     d0e:	00450613          	addi	a2,a0,4
     d12:	0642                	slli	a2,a2,0x10
     d14:	00658693          	addi	a3,a1,6
     d18:	06c2                	slli	a3,a3,0x10
     d1a:	82c1                	srli	a3,a3,0x10
     d1c:	8241                	srli	a2,a2,0x10
     d1e:	3385                	jal	a7e <tft_set_window>
     d20:	489c                	lw	a5,16(s1)
     d22:	4605                	li	a2,1
     d24:	04600593          	li	a1,70
     d28:	0087e793          	ori	a5,a5,8
     d2c:	c89c                	sw	a5,16(s1)
     d2e:	83c18513          	addi	a0,gp,-1988 # 2000007c <_buffer>
     d32:	31d9                	jal	9f8 <SPI_send_DMA>
     d34:	489c                	lw	a5,16(s1)
     d36:	0107e793          	ori	a5,a5,16
     d3a:	c89c                	sw	a5,16(s1)
     d3c:	0151                	addi	sp,sp,20
     d3e:	b6cff06f          	j	aa <__riscv_restore_0>
     d42:	44a2                	lw	s1,8(sp)
     d44:	979a                	add	a5,a5,t1
     d46:	a304                	sb	s1,0(a4)
     d48:	4732                	lw	a4,12(sp)
     d4a:	bf59                	j	ce0 <tft_print_char+0x78>

00000d4c <tft_print>:
     d4c:	b54ff2ef          	jal	t0,a0 <__riscv_save_0>
     d50:	842a                	mv	s0,a0
     d52:	00040503          	lb	a0,0(s0) # 40011000 <__global_pointer$+0x200107c0>
     d56:	e119                	bnez	a0,d5c <tft_print+0x10>
     d58:	b52ff06f          	j	aa <__riscv_restore_0>
     d5c:	3731                	jal	c68 <tft_print_char>
     d5e:	97c18713          	addi	a4,gp,-1668 # 200001bc <_cursor_x>
     d62:	231e                	lhu	a5,0(a4)
     d64:	0405                	addi	s0,s0,1
     d66:	0799                	addi	a5,a5,6
     d68:	a31e                	sh	a5,0(a4)
     d6a:	b7e5                	j	d52 <tft_print+0x6>

00000d6c <tft_print_number>:
     d6c:	b34ff2ef          	jal	t0,a0 <__riscv_save_0>
     d70:	1141                	addi	sp,sp,-16
     d72:	87aa                	mv	a5,a0
     d74:	86ae                	mv	a3,a1
     d76:	4701                	li	a4,0
     d78:	00055563          	bgez	a0,d82 <tft_print_number+0x16>
     d7c:	40a007b3          	neg	a5,a0
     d80:	4705                	li	a4,1
     d82:	98018613          	addi	a2,gp,-1664 # 200001c0 <str.4169>
     d86:	000605a3          	sb	zero,11(a2)
     d8a:	442d                	li	s0,11
     d8c:	98018493          	addi	s1,gp,-1664 # 200001c0 <str.4169>
     d90:	eba9                	bnez	a5,de2 <tft_print_number+0x76>
     d92:	47ad                	li	a5,11
     d94:	00f41663          	bne	s0,a5,da0 <tft_print_number+0x34>
     d98:	03000793          	li	a5,48
     d9c:	a4bc                	sb	a5,10(s1)
     d9e:	4429                	li	s0,10
     da0:	cb09                	beqz	a4,db2 <tft_print_number+0x46>
     da2:	147d                	addi	s0,s0,-1
     da4:	0ff47413          	andi	s0,s0,255
     da8:	008487b3          	add	a5,s1,s0
     dac:	02d00713          	li	a4,45
     db0:	a398                	sb	a4,0(a5)
     db2:	472d                	li	a4,11
     db4:	8f01                	sub	a4,a4,s0
     db6:	00171793          	slli	a5,a4,0x1
     dba:	97ba                	add	a5,a5,a4
     dbc:	0786                	slli	a5,a5,0x1
     dbe:	17fd                	addi	a5,a5,-1
     dc0:	07c2                	slli	a5,a5,0x10
     dc2:	83c1                	srli	a5,a5,0x10
     dc4:	00d7f963          	bgeu	a5,a3,dd6 <tft_print_number+0x6a>
     dc8:	97c18713          	addi	a4,gp,-1668 # 200001bc <_cursor_x>
     dcc:	2312                	lhu	a2,0(a4)
     dce:	96b2                	add	a3,a3,a2
     dd0:	40f687b3          	sub	a5,a3,a5
     dd4:	a31e                	sh	a5,0(a4)
     dd6:	00848533          	add	a0,s1,s0
     dda:	3f8d                	jal	d4c <tft_print>
     ddc:	0141                	addi	sp,sp,16
     dde:	accff06f          	j	aa <__riscv_restore_0>
     de2:	147d                	addi	s0,s0,-1
     de4:	0ff47413          	andi	s0,s0,255
     de8:	00848633          	add	a2,s1,s0
     dec:	45a9                	li	a1,10
     dee:	853e                	mv	a0,a5
     df0:	c636                	sw	a3,12(sp)
     df2:	c43a                	sw	a4,8(sp)
     df4:	c232                	sw	a2,4(sp)
     df6:	c03e                	sw	a5,0(sp)
     df8:	b2cff0ef          	jal	ra,124 <__modsi3>
     dfc:	4782                	lw	a5,0(sp)
     dfe:	4612                	lw	a2,4(sp)
     e00:	03050513          	addi	a0,a0,48
     e04:	45a9                	li	a1,10
     e06:	a208                	sb	a0,0(a2)
     e08:	853e                	mv	a0,a5
     e0a:	ac2ff0ef          	jal	ra,cc <__divsi3>
     e0e:	87aa                	mv	a5,a0
     e10:	46b2                	lw	a3,12(sp)
     e12:	4722                	lw	a4,8(sp)
     e14:	bfb5                	j	d90 <tft_print_number+0x24>

00000e16 <tft_fill_rect>:
     e16:	a8aff2ef          	jal	t0,a0 <__riscv_save_0>
     e1a:	0505                	addi	a0,a0,1
     e1c:	05e9                	addi	a1,a1,26
     e1e:	0542                	slli	a0,a0,0x10
     e20:	05c2                	slli	a1,a1,0x10
     e22:	8336                	mv	t1,a3
     e24:	1171                	addi	sp,sp,-4
     e26:	8141                	srli	a0,a0,0x10
     e28:	81c1                	srli	a1,a1,0x10
     e2a:	00875393          	srli	t2,a4,0x8
     e2e:	4781                	li	a5,0
     e30:	83c18693          	addi	a3,gp,-1988 # 2000007c <_buffer>
     e34:	00179413          	slli	s0,a5,0x1
     e38:	0442                	slli	s0,s0,0x10
     e3a:	8041                	srli	s0,s0,0x10
     e3c:	04c79763          	bne	a5,a2,e8a <tft_fill_rect+0x74>
     e40:	400114b7          	lui	s1,0x40011
     e44:	48d8                	lw	a4,20(s1)
     e46:	fff30693          	addi	a3,t1,-1
     e4a:	fff78613          	addi	a2,a5,-1
     e4e:	96ae                	add	a3,a3,a1
     e50:	962a                	add	a2,a2,a0
     e52:	01076713          	ori	a4,a4,16
     e56:	06c2                	slli	a3,a3,0x10
     e58:	0642                	slli	a2,a2,0x10
     e5a:	c8d8                	sw	a4,20(s1)
     e5c:	82c1                	srli	a3,a3,0x10
     e5e:	8241                	srli	a2,a2,0x10
     e60:	c01a                	sw	t1,0(sp)
     e62:	c1dff0ef          	jal	ra,a7e <tft_set_window>
     e66:	489c                	lw	a5,16(s1)
     e68:	4302                	lw	t1,0(sp)
     e6a:	0087e793          	ori	a5,a5,8
     e6e:	c89c                	sw	a5,16(s1)
     e70:	861a                	mv	a2,t1
     e72:	85a2                	mv	a1,s0
     e74:	83c18513          	addi	a0,gp,-1988 # 2000007c <_buffer>
     e78:	b81ff0ef          	jal	ra,9f8 <SPI_send_DMA>
     e7c:	489c                	lw	a5,16(s1)
     e7e:	0107e793          	ori	a5,a5,16
     e82:	c89c                	sw	a5,16(s1)
     e84:	0111                	addi	sp,sp,4
     e86:	a24ff06f          	j	aa <__riscv_restore_0>
     e8a:	008684b3          	add	s1,a3,s0
     e8e:	0405                	addi	s0,s0,1
     e90:	0442                	slli	s0,s0,0x10
     e92:	8041                	srli	s0,s0,0x10
     e94:	0785                	addi	a5,a5,1
     e96:	00748023          	sb	t2,0(s1) # 40011000 <__global_pointer$+0x200107c0>
     e9a:	9436                	add	s0,s0,a3
     e9c:	07c2                	slli	a5,a5,0x10
     e9e:	a018                	sb	a4,0(s0)
     ea0:	83c1                	srli	a5,a5,0x10
     ea2:	bf49                	j	e34 <tft_fill_rect+0x1e>

00000ea4 <SystemInit>:
     ea4:	9fcff2ef          	jal	t0,a0 <__riscv_save_0>
     ea8:	40021437          	lui	s0,0x40021
     eac:	401c                	lw	a5,0(s0)
     eae:	f8ff0737          	lui	a4,0xf8ff0
     eb2:	1161                	addi	sp,sp,-8
     eb4:	0017e793          	ori	a5,a5,1
     eb8:	c01c                	sw	a5,0(s0)
     eba:	405c                	lw	a5,4(s0)
     ebc:	4541                	li	a0,16
     ebe:	8ff9                	and	a5,a5,a4
     ec0:	c05c                	sw	a5,4(s0)
     ec2:	401c                	lw	a5,0(s0)
     ec4:	fef70737          	lui	a4,0xfef70
     ec8:	177d                	addi	a4,a4,-1
     eca:	8ff9                	and	a5,a5,a4
     ecc:	c01c                	sw	a5,0(s0)
     ece:	401c                	lw	a5,0(s0)
     ed0:	fffc0737          	lui	a4,0xfffc0
     ed4:	177d                	addi	a4,a4,-1
     ed6:	8ff9                	and	a5,a5,a4
     ed8:	c01c                	sw	a5,0(s0)
     eda:	405c                	lw	a5,4(s0)
     edc:	7741                	lui	a4,0xffff0
     ede:	177d                	addi	a4,a4,-1
     ee0:	8ff9                	and	a5,a5,a4
     ee2:	c05c                	sw	a5,4(s0)
     ee4:	009f07b7          	lui	a5,0x9f0
     ee8:	c41c                	sw	a5,8(s0)
     eea:	25cd                	jal	15cc <RCC_AdjustHSICalibrationValue>
     eec:	4c1c                	lw	a5,24(s0)
     eee:	00020637          	lui	a2,0x20
     ef2:	0207e793          	ori	a5,a5,32
     ef6:	cc1c                	sw	a5,24(s0)
     ef8:	400117b7          	lui	a5,0x40011
     efc:	4007a703          	lw	a4,1024(a5) # 40011400 <__global_pointer$+0x20010bc0>
     f00:	40078693          	addi	a3,a5,1024
     f04:	f0f77713          	andi	a4,a4,-241
     f08:	40e7a023          	sw	a4,1024(a5)
     f0c:	4007a703          	lw	a4,1024(a5)
     f10:	08076713          	ori	a4,a4,128
     f14:	40e7a023          	sw	a4,1024(a5)
     f18:	4789                	li	a5,2
     f1a:	ca9c                	sw	a5,16(a3)
     f1c:	c002                	sw	zero,0(sp)
     f1e:	c202                	sw	zero,4(sp)
     f20:	4c1c                	lw	a5,24(s0)
     f22:	40010737          	lui	a4,0x40010
     f26:	66a1                	lui	a3,0x8
     f28:	0017e793          	ori	a5,a5,1
     f2c:	cc1c                	sw	a5,24(s0)
     f2e:	435c                	lw	a5,4(a4)
     f30:	8fd5                	or	a5,a5,a3
     f32:	c35c                	sw	a5,4(a4)
     f34:	401c                	lw	a5,0(s0)
     f36:	6741                	lui	a4,0x10
     f38:	400216b7          	lui	a3,0x40021
     f3c:	8fd9                	or	a5,a5,a4
     f3e:	c01c                	sw	a5,0(s0)
     f40:	6709                	lui	a4,0x2
     f42:	429c                	lw	a5,0(a3)
     f44:	8ff1                	and	a5,a5,a2
     f46:	c23e                	sw	a5,4(sp)
     f48:	4782                	lw	a5,0(sp)
     f4a:	0785                	addi	a5,a5,1
     f4c:	c03e                	sw	a5,0(sp)
     f4e:	4792                	lw	a5,4(sp)
     f50:	e781                	bnez	a5,f58 <SystemInit+0xb4>
     f52:	4782                	lw	a5,0(sp)
     f54:	fee797e3          	bne	a5,a4,f42 <SystemInit+0x9e>
     f58:	400217b7          	lui	a5,0x40021
     f5c:	439c                	lw	a5,0(a5)
     f5e:	00e79713          	slli	a4,a5,0xe
     f62:	06075963          	bgez	a4,fd4 <SystemInit+0x130>
     f66:	4785                	li	a5,1
     f68:	c23e                	sw	a5,4(sp)
     f6a:	4712                	lw	a4,4(sp)
     f6c:	4785                	li	a5,1
     f6e:	06f71063          	bne	a4,a5,fce <SystemInit+0x12a>
     f72:	400227b7          	lui	a5,0x40022
     f76:	4398                	lw	a4,0(a5)
     f78:	76c1                	lui	a3,0xffff0
     f7a:	16fd                	addi	a3,a3,-1
     f7c:	9b71                	andi	a4,a4,-4
     f7e:	c398                	sw	a4,0(a5)
     f80:	4398                	lw	a4,0(a5)
     f82:	00176713          	ori	a4,a4,1
     f86:	c398                	sw	a4,0(a5)
     f88:	400217b7          	lui	a5,0x40021
     f8c:	43d8                	lw	a4,4(a5)
     f8e:	c3d8                	sw	a4,4(a5)
     f90:	43d8                	lw	a4,4(a5)
     f92:	8f75                	and	a4,a4,a3
     f94:	c3d8                	sw	a4,4(a5)
     f96:	43d8                	lw	a4,4(a5)
     f98:	66c1                	lui	a3,0x10
     f9a:	8f55                	or	a4,a4,a3
     f9c:	c3d8                	sw	a4,4(a5)
     f9e:	4398                	lw	a4,0(a5)
     fa0:	010006b7          	lui	a3,0x1000
     fa4:	8f55                	or	a4,a4,a3
     fa6:	c398                	sw	a4,0(a5)
     fa8:	4398                	lw	a4,0(a5)
     faa:	00671693          	slli	a3,a4,0x6
     fae:	fe06dde3          	bgez	a3,fa8 <SystemInit+0x104>
     fb2:	43d8                	lw	a4,4(a5)
     fb4:	400216b7          	lui	a3,0x40021
     fb8:	9b71                	andi	a4,a4,-4
     fba:	c3d8                	sw	a4,4(a5)
     fbc:	43d8                	lw	a4,4(a5)
     fbe:	00276713          	ori	a4,a4,2
     fc2:	c3d8                	sw	a4,4(a5)
     fc4:	4721                	li	a4,8
     fc6:	42dc                	lw	a5,4(a3)
     fc8:	8bb1                	andi	a5,a5,12
     fca:	fee79ee3          	bne	a5,a4,fc6 <SystemInit+0x122>
     fce:	0121                	addi	sp,sp,8
     fd0:	8daff06f          	j	aa <__riscv_restore_0>
     fd4:	c202                	sw	zero,4(sp)
     fd6:	bf51                	j	f6a <SystemInit+0xc6>

00000fd8 <SystemCoreClockUpdate>:
     fd8:	8c8ff2ef          	jal	t0,a0 <__riscv_save_0>
     fdc:	40021737          	lui	a4,0x40021
     fe0:	435c                	lw	a5,4(a4)
     fe2:	20000437          	lui	s0,0x20000
     fe6:	4691                	li	a3,4
     fe8:	8bb1                	andi	a5,a5,12
     fea:	01440413          	addi	s0,s0,20 # 20000014 <SystemCoreClock>
     fee:	00d78563          	beq	a5,a3,ff8 <SystemCoreClockUpdate+0x20>
     ff2:	46a1                	li	a3,8
     ff4:	04d78263          	beq	a5,a3,1038 <SystemCoreClockUpdate+0x60>
     ff8:	016e37b7          	lui	a5,0x16e3
     ffc:	60078793          	addi	a5,a5,1536 # 16e3600 <_data_lma+0x16e0dd4>
    1000:	c01c                	sw	a5,0(s0)
    1002:	400216b7          	lui	a3,0x40021
    1006:	42dc                	lw	a5,4(a3)
    1008:	4008                	lw	a0,0(s0)
    100a:	8391                	srli	a5,a5,0x4
    100c:	00f7f713          	andi	a4,a5,15
    1010:	200007b7          	lui	a5,0x20000
    1014:	00478793          	addi	a5,a5,4 # 20000004 <AHBPrescTable>
    1018:	97ba                	add	a5,a5,a4
    101a:	238c                	lbu	a1,0(a5)
    101c:	42dc                	lw	a5,4(a3)
    101e:	0ff5f593          	andi	a1,a1,255
    1022:	0807f793          	andi	a5,a5,128
    1026:	00b55733          	srl	a4,a0,a1
    102a:	e781                	bnez	a5,1032 <SystemCoreClockUpdate+0x5a>
    102c:	8a8ff0ef          	jal	ra,d4 <__udivsi3>
    1030:	872a                	mv	a4,a0
    1032:	c018                	sw	a4,0(s0)
    1034:	876ff06f          	j	aa <__riscv_restore_0>
    1038:	435c                	lw	a5,4(a4)
    103a:	02dc77b7          	lui	a5,0x2dc7
    103e:	c0078793          	addi	a5,a5,-1024 # 2dc6c00 <_data_lma+0x2dc43d4>
    1042:	bf7d                	j	1000 <SystemCoreClockUpdate+0x28>

00001044 <Touch_Button_GPIO_Config>:
    1044:	85cff2ef          	jal	t0,a0 <__riscv_save_0>
    1048:	1151                	addi	sp,sp,-12
    104a:	4585                	li	a1,1
    104c:	02000513          	li	a0,32
    1050:	c002                	sw	zero,0(sp)
    1052:	c202                	sw	zero,4(sp)
    1054:	c402                	sw	zero,8(sp)
    1056:	252d                	jal	1680 <RCC_APB2PeriphClockCmd>
    1058:	4785                	li	a5,1
    105a:	40011537          	lui	a0,0x40011
    105e:	807c                	sh	a5,0(sp)
    1060:	858a                	mv	a1,sp
    1062:	04800793          	li	a5,72
    1066:	40050513          	addi	a0,a0,1024 # 40011400 <__global_pointer$+0x20010bc0>
    106a:	c43e                	sw	a5,8(sp)
    106c:	2981                	jal	14bc <GPIO_Init>
    106e:	0131                	addi	sp,sp,12
    1070:	83aff06f          	j	aa <__riscv_restore_0>

00001074 <Touch_Button_Timer_Init>:
    1074:	82cff2ef          	jal	t0,a0 <__riscv_save_0>
    1078:	1131                	addi	sp,sp,-20
    107a:	4585                	li	a1,1
    107c:	4505                	li	a0,1
    107e:	c402                	sw	zero,8(sp)
    1080:	c602                	sw	zero,12(sp)
    1082:	00011823          	sh	zero,16(sp)
    1086:	c002                	sw	zero,0(sp)
    1088:	c202                	sw	zero,4(sp)
    108a:	2d11                	jal	169e <RCC_APB1PeriphClockCmd>
    108c:	02f00793          	li	a5,47
    1090:	c43e                	sw	a5,8(sp)
    1092:	002c                	addi	a1,sp,8
    1094:	3e700793          	li	a5,999
    1098:	40000537          	lui	a0,0x40000
    109c:	c63e                	sw	a5,12(sp)
    109e:	2d39                	jal	16bc <TIM_TimeBaseInit>
    10a0:	4605                	li	a2,1
    10a2:	4585                	li	a1,1
    10a4:	40000537          	lui	a0,0x40000
    10a8:	2ded                	jal	17a2 <TIM_ITConfig>
    10aa:	22600793          	li	a5,550
    10ae:	807c                	sh	a5,0(sp)
    10b0:	850a                	mv	a0,sp
    10b2:	4785                	li	a5,1
    10b4:	c23e                	sw	a5,4(sp)
    10b6:	00010123          	sb	zero,2(sp)
    10ba:	2945                	jal	156a <NVIC_Init>
    10bc:	4585                	li	a1,1
    10be:	40000537          	lui	a0,0x40000
    10c2:	2d4d                	jal	1774 <TIM_Cmd>
    10c4:	0151                	addi	sp,sp,20
    10c6:	fe5fe06f          	j	aa <__riscv_restore_0>

000010ca <Touch_Button_EXTI_Config>:
    10ca:	fd7fe2ef          	jal	t0,a0 <__riscv_save_0>
    10ce:	1121                	addi	sp,sp,-24
    10d0:	4585                	li	a1,1
    10d2:	4505                	li	a0,1
    10d4:	c402                	sw	zero,8(sp)
    10d6:	c602                	sw	zero,12(sp)
    10d8:	c802                	sw	zero,16(sp)
    10da:	ca02                	sw	zero,20(sp)
    10dc:	c002                	sw	zero,0(sp)
    10de:	c202                	sw	zero,4(sp)
    10e0:	2345                	jal	1680 <RCC_APB2PeriphClockCmd>
    10e2:	4581                	li	a1,0
    10e4:	450d                	li	a0,3
    10e6:	29b1                	jal	1542 <GPIO_EXTILineConfig>
    10e8:	4405                	li	s0,1
    10ea:	47c1                	li	a5,16
    10ec:	0028                	addi	a0,sp,8
    10ee:	c422                	sw	s0,8(sp)
    10f0:	c83e                	sw	a5,16(sp)
    10f2:	ca22                	sw	s0,20(sp)
    10f4:	c602                	sw	zero,12(sp)
    10f6:	2e15                	jal	142a <EXTI_Init>
    10f8:	11400793          	li	a5,276
    10fc:	850a                	mv	a0,sp
    10fe:	807c                	sh	a5,0(sp)
    1100:	8140                	sb	s0,2(sp)
    1102:	c222                	sw	s0,4(sp)
    1104:	219d                	jal	156a <NVIC_Init>
    1106:	0161                	addi	sp,sp,24
    1108:	fa3fe06f          	j	aa <__riscv_restore_0>

0000110c <Touch_Button_Init>:
    110c:	f95fe2ef          	jal	t0,a0 <__riscv_save_0>
    1110:	99018793          	addi	a5,gp,-1648 # 200001d0 <touch_button>
    1114:	00079823          	sh	zero,16(a5)
    1118:	0007a023          	sw	zero,0(a5)
    111c:	0007a223          	sw	zero,4(a5)
    1120:	0007a423          	sw	zero,8(a5)
    1124:	0007a623          	sw	zero,12(a5)
    1128:	00078923          	sb	zero,18(a5)
    112c:	3f21                	jal	1044 <Touch_Button_GPIO_Config>
    112e:	3f71                	jal	10ca <Touch_Button_EXTI_Config>
    1130:	3791                	jal	1074 <Touch_Button_Timer_Init>
    1132:	f79fe06f          	j	aa <__riscv_restore_0>

00001136 <Touch_Button_Update>:
    1136:	98c1a683          	lw	a3,-1652(gp) # 200001cc <system_tick_ms>
    113a:	99018793          	addi	a5,gp,-1648 # 200001d0 <touch_button>
    113e:	4790                	lw	a2,8(a5)
    1140:	438c                	lw	a1,0(a5)
    1142:	4505                	li	a0,1
    1144:	40c68633          	sub	a2,a3,a2
    1148:	99018713          	addi	a4,gp,-1648 # 200001d0 <touch_button>
    114c:	02a58663          	beq	a1,a0,1178 <Touch_Button_Update+0x42>
    1150:	c589                	beqz	a1,115a <Touch_Button_Update+0x24>
    1152:	478d                	li	a5,3
    1154:	04f58063          	beq	a1,a5,1194 <Touch_Button_Update+0x5e>
    1158:	8082                	ret
    115a:	3b98                	lbu	a4,17(a5)
    115c:	c729                	beqz	a4,11a6 <Touch_Button_Update+0x70>
    115e:	2bb8                	lbu	a4,18(a5)
    1160:	e339                	bnez	a4,11a6 <Touch_Button_Update+0x70>
    1162:	47d8                	lw	a4,12(a5)
    1164:	8e99                	sub	a3,a3,a4
    1166:	7cf00713          	li	a4,1999
    116a:	02d77e63          	bgeu	a4,a3,11a6 <Touch_Button_Update+0x70>
    116e:	470d                	li	a4,3
    1170:	000788a3          	sb	zero,17(a5)
    1174:	c3d8                	sw	a4,4(a5)
    1176:	8082                	ret
    1178:	3e700713          	li	a4,999
    117c:	02c77563          	bgeu	a4,a2,11a6 <Touch_Button_Update+0x70>
    1180:	4709                	li	a4,2
    1182:	c398                	sw	a4,0(a5)
    1184:	c3d8                	sw	a4,4(a5)
    1186:	2bb8                	lbu	a4,18(a5)
    1188:	bb8c                	sb	a1,17(a5)
    118a:	c7d4                	sw	a3,12(a5)
    118c:	00173713          	seqz	a4,a4
    1190:	abb8                	sb	a4,18(a5)
    1192:	8082                	ret
    1194:	3e700793          	li	a5,999
    1198:	00c7e563          	bltu	a5,a2,11a2 <Touch_Button_Update+0x6c>
    119c:	c348                	sw	a0,4(a4)
    119e:	bb08                	sb	a0,17(a4)
    11a0:	c754                	sw	a3,12(a4)
    11a2:	00072023          	sw	zero,0(a4) # 40021000 <__global_pointer$+0x200207c0>
    11a6:	8082                	ret

000011a8 <Touch_Button_Get_Event>:
    11a8:	99018793          	addi	a5,gp,-1648 # 200001d0 <touch_button>
    11ac:	43c8                	lw	a0,4(a5)
    11ae:	0007a223          	sw	zero,4(a5)
    11b2:	8082                	ret

000011b4 <Touch_Button_Get_Time_Ms>:
    11b4:	98c1a503          	lw	a0,-1652(gp) # 200001cc <system_tick_ms>
    11b8:	8082                	ret

000011ba <Touch_Button_IRQ_Handler>:
    11ba:	ee7fe2ef          	jal	t0,a0 <__riscv_save_0>
    11be:	4505                	li	a0,1
    11c0:	2cd1                	jal	1494 <EXTI_GetITStatus>
    11c2:	c505                	beqz	a0,11ea <Touch_Button_IRQ_Handler+0x30>
    11c4:	40011537          	lui	a0,0x40011
    11c8:	4585                	li	a1,1
    11ca:	40050513          	addi	a0,a0,1024 # 40011400 <__global_pointer$+0x20010bc0>
    11ce:	98c1a403          	lw	s0,-1652(gp) # 200001cc <system_tick_ms>
    11d2:	269d                	jal	1538 <GPIO_ReadInputDataBit>
    11d4:	99018793          	addi	a5,gp,-1648 # 200001d0 <touch_button>
    11d8:	4398                	lw	a4,0(a5)
    11da:	c911                	beqz	a0,11ee <Touch_Button_IRQ_Handler+0x34>
    11dc:	e709                	bnez	a4,11e6 <Touch_Button_IRQ_Handler+0x2c>
    11de:	4705                	li	a4,1
    11e0:	c398                	sw	a4,0(a5)
    11e2:	c780                	sw	s0,8(a5)
    11e4:	ab98                	sb	a4,16(a5)
    11e6:	4505                	li	a0,1
    11e8:	24e9                	jal	14b2 <EXTI_ClearITPendingBit>
    11ea:	ec1fe06f          	j	aa <__riscv_restore_0>
    11ee:	177d                	addi	a4,a4,-1
    11f0:	4685                	li	a3,1
    11f2:	fee6eae3          	bltu	a3,a4,11e6 <Touch_Button_IRQ_Handler+0x2c>
    11f6:	470d                	li	a4,3
    11f8:	c398                	sw	a4,0(a5)
    11fa:	00078823          	sb	zero,16(a5)
    11fe:	b7e5                	j	11e6 <Touch_Button_IRQ_Handler+0x2c>

00001200 <ADC1_IRQHandler>:
    1200:	a001                	j	1200 <ADC1_IRQHandler>

00001202 <handle_reset>:
    1202:	1ffff197          	auipc	gp,0x1ffff
    1206:	63e18193          	addi	gp,gp,1598 # 20000840 <__global_pointer$>
    120a:	fc018113          	addi	sp,gp,-64 # 20000800 <_eusrstack>
    120e:	0a000513          	li	a0,160
    1212:	1ffff597          	auipc	a1,0x1ffff
    1216:	dee58593          	addi	a1,a1,-530 # 20000000 <_highcode_vma_end>
    121a:	1ffff617          	auipc	a2,0x1ffff
    121e:	de660613          	addi	a2,a2,-538 # 20000000 <_highcode_vma_end>
    1222:	00c5fa63          	bgeu	a1,a2,1236 <handle_reset+0x34>
    1226:	00052283          	lw	t0,0(a0)
    122a:	0055a023          	sw	t0,0(a1)
    122e:	0511                	addi	a0,a0,4
    1230:	0591                	addi	a1,a1,4
    1232:	fec5eae3          	bltu	a1,a2,1226 <handle_reset+0x24>
    1236:	00001517          	auipc	a0,0x1
    123a:	5f650513          	addi	a0,a0,1526 # 282c <_data_lma>
    123e:	1ffff597          	auipc	a1,0x1ffff
    1242:	dc258593          	addi	a1,a1,-574 # 20000000 <_highcode_vma_end>
    1246:	1ffff617          	auipc	a2,0x1ffff
    124a:	dfa60613          	addi	a2,a2,-518 # 20000040 <_edata>
    124e:	00c5fa63          	bgeu	a1,a2,1262 <handle_reset+0x60>
    1252:	00052283          	lw	t0,0(a0)
    1256:	0055a023          	sw	t0,0(a1)
    125a:	0511                	addi	a0,a0,4
    125c:	0591                	addi	a1,a1,4
    125e:	fec5eae3          	bltu	a1,a2,1252 <handle_reset+0x50>
    1262:	1ffff517          	auipc	a0,0x1ffff
    1266:	dde50513          	addi	a0,a0,-546 # 20000040 <_edata>
    126a:	9ac18593          	addi	a1,gp,-1620 # 200001ec <_ebss>
    126e:	00b57763          	bgeu	a0,a1,127c <handle_reset+0x7a>
    1272:	00052023          	sw	zero,0(a0)
    1276:	0511                	addi	a0,a0,4
    1278:	feb56de3          	bltu	a0,a1,1272 <handle_reset+0x70>
    127c:	000022b7          	lui	t0,0x2
    1280:	88028293          	addi	t0,t0,-1920 # 1880 <USART_Init+0x7c>
    1284:	30029073          	csrw	mstatus,t0
    1288:	428d                	li	t0,3
    128a:	80429073          	csrw	0x804,t0
    128e:	fffff297          	auipc	t0,0xfffff
    1292:	d7228293          	addi	t0,t0,-654 # 0 <_sinit>
    1296:	0032e293          	ori	t0,t0,3
    129a:	30529073          	csrw	mtvec,t0
    129e:	c07ff0ef          	jal	ra,ea4 <SystemInit>
    12a2:	fffff297          	auipc	t0,0xfffff
    12a6:	4ee28293          	addi	t0,t0,1262 # 790 <main>
    12aa:	34129073          	csrw	mepc,t0
    12ae:	30200073          	mret

000012b2 <ADC_Init>:
    12b2:	415c                	lw	a5,4(a0)
    12b4:	fff10737          	lui	a4,0xfff10
    12b8:	eff70713          	addi	a4,a4,-257 # fff0feff <__global_pointer$+0xdff0f6bf>
    12bc:	8f7d                	and	a4,a4,a5
    12be:	41dc                	lw	a5,4(a1)
    12c0:	4194                	lw	a3,0(a1)
    12c2:	07a2                	slli	a5,a5,0x8
    12c4:	8fd5                	or	a5,a5,a3
    12c6:	8fd9                	or	a5,a5,a4
    12c8:	c15c                	sw	a5,4(a0)
    12ca:	451c                	lw	a5,8(a0)
    12cc:	fff1f737          	lui	a4,0xfff1f
    12d0:	7fd70713          	addi	a4,a4,2045 # fff1f7fd <__global_pointer$+0xdff1efbd>
    12d4:	45d4                	lw	a3,12(a1)
    12d6:	8f7d                	and	a4,a4,a5
    12d8:	499c                	lw	a5,16(a1)
    12da:	8fd5                	or	a5,a5,a3
    12dc:	4594                	lw	a3,8(a1)
    12de:	0686                	slli	a3,a3,0x1
    12e0:	8fd5                	or	a5,a5,a3
    12e2:	8fd9                	or	a5,a5,a4
    12e4:	c51c                	sw	a5,8(a0)
    12e6:	555c                	lw	a5,44(a0)
    12e8:	ff100737          	lui	a4,0xff100
    12ec:	177d                	addi	a4,a4,-1
    12ee:	8f7d                	and	a4,a4,a5
    12f0:	29dc                	lbu	a5,20(a1)
    12f2:	17fd                	addi	a5,a5,-1
    12f4:	0ff7f793          	andi	a5,a5,255
    12f8:	07d2                	slli	a5,a5,0x14
    12fa:	8fd9                	or	a5,a5,a4
    12fc:	d55c                	sw	a5,44(a0)
    12fe:	8082                	ret

00001300 <ADC_Cmd>:
    1300:	451c                	lw	a5,8(a0)
    1302:	c589                	beqz	a1,130c <ADC_Cmd+0xc>
    1304:	0017e793          	ori	a5,a5,1
    1308:	c51c                	sw	a5,8(a0)
    130a:	8082                	ret
    130c:	9bf9                	andi	a5,a5,-2
    130e:	bfed                	j	1308 <ADC_Cmd+0x8>

00001310 <ADC_ResetCalibration>:
    1310:	451c                	lw	a5,8(a0)
    1312:	0087e793          	ori	a5,a5,8
    1316:	c51c                	sw	a5,8(a0)
    1318:	8082                	ret

0000131a <ADC_GetResetCalibrationStatus>:
    131a:	4508                	lw	a0,8(a0)
    131c:	810d                	srli	a0,a0,0x3
    131e:	8905                	andi	a0,a0,1
    1320:	8082                	ret

00001322 <ADC_StartCalibration>:
    1322:	451c                	lw	a5,8(a0)
    1324:	0047e793          	ori	a5,a5,4
    1328:	c51c                	sw	a5,8(a0)
    132a:	8082                	ret

0000132c <ADC_GetCalibrationStatus>:
    132c:	4508                	lw	a0,8(a0)
    132e:	8109                	srli	a0,a0,0x2
    1330:	8905                	andi	a0,a0,1
    1332:	8082                	ret

00001334 <ADC_SoftwareStartConvCmd>:
    1334:	451c                	lw	a5,8(a0)
    1336:	c591                	beqz	a1,1342 <ADC_SoftwareStartConvCmd+0xe>
    1338:	00500737          	lui	a4,0x500
    133c:	8fd9                	or	a5,a5,a4
    133e:	c51c                	sw	a5,8(a0)
    1340:	8082                	ret
    1342:	ffb00737          	lui	a4,0xffb00
    1346:	177d                	addi	a4,a4,-1
    1348:	8ff9                	and	a5,a5,a4
    134a:	bfd5                	j	133e <ADC_SoftwareStartConvCmd+0xa>

0000134c <ADC_RegularChannelConfig>:
    134c:	47a5                	li	a5,9
    134e:	04b7f863          	bgeu	a5,a1,139e <ADC_RegularChannelConfig+0x52>
    1352:	ff658713          	addi	a4,a1,-10
    1356:	00171793          	slli	a5,a4,0x1
    135a:	00c52283          	lw	t0,12(a0)
    135e:	97ba                	add	a5,a5,a4
    1360:	431d                	li	t1,7
    1362:	00f31333          	sll	t1,t1,a5
    1366:	fff34313          	not	t1,t1
    136a:	00537333          	and	t1,t1,t0
    136e:	00f697b3          	sll	a5,a3,a5
    1372:	0067e7b3          	or	a5,a5,t1
    1376:	c55c                	sw	a5,12(a0)
    1378:	4799                	li	a5,6
    137a:	04c7e363          	bltu	a5,a2,13c0 <ADC_RegularChannelConfig+0x74>
    137e:	167d                	addi	a2,a2,-1
    1380:	00261793          	slli	a5,a2,0x2
    1384:	963e                	add	a2,a2,a5
    1386:	5958                	lw	a4,52(a0)
    1388:	47fd                	li	a5,31
    138a:	00c797b3          	sll	a5,a5,a2
    138e:	fff7c793          	not	a5,a5
    1392:	8ff9                	and	a5,a5,a4
    1394:	00c595b3          	sll	a1,a1,a2
    1398:	8ddd                	or	a1,a1,a5
    139a:	d94c                	sw	a1,52(a0)
    139c:	8082                	ret
    139e:	00159793          	slli	a5,a1,0x1
    13a2:	01052303          	lw	t1,16(a0)
    13a6:	97ae                	add	a5,a5,a1
    13a8:	471d                	li	a4,7
    13aa:	00f71733          	sll	a4,a4,a5
    13ae:	fff74713          	not	a4,a4
    13b2:	00677733          	and	a4,a4,t1
    13b6:	00f697b3          	sll	a5,a3,a5
    13ba:	8f5d                	or	a4,a4,a5
    13bc:	c918                	sw	a4,16(a0)
    13be:	bf6d                	j	1378 <ADC_RegularChannelConfig+0x2c>
    13c0:	47b1                	li	a5,12
    13c2:	02c7e263          	bltu	a5,a2,13e6 <ADC_RegularChannelConfig+0x9a>
    13c6:	1665                	addi	a2,a2,-7
    13c8:	00261793          	slli	a5,a2,0x2
    13cc:	963e                	add	a2,a2,a5
    13ce:	5918                	lw	a4,48(a0)
    13d0:	47fd                	li	a5,31
    13d2:	00c797b3          	sll	a5,a5,a2
    13d6:	fff7c793          	not	a5,a5
    13da:	8ff9                	and	a5,a5,a4
    13dc:	00c595b3          	sll	a1,a1,a2
    13e0:	8ddd                	or	a1,a1,a5
    13e2:	d90c                	sw	a1,48(a0)
    13e4:	8082                	ret
    13e6:	164d                	addi	a2,a2,-13
    13e8:	00261713          	slli	a4,a2,0x2
    13ec:	5554                	lw	a3,44(a0)
    13ee:	963a                	add	a2,a2,a4
    13f0:	47fd                	li	a5,31
    13f2:	00c797b3          	sll	a5,a5,a2
    13f6:	fff7c793          	not	a5,a5
    13fa:	8ff5                	and	a5,a5,a3
    13fc:	00c595b3          	sll	a1,a1,a2
    1400:	8ddd                	or	a1,a1,a5
    1402:	d54c                	sw	a1,44(a0)
    1404:	8082                	ret

00001406 <ADC_GetConversionValue>:
    1406:	4568                	lw	a0,76(a0)
    1408:	0542                	slli	a0,a0,0x10
    140a:	8141                	srli	a0,a0,0x10
    140c:	8082                	ret

0000140e <ADC_GetFlagStatus>:
    140e:	4108                	lw	a0,0(a0)
    1410:	8d6d                	and	a0,a0,a1
    1412:	00a03533          	snez	a0,a0
    1416:	8082                	ret

00001418 <ADC_ClearFlag>:
    1418:	fff5c593          	not	a1,a1
    141c:	c10c                	sw	a1,0(a0)
    141e:	8082                	ret

00001420 <DBGMCU_GetCHIPID>:
    1420:	1ffff7b7          	lui	a5,0x1ffff
    1424:	7c47a503          	lw	a0,1988(a5) # 1ffff7c4 <_data_lma+0x1fffcf98>
    1428:	8082                	ret

0000142a <EXTI_Init>:
    142a:	4158                	lw	a4,4(a0)
    142c:	00052303          	lw	t1,0(a0)
    1430:	454c                	lw	a1,12(a0)
    1432:	40010637          	lui	a2,0x40010
    1436:	40060793          	addi	a5,a2,1024 # 40010400 <__global_pointer$+0x2000fbc0>
    143a:	973e                	add	a4,a4,a5
    143c:	fff34693          	not	a3,t1
    1440:	c5b1                	beqz	a1,148c <EXTI_Init+0x62>
    1442:	40062583          	lw	a1,1024(a2)
    1446:	8df5                	and	a1,a1,a3
    1448:	40b62023          	sw	a1,1024(a2)
    144c:	43d0                	lw	a2,4(a5)
    144e:	8ef1                	and	a3,a3,a2
    1450:	c3d4                	sw	a3,4(a5)
    1452:	4314                	lw	a3,0(a4)
    1454:	0066e6b3          	or	a3,a3,t1
    1458:	c314                	sw	a3,0(a4)
    145a:	4118                	lw	a4,0(a0)
    145c:	4790                	lw	a2,8(a5)
    145e:	fff74693          	not	a3,a4
    1462:	8e75                	and	a2,a2,a3
    1464:	c790                	sw	a2,8(a5)
    1466:	47d0                	lw	a2,12(a5)
    1468:	8ef1                	and	a3,a3,a2
    146a:	c7d4                	sw	a3,12(a5)
    146c:	4514                	lw	a3,8(a0)
    146e:	4641                	li	a2,16
    1470:	00c69963          	bne	a3,a2,1482 <EXTI_Init+0x58>
    1474:	4794                	lw	a3,8(a5)
    1476:	8ed9                	or	a3,a3,a4
    1478:	c794                	sw	a3,8(a5)
    147a:	47d4                	lw	a3,12(a5)
    147c:	8f55                	or	a4,a4,a3
    147e:	c7d8                	sw	a4,12(a5)
    1480:	8082                	ret
    1482:	97b6                	add	a5,a5,a3
    1484:	4394                	lw	a3,0(a5)
    1486:	8f55                	or	a4,a4,a3
    1488:	c398                	sw	a4,0(a5)
    148a:	8082                	ret
    148c:	431c                	lw	a5,0(a4)
    148e:	8ff5                	and	a5,a5,a3
    1490:	c31c                	sw	a5,0(a4)
    1492:	8082                	ret

00001494 <EXTI_GetITStatus>:
    1494:	400107b7          	lui	a5,0x40010
    1498:	40078713          	addi	a4,a5,1024 # 40010400 <__global_pointer$+0x2000fbc0>
    149c:	4007a783          	lw	a5,1024(a5)
    14a0:	4b58                	lw	a4,20(a4)
    14a2:	8f69                	and	a4,a4,a0
    14a4:	c709                	beqz	a4,14ae <EXTI_GetITStatus+0x1a>
    14a6:	8d7d                	and	a0,a0,a5
    14a8:	00a03533          	snez	a0,a0
    14ac:	8082                	ret
    14ae:	4501                	li	a0,0
    14b0:	8082                	ret

000014b2 <EXTI_ClearITPendingBit>:
    14b2:	400107b7          	lui	a5,0x40010
    14b6:	40a7aa23          	sw	a0,1044(a5) # 40010414 <__global_pointer$+0x2000fbd4>
    14ba:	8082                	ret

000014bc <GPIO_Init>:
    14bc:	4594                	lw	a3,8(a1)
    14be:	0106f793          	andi	a5,a3,16
    14c2:	00f6f293          	andi	t0,a3,15
    14c6:	c781                	beqz	a5,14ce <GPIO_Init+0x12>
    14c8:	41dc                	lw	a5,4(a1)
    14ca:	00f2e2b3          	or	t0,t0,a5
    14ce:	0005d383          	lhu	t2,0(a1)
    14d2:	0ff3f793          	andi	a5,t2,255
    14d6:	c3a5                	beqz	a5,1536 <GPIO_Init+0x7a>
    14d8:	00052303          	lw	t1,0(a0)
    14dc:	1161                	addi	sp,sp,-8
    14de:	c222                	sw	s0,4(sp)
    14e0:	c026                	sw	s1,0(sp)
    14e2:	4781                	li	a5,0
    14e4:	02800413          	li	s0,40
    14e8:	04800493          	li	s1,72
    14ec:	4705                	li	a4,1
    14ee:	00f71633          	sll	a2,a4,a5
    14f2:	00c3f733          	and	a4,t2,a2
    14f6:	02e61263          	bne	a2,a4,151a <GPIO_Init+0x5e>
    14fa:	00279593          	slli	a1,a5,0x2
    14fe:	473d                	li	a4,15
    1500:	00b71733          	sll	a4,a4,a1
    1504:	fff74713          	not	a4,a4
    1508:	00677333          	and	t1,a4,t1
    150c:	00b295b3          	sll	a1,t0,a1
    1510:	0065e333          	or	t1,a1,t1
    1514:	00869d63          	bne	a3,s0,152e <GPIO_Init+0x72>
    1518:	c950                	sw	a2,20(a0)
    151a:	0785                	addi	a5,a5,1
    151c:	4721                	li	a4,8
    151e:	fce797e3          	bne	a5,a4,14ec <GPIO_Init+0x30>
    1522:	4412                	lw	s0,4(sp)
    1524:	00652023          	sw	t1,0(a0)
    1528:	4482                	lw	s1,0(sp)
    152a:	0121                	addi	sp,sp,8
    152c:	8082                	ret
    152e:	fe9696e3          	bne	a3,s1,151a <GPIO_Init+0x5e>
    1532:	c910                	sw	a2,16(a0)
    1534:	b7dd                	j	151a <GPIO_Init+0x5e>
    1536:	8082                	ret

00001538 <GPIO_ReadInputDataBit>:
    1538:	4508                	lw	a0,8(a0)
    153a:	8d6d                	and	a0,a0,a1
    153c:	00a03533          	snez	a0,a0
    1540:	8082                	ret

00001542 <GPIO_EXTILineConfig>:
    1542:	40010737          	lui	a4,0x40010
    1546:	4714                	lw	a3,8(a4)
    1548:	0586                	slli	a1,a1,0x1
    154a:	478d                	li	a5,3
    154c:	00b797b3          	sll	a5,a5,a1
    1550:	fff7c793          	not	a5,a5
    1554:	8ff5                	and	a5,a5,a3
    1556:	c71c                	sw	a5,8(a4)
    1558:	471c                	lw	a5,8(a4)
    155a:	00b515b3          	sll	a1,a0,a1
    155e:	8ddd                	or	a1,a1,a5
    1560:	c70c                	sw	a1,8(a4)
    1562:	8082                	ret

00001564 <NVIC_PriorityGroupConfig>:
    1564:	9aa1a223          	sw	a0,-1628(gp) # 200001e4 <NVIC_Priority_Group>
    1568:	8082                	ret

0000156a <NVIC_Init>:
    156a:	9a41a683          	lw	a3,-1628(gp) # 200001e4 <NVIC_Priority_Group>
    156e:	4785                	li	a5,1
    1570:	2118                	lbu	a4,0(a0)
    1572:	02f69063          	bne	a3,a5,1592 <NVIC_Init+0x28>
    1576:	311c                	lbu	a5,1(a0)
    1578:	02d79c63          	bne	a5,a3,15b0 <NVIC_Init+0x46>
    157c:	213c                	lbu	a5,2(a0)
    157e:	079a                	slli	a5,a5,0x6
    1580:	f807e793          	ori	a5,a5,-128
    1584:	e000e6b7          	lui	a3,0xe000e
    1588:	0ff7f793          	andi	a5,a5,255
    158c:	96ba                	add	a3,a3,a4
    158e:	40f68023          	sb	a5,1024(a3) # e000e400 <__global_pointer$+0xc000dbc0>
    1592:	4685                	li	a3,1
    1594:	00575793          	srli	a5,a4,0x5
    1598:	00e69733          	sll	a4,a3,a4
    159c:	4154                	lw	a3,4(a0)
    159e:	ce89                	beqz	a3,15b8 <NVIC_Init+0x4e>
    15a0:	04078793          	addi	a5,a5,64
    15a4:	078a                	slli	a5,a5,0x2
    15a6:	e000e6b7          	lui	a3,0xe000e
    15aa:	97b6                	add	a5,a5,a3
    15ac:	c398                	sw	a4,0(a5)
    15ae:	8082                	ret
    15b0:	f3ed                	bnez	a5,1592 <NVIC_Init+0x28>
    15b2:	213c                	lbu	a5,2(a0)
    15b4:	079a                	slli	a5,a5,0x6
    15b6:	b7f9                	j	1584 <NVIC_Init+0x1a>
    15b8:	06078793          	addi	a5,a5,96
    15bc:	e000e6b7          	lui	a3,0xe000e
    15c0:	078a                	slli	a5,a5,0x2
    15c2:	97b6                	add	a5,a5,a3
    15c4:	c398                	sw	a4,0(a5)
    15c6:	0000100f          	fence.i
    15ca:	8082                	ret

000015cc <RCC_AdjustHSICalibrationValue>:
    15cc:	40021737          	lui	a4,0x40021
    15d0:	431c                	lw	a5,0(a4)
    15d2:	050e                	slli	a0,a0,0x3
    15d4:	f077f793          	andi	a5,a5,-249
    15d8:	8d5d                	or	a0,a0,a5
    15da:	c308                	sw	a0,0(a4)
    15dc:	8082                	ret

000015de <RCC_GetClocksFreq>:
    15de:	ac3fe2ef          	jal	t0,a0 <__riscv_save_0>
    15e2:	40021737          	lui	a4,0x40021
    15e6:	435c                	lw	a5,4(a4)
    15e8:	4691                	li	a3,4
    15ea:	842a                	mv	s0,a0
    15ec:	8bb1                	andi	a5,a5,12
    15ee:	00d78563          	beq	a5,a3,15f8 <RCC_GetClocksFreq+0x1a>
    15f2:	46a1                	li	a3,8
    15f4:	08d78063          	beq	a5,a3,1674 <RCC_GetClocksFreq+0x96>
    15f8:	016e37b7          	lui	a5,0x16e3
    15fc:	60078793          	addi	a5,a5,1536 # 16e3600 <_data_lma+0x16e0dd4>
    1600:	c01c                	sw	a5,0(s0)
    1602:	400216b7          	lui	a3,0x40021
    1606:	42dc                	lw	a5,4(a3)
    1608:	8391                	srli	a5,a5,0x4
    160a:	00f7f713          	andi	a4,a5,15
    160e:	200007b7          	lui	a5,0x20000
    1612:	02c78793          	addi	a5,a5,44 # 2000002c <APBAHBPrescTable>
    1616:	97ba                	add	a5,a5,a4
    1618:	238c                	lbu	a1,0(a5)
    161a:	42dc                	lw	a5,4(a3)
    161c:	4018                	lw	a4,0(s0)
    161e:	0ff5f593          	andi	a1,a1,255
    1622:	0807f793          	andi	a5,a5,128
    1626:	00b75533          	srl	a0,a4,a1
    162a:	e781                	bnez	a5,1632 <RCC_GetClocksFreq+0x54>
    162c:	853a                	mv	a0,a4
    162e:	aa7fe0ef          	jal	ra,d4 <__udivsi3>
    1632:	c048                	sw	a0,4(s0)
    1634:	c408                	sw	a0,8(s0)
    1636:	c448                	sw	a0,12(s0)
    1638:	400217b7          	lui	a5,0x40021
    163c:	43dc                	lw	a5,4(a5)
    163e:	468d                	li	a3,3
    1640:	83ad                	srli	a5,a5,0xb
    1642:	8bfd                	andi	a5,a5,31
    1644:	0037d713          	srli	a4,a5,0x3
    1648:	078a                	slli	a5,a5,0x2
    164a:	8bf1                	andi	a5,a5,28
    164c:	8fd9                	or	a5,a5,a4
    164e:	0137f613          	andi	a2,a5,19
    1652:	0037f713          	andi	a4,a5,3
    1656:	00c6f463          	bgeu	a3,a2,165e <RCC_GetClocksFreq+0x80>
    165a:	ff478713          	addi	a4,a5,-12 # 40020ff4 <__global_pointer$+0x200207b4>
    165e:	200007b7          	lui	a5,0x20000
    1662:	01878793          	addi	a5,a5,24 # 20000018 <ADCPrescTable>
    1666:	97ba                	add	a5,a5,a4
    1668:	238c                	lbu	a1,0(a5)
    166a:	a6bfe0ef          	jal	ra,d4 <__udivsi3>
    166e:	c808                	sw	a0,16(s0)
    1670:	a3bfe06f          	j	aa <__riscv_restore_0>
    1674:	435c                	lw	a5,4(a4)
    1676:	02dc77b7          	lui	a5,0x2dc7
    167a:	c0078793          	addi	a5,a5,-1024 # 2dc6c00 <_data_lma+0x2dc43d4>
    167e:	b749                	j	1600 <RCC_GetClocksFreq+0x22>

00001680 <RCC_APB2PeriphClockCmd>:
    1680:	c599                	beqz	a1,168e <RCC_APB2PeriphClockCmd+0xe>
    1682:	40021737          	lui	a4,0x40021
    1686:	4f1c                	lw	a5,24(a4)
    1688:	8d5d                	or	a0,a0,a5
    168a:	cf08                	sw	a0,24(a4)
    168c:	8082                	ret
    168e:	400217b7          	lui	a5,0x40021
    1692:	4f98                	lw	a4,24(a5)
    1694:	fff54513          	not	a0,a0
    1698:	8d79                	and	a0,a0,a4
    169a:	cf88                	sw	a0,24(a5)
    169c:	8082                	ret

0000169e <RCC_APB1PeriphClockCmd>:
    169e:	c599                	beqz	a1,16ac <RCC_APB1PeriphClockCmd+0xe>
    16a0:	40021737          	lui	a4,0x40021
    16a4:	4f5c                	lw	a5,28(a4)
    16a6:	8d5d                	or	a0,a0,a5
    16a8:	cf48                	sw	a0,28(a4)
    16aa:	8082                	ret
    16ac:	400217b7          	lui	a5,0x40021
    16b0:	4fd8                	lw	a4,28(a5)
    16b2:	fff54513          	not	a0,a0
    16b6:	8d79                	and	a0,a0,a4
    16b8:	cfc8                	sw	a0,28(a5)
    16ba:	8082                	ret

000016bc <TIM_TimeBaseInit>:
    16bc:	211e                	lhu	a5,0(a0)
    16be:	40013737          	lui	a4,0x40013
    16c2:	c0070713          	addi	a4,a4,-1024 # 40012c00 <__global_pointer$+0x200123c0>
    16c6:	07c2                	slli	a5,a5,0x10
    16c8:	83c1                	srli	a5,a5,0x10
    16ca:	00e50663          	beq	a0,a4,16d6 <TIM_TimeBaseInit+0x1a>
    16ce:	40000737          	lui	a4,0x40000
    16d2:	00e51663          	bne	a0,a4,16de <TIM_TimeBaseInit+0x22>
    16d6:	21ba                	lhu	a4,2(a1)
    16d8:	f8f7f793          	andi	a5,a5,-113
    16dc:	8fd9                	or	a5,a5,a4
    16de:	21fa                	lhu	a4,6(a1)
    16e0:	cff7f793          	andi	a5,a5,-769
    16e4:	07c2                	slli	a5,a5,0x10
    16e6:	83c1                	srli	a5,a5,0x10
    16e8:	8fd9                	or	a5,a5,a4
    16ea:	a11e                	sh	a5,0(a0)
    16ec:	21de                	lhu	a5,4(a1)
    16ee:	b55e                	sh	a5,44(a0)
    16f0:	219e                	lhu	a5,0(a1)
    16f2:	b51e                	sh	a5,40(a0)
    16f4:	400137b7          	lui	a5,0x40013
    16f8:	c0078793          	addi	a5,a5,-1024 # 40012c00 <__global_pointer$+0x200123c0>
    16fc:	00f51463          	bne	a0,a5,1704 <TIM_TimeBaseInit+0x48>
    1700:	259c                	lbu	a5,8(a1)
    1702:	b91e                	sh	a5,48(a0)
    1704:	4785                	li	a5,1
    1706:	a95e                	sh	a5,20(a0)
    1708:	8082                	ret

0000170a <TIM_OC1Init>:
    170a:	311e                	lhu	a5,32(a0)
    170c:	2192                	lhu	a2,0(a1)
    170e:	0025d303          	lhu	t1,2(a1)
    1712:	07c2                	slli	a5,a5,0x10
    1714:	83c1                	srli	a5,a5,0x10
    1716:	9bf9                	andi	a5,a5,-2
    1718:	07c2                	slli	a5,a5,0x10
    171a:	83c1                	srli	a5,a5,0x10
    171c:	b11e                	sh	a5,32(a0)
    171e:	311e                	lhu	a5,32(a0)
    1720:	2156                	lhu	a3,4(a0)
    1722:	2d1a                	lhu	a4,24(a0)
    1724:	07c2                	slli	a5,a5,0x10
    1726:	83c1                	srli	a5,a5,0x10
    1728:	0742                	slli	a4,a4,0x10
    172a:	8341                	srli	a4,a4,0x10
    172c:	f8c77713          	andi	a4,a4,-116
    1730:	8f51                	or	a4,a4,a2
    1732:	2592                	lhu	a2,8(a1)
    1734:	9bf5                	andi	a5,a5,-3
    1736:	06c2                	slli	a3,a3,0x10
    1738:	00666633          	or	a2,a2,t1
    173c:	8fd1                	or	a5,a5,a2
    173e:	40013637          	lui	a2,0x40013
    1742:	c0060613          	addi	a2,a2,-1024 # 40012c00 <__global_pointer$+0x200123c0>
    1746:	82c1                	srli	a3,a3,0x10
    1748:	02c51063          	bne	a0,a2,1768 <TIM_OC1Init+0x5e>
    174c:	25b2                	lhu	a2,10(a1)
    174e:	9bdd                	andi	a5,a5,-9
    1750:	00e5d303          	lhu	t1,14(a1)
    1754:	8fd1                	or	a5,a5,a2
    1756:	21d2                	lhu	a2,4(a1)
    1758:	9bed                	andi	a5,a5,-5
    175a:	cff6f693          	andi	a3,a3,-769
    175e:	8fd1                	or	a5,a5,a2
    1760:	25d2                	lhu	a2,12(a1)
    1762:	00666633          	or	a2,a2,t1
    1766:	8ed1                	or	a3,a3,a2
    1768:	a156                	sh	a3,4(a0)
    176a:	ad1a                	sh	a4,24(a0)
    176c:	21fa                	lhu	a4,6(a1)
    176e:	d958                	sw	a4,52(a0)
    1770:	b11e                	sh	a5,32(a0)
    1772:	8082                	ret

00001774 <TIM_Cmd>:
    1774:	211e                	lhu	a5,0(a0)
    1776:	c589                	beqz	a1,1780 <TIM_Cmd+0xc>
    1778:	0017e793          	ori	a5,a5,1
    177c:	a11e                	sh	a5,0(a0)
    177e:	8082                	ret
    1780:	07c2                	slli	a5,a5,0x10
    1782:	83c1                	srli	a5,a5,0x10
    1784:	9bf9                	andi	a5,a5,-2
    1786:	07c2                	slli	a5,a5,0x10
    1788:	83c1                	srli	a5,a5,0x10
    178a:	bfcd                	j	177c <TIM_Cmd+0x8>

0000178c <TIM_CtrlPWMOutputs>:
    178c:	04455783          	lhu	a5,68(a0)
    1790:	c591                	beqz	a1,179c <TIM_CtrlPWMOutputs+0x10>
    1792:	6721                	lui	a4,0x8
    1794:	8fd9                	or	a5,a5,a4
    1796:	04f51223          	sh	a5,68(a0)
    179a:	8082                	ret
    179c:	07c6                	slli	a5,a5,0x11
    179e:	83c5                	srli	a5,a5,0x11
    17a0:	bfdd                	j	1796 <TIM_CtrlPWMOutputs+0xa>

000017a2 <TIM_ITConfig>:
    17a2:	255e                	lhu	a5,12(a0)
    17a4:	c601                	beqz	a2,17ac <TIM_ITConfig+0xa>
    17a6:	8ddd                	or	a1,a1,a5
    17a8:	a54e                	sh	a1,12(a0)
    17aa:	8082                	ret
    17ac:	fff5c593          	not	a1,a1
    17b0:	8dfd                	and	a1,a1,a5
    17b2:	bfdd                	j	17a8 <TIM_ITConfig+0x6>

000017b4 <TIM_ARRPreloadConfig>:
    17b4:	211e                	lhu	a5,0(a0)
    17b6:	c589                	beqz	a1,17c0 <TIM_ARRPreloadConfig+0xc>
    17b8:	0807e793          	ori	a5,a5,128
    17bc:	a11e                	sh	a5,0(a0)
    17be:	8082                	ret
    17c0:	07c2                	slli	a5,a5,0x10
    17c2:	83c1                	srli	a5,a5,0x10
    17c4:	f7f7f793          	andi	a5,a5,-129
    17c8:	07c2                	slli	a5,a5,0x10
    17ca:	83c1                	srli	a5,a5,0x10
    17cc:	bfc5                	j	17bc <TIM_ARRPreloadConfig+0x8>

000017ce <TIM_OC1PreloadConfig>:
    17ce:	2d1e                	lhu	a5,24(a0)
    17d0:	07c2                	slli	a5,a5,0x10
    17d2:	83c1                	srli	a5,a5,0x10
    17d4:	9bdd                	andi	a5,a5,-9
    17d6:	8ddd                	or	a1,a1,a5
    17d8:	ad0e                	sh	a1,24(a0)
    17da:	8082                	ret

000017dc <TIM_SetCompare1>:
    17dc:	d94c                	sw	a1,52(a0)
    17de:	8082                	ret

000017e0 <TIM_GetITStatus>:
    17e0:	291e                	lhu	a5,16(a0)
    17e2:	254a                	lhu	a0,12(a0)
    17e4:	8fed                	and	a5,a5,a1
    17e6:	0542                	slli	a0,a0,0x10
    17e8:	8141                	srli	a0,a0,0x10
    17ea:	c789                	beqz	a5,17f4 <TIM_GetITStatus+0x14>
    17ec:	8d6d                	and	a0,a0,a1
    17ee:	00a03533          	snez	a0,a0
    17f2:	8082                	ret
    17f4:	4501                	li	a0,0
    17f6:	8082                	ret

000017f8 <TIM_ClearITPendingBit>:
    17f8:	fff5c593          	not	a1,a1
    17fc:	05c2                	slli	a1,a1,0x10
    17fe:	81c1                	srli	a1,a1,0x10
    1800:	a90e                	sh	a1,16(a0)
    1802:	8082                	ret

00001804 <USART_Init>:
    1804:	89dfe2ef          	jal	t0,a0 <__riscv_save_0>
    1808:	2916                	lhu	a3,16(a0)
    180a:	77f5                	lui	a5,0xffffd
    180c:	17fd                	addi	a5,a5,-1
    180e:	8ff5                	and	a5,a5,a3
    1810:	21f6                	lhu	a3,6(a1)
    1812:	25da                	lhu	a4,12(a1)
    1814:	1121                	addi	sp,sp,-24
    1816:	8fd5                	or	a5,a5,a3
    1818:	a91e                	sh	a5,16(a0)
    181a:	2556                	lhu	a3,12(a0)
    181c:	77fd                	lui	a5,0xfffff
    181e:	9f378793          	addi	a5,a5,-1549 # ffffe9f3 <__global_pointer$+0xdfffe1b3>
    1822:	8ff5                	and	a5,a5,a3
    1824:	21d6                	lhu	a3,4(a1)
    1826:	842a                	mv	s0,a0
    1828:	c02e                	sw	a1,0(sp)
    182a:	8fd5                	or	a5,a5,a3
    182c:	2596                	lhu	a3,8(a1)
    182e:	8fd5                	or	a5,a5,a3
    1830:	25b6                	lhu	a3,10(a1)
    1832:	8fd5                	or	a5,a5,a3
    1834:	a55e                	sh	a5,12(a0)
    1836:	295e                	lhu	a5,20(a0)
    1838:	07c2                	slli	a5,a5,0x10
    183a:	83c1                	srli	a5,a5,0x10
    183c:	cff7f793          	andi	a5,a5,-769
    1840:	8f5d                	or	a4,a4,a5
    1842:	a95a                	sh	a4,20(a0)
    1844:	0048                	addi	a0,sp,4
    1846:	d99ff0ef          	jal	ra,15de <RCC_GetClocksFreq>
    184a:	400147b7          	lui	a5,0x40014
    184e:	80078793          	addi	a5,a5,-2048 # 40013800 <__global_pointer$+0x20012fc0>
    1852:	4582                	lw	a1,0(sp)
    1854:	06f41263          	bne	s0,a5,18b8 <USART_Init+0xb4>
    1858:	47c2                	lw	a5,16(sp)
    185a:	245a                	lhu	a4,12(s0)
    185c:	00179513          	slli	a0,a5,0x1
    1860:	953e                	add	a0,a0,a5
    1862:	0742                	slli	a4,a4,0x10
    1864:	050e                	slli	a0,a0,0x3
    1866:	8741                	srai	a4,a4,0x10
    1868:	953e                	add	a0,a0,a5
    186a:	418c                	lw	a1,0(a1)
    186c:	04075863          	bgez	a4,18bc <USART_Init+0xb8>
    1870:	0586                	slli	a1,a1,0x1
    1872:	863fe0ef          	jal	ra,d4 <__udivsi3>
    1876:	06400593          	li	a1,100
    187a:	c02a                	sw	a0,0(sp)
    187c:	859fe0ef          	jal	ra,d4 <__udivsi3>
    1880:	4782                	lw	a5,0(sp)
    1882:	00451493          	slli	s1,a0,0x4
    1886:	06400593          	li	a1,100
    188a:	853e                	mv	a0,a5
    188c:	875fe0ef          	jal	ra,100 <__umodsi3>
    1890:	245e                	lhu	a5,12(s0)
    1892:	07c2                	slli	a5,a5,0x10
    1894:	87c1                	srai	a5,a5,0x10
    1896:	0207d563          	bgez	a5,18c0 <USART_Init+0xbc>
    189a:	050e                	slli	a0,a0,0x3
    189c:	06400593          	li	a1,100
    18a0:	03250513          	addi	a0,a0,50
    18a4:	831fe0ef          	jal	ra,d4 <__udivsi3>
    18a8:	891d                	andi	a0,a0,7
    18aa:	8cc9                	or	s1,s1,a0
    18ac:	04c2                	slli	s1,s1,0x10
    18ae:	80c1                	srli	s1,s1,0x10
    18b0:	a406                	sh	s1,8(s0)
    18b2:	0161                	addi	sp,sp,24
    18b4:	ff6fe06f          	j	aa <__riscv_restore_0>
    18b8:	47b2                	lw	a5,12(sp)
    18ba:	b745                	j	185a <USART_Init+0x56>
    18bc:	058a                	slli	a1,a1,0x2
    18be:	bf55                	j	1872 <USART_Init+0x6e>
    18c0:	0512                	slli	a0,a0,0x4
    18c2:	06400593          	li	a1,100
    18c6:	03250513          	addi	a0,a0,50
    18ca:	80bfe0ef          	jal	ra,d4 <__udivsi3>
    18ce:	893d                	andi	a0,a0,15
    18d0:	bfe9                	j	18aa <USART_Init+0xa6>

000018d2 <USART_Cmd>:
    18d2:	c591                	beqz	a1,18de <USART_Cmd+0xc>
    18d4:	255e                	lhu	a5,12(a0)
    18d6:	6709                	lui	a4,0x2
    18d8:	8fd9                	or	a5,a5,a4
    18da:	a55e                	sh	a5,12(a0)
    18dc:	8082                	ret
    18de:	255a                	lhu	a4,12(a0)
    18e0:	77f9                	lui	a5,0xffffe
    18e2:	17fd                	addi	a5,a5,-1
    18e4:	8ff9                	and	a5,a5,a4
    18e6:	bfd5                	j	18da <USART_Cmd+0x8>

000018e8 <USART_SendData>:
    18e8:	1ff5f593          	andi	a1,a1,511
    18ec:	a14e                	sh	a1,4(a0)
    18ee:	8082                	ret

000018f0 <USART_GetFlagStatus>:
    18f0:	210a                	lhu	a0,0(a0)
    18f2:	8d6d                	and	a0,a0,a1
    18f4:	00a03533          	snez	a0,a0
    18f8:	8082                	ret

000018fa <Delay_Init>:
    18fa:	fa6fe2ef          	jal	t0,a0 <__riscv_save_0>
    18fe:	200007b7          	lui	a5,0x20000
    1902:	0147a503          	lw	a0,20(a5) # 20000014 <SystemCoreClock>
    1906:	007a15b7          	lui	a1,0x7a1
    190a:	20058593          	addi	a1,a1,512 # 7a1200 <_data_lma+0x79e9d4>
    190e:	fc6fe0ef          	jal	ra,d4 <__udivsi3>
    1912:	0ff57513          	andi	a0,a0,255
    1916:	9aa18523          	sb	a0,-1622(gp) # 200001ea <p_us>
    191a:	00551793          	slli	a5,a0,0x5
    191e:	8f89                	sub	a5,a5,a0
    1920:	078a                	slli	a5,a5,0x2
    1922:	953e                	add	a0,a0,a5
    1924:	050e                	slli	a0,a0,0x3
    1926:	9aa19423          	sh	a0,-1624(gp) # 200001e8 <p_ms>
    192a:	f80fe06f          	j	aa <__riscv_restore_0>

0000192e <Delay_Ms>:
    192e:	f72fe2ef          	jal	t0,a0 <__riscv_save_0>
    1932:	e000f437          	lui	s0,0xe000f
    1936:	405c                	lw	a5,4(s0)
    1938:	85aa                	mv	a1,a0
    193a:	9bf9                	andi	a5,a5,-2
    193c:	c05c                	sw	a5,4(s0)
    193e:	9a81d503          	lhu	a0,-1624(gp) # 200001e8 <p_ms>
    1942:	f72fe0ef          	jal	ra,b4 <__mulsi3>
    1946:	c808                	sw	a0,16(s0)
    1948:	00042423          	sw	zero,8(s0) # e000f008 <__global_pointer$+0xc000e7c8>
    194c:	401c                	lw	a5,0(s0)
    194e:	0017e793          	ori	a5,a5,1
    1952:	c01c                	sw	a5,0(s0)
    1954:	e000f7b7          	lui	a5,0xe000f
    1958:	43d8                	lw	a4,4(a5)
    195a:	8b05                	andi	a4,a4,1
    195c:	df75                	beqz	a4,1958 <Delay_Ms+0x2a>
    195e:	4398                	lw	a4,0(a5)
    1960:	9b79                	andi	a4,a4,-2
    1962:	c398                	sw	a4,0(a5)
    1964:	f46fe06f          	j	aa <__riscv_restore_0>

00001968 <USART_Printf_Init>:
    1968:	f38fe2ef          	jal	t0,a0 <__riscv_save_0>
    196c:	842a                	mv	s0,a0
    196e:	6511                	lui	a0,0x4
    1970:	1111                	addi	sp,sp,-28
    1972:	4585                	li	a1,1
    1974:	02050513          	addi	a0,a0,32 # 4020 <_data_lma+0x17f4>
    1978:	d09ff0ef          	jal	ra,1680 <RCC_APB2PeriphClockCmd>
    197c:	02000793          	li	a5,32
    1980:	807c                	sh	a5,0(sp)
    1982:	40011537          	lui	a0,0x40011
    1986:	478d                	li	a5,3
    1988:	c23e                	sw	a5,4(sp)
    198a:	858a                	mv	a1,sp
    198c:	47e1                	li	a5,24
    198e:	40050513          	addi	a0,a0,1024 # 40011400 <__global_pointer$+0x20010bc0>
    1992:	c43e                	sw	a5,8(sp)
    1994:	b29ff0ef          	jal	ra,14bc <GPIO_Init>
    1998:	c622                	sw	s0,12(sp)
    199a:	40014437          	lui	s0,0x40014
    199e:	000807b7          	lui	a5,0x80
    19a2:	006c                	addi	a1,sp,12
    19a4:	80040513          	addi	a0,s0,-2048 # 40013800 <__global_pointer$+0x20012fc0>
    19a8:	ca3e                	sw	a5,20(sp)
    19aa:	c802                	sw	zero,16(sp)
    19ac:	00011c23          	sh	zero,24(sp)
    19b0:	3d91                	jal	1804 <USART_Init>
    19b2:	4585                	li	a1,1
    19b4:	80040513          	addi	a0,s0,-2048
    19b8:	3f29                	jal	18d2 <USART_Cmd>
    19ba:	0171                	addi	sp,sp,28
    19bc:	eeefe06f          	j	aa <__riscv_restore_0>

000019c0 <_write>:
    19c0:	ee0fe2ef          	jal	t0,a0 <__riscv_save_0>
    19c4:	1171                	addi	sp,sp,-4
    19c6:	84ae                	mv	s1,a1
    19c8:	4401                	li	s0,0
    19ca:	02c45d63          	bge	s0,a2,1a04 <_write+0x44>
    19ce:	400147b7          	lui	a5,0x40014
    19d2:	80078713          	addi	a4,a5,-2048 # 40013800 <__global_pointer$+0x20012fc0>
    19d6:	853a                	mv	a0,a4
    19d8:	04000593          	li	a1,64
    19dc:	c032                	sw	a2,0(sp)
    19de:	3f09                	jal	18f0 <USART_GetFlagStatus>
    19e0:	400147b7          	lui	a5,0x40014
    19e4:	80078713          	addi	a4,a5,-2048 # 40013800 <__global_pointer$+0x20012fc0>
    19e8:	4602                	lw	a2,0(sp)
    19ea:	d575                	beqz	a0,19d6 <_write+0x16>
    19ec:	00848733          	add	a4,s1,s0
    19f0:	00070583          	lb	a1,0(a4) # 2000 <snprintf+0x22>
    19f4:	80078513          	addi	a0,a5,-2048
    19f8:	0405                	addi	s0,s0,1
    19fa:	05c2                	slli	a1,a1,0x10
    19fc:	81c1                	srli	a1,a1,0x10
    19fe:	35ed                	jal	18e8 <USART_SendData>
    1a00:	4602                	lw	a2,0(sp)
    1a02:	b7e1                	j	19ca <_write+0xa>
    1a04:	8532                	mv	a0,a2
    1a06:	0111                	addi	sp,sp,4
    1a08:	ea2fe06f          	j	aa <__riscv_restore_0>

00001a0c <printchar>:
    1a0c:	1141                	addi	sp,sp,-16
    1a0e:	c606                	sw	ra,12(sp)
    1a10:	c02e                	sw	a1,0(sp)
    1a12:	cd0d                	beqz	a0,1a4c <printchar+0x40>
    1a14:	4118                	lw	a4,0(a0)
    1a16:	87aa                	mv	a5,a0
    1a18:	c305                	beqz	a4,1a38 <printchar+0x2c>
    1a1a:	4158                	lw	a4,4(a0)
    1a1c:	557d                	li	a0,-1
    1a1e:	cb11                	beqz	a4,1a32 <printchar+0x26>
    1a20:	4685                	li	a3,1
    1a22:	00d71b63          	bne	a4,a3,1a38 <printchar+0x2c>
    1a26:	4798                	lw	a4,8(a5)
    1a28:	00070023          	sb	zero,0(a4)
    1a2c:	0007a223          	sw	zero,4(a5)
    1a30:	4505                	li	a0,1
    1a32:	40b2                	lw	ra,12(sp)
    1a34:	0141                	addi	sp,sp,16
    1a36:	8082                	ret
    1a38:	4798                	lw	a4,8(a5)
    1a3a:	4682                	lw	a3,0(sp)
    1a3c:	a314                	sb	a3,0(a4)
    1a3e:	4798                	lw	a4,8(a5)
    1a40:	0705                	addi	a4,a4,1
    1a42:	c798                	sw	a4,8(a5)
    1a44:	43d8                	lw	a4,4(a5)
    1a46:	177d                	addi	a4,a4,-1
    1a48:	c3d8                	sw	a4,4(a5)
    1a4a:	b7dd                	j	1a30 <printchar+0x24>
    1a4c:	4605                	li	a2,1
    1a4e:	858a                	mv	a1,sp
    1a50:	3f85                	jal	19c0 <_write>
    1a52:	bff9                	j	1a30 <printchar+0x24>

00001a54 <prints>:
    1a54:	1101                	addi	sp,sp,-32
    1a56:	cc22                	sw	s0,24(sp)
    1a58:	c22e                	sw	a1,4(sp)
    1a5a:	ce06                	sw	ra,28(sp)
    1a5c:	ca26                	sw	s1,20(sp)
    1a5e:	842a                	mv	s0,a0
    1a60:	4781                	li	a5,0
    1a62:	02000593          	li	a1,32
    1a66:	02064563          	bltz	a2,1a90 <prints+0x3c>
    1a6a:	4592                	lw	a1,4(sp)
    1a6c:	95be                	add	a1,a1,a5
    1a6e:	00058583          	lb	a1,0(a1)
    1a72:	e58d                	bnez	a1,1a9c <prints+0x48>
    1a74:	02c7d863          	bge	a5,a2,1aa4 <prints+0x50>
    1a78:	02e7d463          	bge	a5,a4,1aa0 <prints+0x4c>
    1a7c:	8e19                	sub	a2,a2,a4
    1a7e:	02000513          	li	a0,32
    1a82:	0026f593          	andi	a1,a3,2
    1a86:	c02a                	sw	a0,0(sp)
    1a88:	c589                	beqz	a1,1a92 <prints+0x3e>
    1a8a:	e701                	bnez	a4,1a92 <prints+0x3e>
    1a8c:	03000593          	li	a1,48
    1a90:	c02e                	sw	a1,0(sp)
    1a92:	8a85                	andi	a3,a3,1
    1a94:	4481                	li	s1,0
    1a96:	ea95                	bnez	a3,1aca <prints+0x76>
    1a98:	84b2                	mv	s1,a2
    1a9a:	a00d                	j	1abc <prints+0x68>
    1a9c:	0785                	addi	a5,a5,1
    1a9e:	b7f1                	j	1a6a <prints+0x16>
    1aa0:	8e1d                	sub	a2,a2,a5
    1aa2:	bff1                	j	1a7e <prints+0x2a>
    1aa4:	4601                	li	a2,0
    1aa6:	bfe1                	j	1a7e <prints+0x2a>
    1aa8:	4582                	lw	a1,0(sp)
    1aaa:	8522                	mv	a0,s0
    1aac:	c83a                	sw	a4,16(sp)
    1aae:	c632                	sw	a2,12(sp)
    1ab0:	c43e                	sw	a5,8(sp)
    1ab2:	3fa9                	jal	1a0c <printchar>
    1ab4:	47a2                	lw	a5,8(sp)
    1ab6:	4632                	lw	a2,12(sp)
    1ab8:	4742                	lw	a4,16(sp)
    1aba:	14fd                	addi	s1,s1,-1
    1abc:	fe9046e3          	bgtz	s1,1aa8 <prints+0x54>
    1ac0:	84b2                	mv	s1,a2
    1ac2:	00065363          	bgez	a2,1ac8 <prints+0x74>
    1ac6:	4481                	li	s1,0
    1ac8:	8e05                	sub	a2,a2,s1
    1aca:	02e7c763          	blt	a5,a4,1af8 <prints+0xa4>
    1ace:	87a6                	mv	a5,s1
    1ad0:	4692                	lw	a3,4(sp)
    1ad2:	40978733          	sub	a4,a5,s1
    1ad6:	9736                	add	a4,a4,a3
    1ad8:	00070583          	lb	a1,0(a4)
    1adc:	ed95                	bnez	a1,1b18 <prints+0xc4>
    1ade:	84b2                	mv	s1,a2
    1ae0:	04904463          	bgtz	s1,1b28 <prints+0xd4>
    1ae4:	00065363          	bgez	a2,1aea <prints+0x96>
    1ae8:	4601                	li	a2,0
    1aea:	40f2                	lw	ra,28(sp)
    1aec:	4462                	lw	s0,24(sp)
    1aee:	44d2                	lw	s1,20(sp)
    1af0:	00f60533          	add	a0,a2,a5
    1af4:	6105                	addi	sp,sp,32
    1af6:	8082                	ret
    1af8:	8f1d                	sub	a4,a4,a5
    1afa:	87ba                	mv	a5,a4
    1afc:	03000593          	li	a1,48
    1b00:	8522                	mv	a0,s0
    1b02:	c832                	sw	a2,16(sp)
    1b04:	c63e                	sw	a5,12(sp)
    1b06:	c43a                	sw	a4,8(sp)
    1b08:	3711                	jal	1a0c <printchar>
    1b0a:	47b2                	lw	a5,12(sp)
    1b0c:	4722                	lw	a4,8(sp)
    1b0e:	4642                	lw	a2,16(sp)
    1b10:	17fd                	addi	a5,a5,-1
    1b12:	f7ed                	bnez	a5,1afc <prints+0xa8>
    1b14:	94ba                	add	s1,s1,a4
    1b16:	bf65                	j	1ace <prints+0x7a>
    1b18:	8522                	mv	a0,s0
    1b1a:	c632                	sw	a2,12(sp)
    1b1c:	c43e                	sw	a5,8(sp)
    1b1e:	35fd                	jal	1a0c <printchar>
    1b20:	47a2                	lw	a5,8(sp)
    1b22:	4632                	lw	a2,12(sp)
    1b24:	0785                	addi	a5,a5,1
    1b26:	b76d                	j	1ad0 <prints+0x7c>
    1b28:	4582                	lw	a1,0(sp)
    1b2a:	8522                	mv	a0,s0
    1b2c:	c432                	sw	a2,8(sp)
    1b2e:	c23e                	sw	a5,4(sp)
    1b30:	3df1                	jal	1a0c <printchar>
    1b32:	14fd                	addi	s1,s1,-1
    1b34:	4622                	lw	a2,8(sp)
    1b36:	4792                	lw	a5,4(sp)
    1b38:	b765                	j	1ae0 <prints+0x8c>

00001b3a <printInt>:
    1b3a:	7139                	addi	sp,sp,-64
    1b3c:	de06                	sw	ra,60(sp)
    1b3e:	dc22                	sw	s0,56(sp)
    1b40:	da26                	sw	s1,52(sp)
    1b42:	c23e                	sw	a5,4(sp)
    1b44:	8332                	mv	t1,a2
    1b46:	863a                	mv	a2,a4
    1b48:	ed89                	bnez	a1,1b62 <printInt+0x28>
    1b4a:	4692                	lw	a3,4(sp)
    1b4c:	03000793          	li	a5,48
    1b50:	4701                	li	a4,0
    1b52:	086c                	addi	a1,sp,28
    1b54:	86fc                	sh	a5,28(sp)
    1b56:	3dfd                	jal	1a54 <prints>
    1b58:	50f2                	lw	ra,60(sp)
    1b5a:	5462                	lw	s0,56(sp)
    1b5c:	54d2                	lw	s1,52(sp)
    1b5e:	6121                	addi	sp,sp,64
    1b60:	8082                	ret
    1b62:	84aa                	mv	s1,a0
    1b64:	8436                	mv	s0,a3
    1b66:	87ae                	mv	a5,a1
    1b68:	ca91                	beqz	a3,1b7c <printInt+0x42>
    1b6a:	4729                	li	a4,10
    1b6c:	4401                	li	s0,0
    1b6e:	00e31763          	bne	t1,a4,1b7c <printInt+0x42>
    1b72:	0005d563          	bgez	a1,1b7c <printInt+0x42>
    1b76:	40b007b3          	neg	a5,a1
    1b7a:	4405                	li	s0,1
    1b7c:	4686                	lw	a3,64(sp)
    1b7e:	020109a3          	sb	zero,51(sp)
    1b82:	03310713          	addi	a4,sp,51
    1b86:	fc668693          	addi	a3,a3,-58 # 40020fc6 <__global_pointer$+0x20020786>
    1b8a:	c436                	sw	a3,8(sp)
    1b8c:	859a                	mv	a1,t1
    1b8e:	853e                	mv	a0,a5
    1b90:	ca32                	sw	a2,20(sp)
    1b92:	c83a                	sw	a4,16(sp)
    1b94:	c61a                	sw	t1,12(sp)
    1b96:	c03e                	sw	a5,0(sp)
    1b98:	d68fe0ef          	jal	ra,100 <__umodsi3>
    1b9c:	46a5                	li	a3,9
    1b9e:	4782                	lw	a5,0(sp)
    1ba0:	4332                	lw	t1,12(sp)
    1ba2:	4742                	lw	a4,16(sp)
    1ba4:	4652                	lw	a2,20(sp)
    1ba6:	00a6d463          	bge	a3,a0,1bae <printInt+0x74>
    1baa:	46a2                	lw	a3,8(sp)
    1bac:	9536                	add	a0,a0,a3
    1bae:	03050513          	addi	a0,a0,48
    1bb2:	fff70693          	addi	a3,a4,-1
    1bb6:	fea70fa3          	sb	a0,-1(a4)
    1bba:	859a                	mv	a1,t1
    1bbc:	853e                	mv	a0,a5
    1bbe:	cc32                	sw	a2,24(sp)
    1bc0:	ca3a                	sw	a4,20(sp)
    1bc2:	c81a                	sw	t1,16(sp)
    1bc4:	c63e                	sw	a5,12(sp)
    1bc6:	c036                	sw	a3,0(sp)
    1bc8:	d0cfe0ef          	jal	ra,d4 <__udivsi3>
    1bcc:	47b2                	lw	a5,12(sp)
    1bce:	4342                	lw	t1,16(sp)
    1bd0:	4752                	lw	a4,20(sp)
    1bd2:	4662                	lw	a2,24(sp)
    1bd4:	0467fd63          	bgeu	a5,t1,1c2e <printInt+0xf4>
    1bd8:	cc09                	beqz	s0,1bf2 <printInt+0xb8>
    1bda:	ce29                	beqz	a2,1c34 <printInt+0xfa>
    1bdc:	4792                	lw	a5,4(sp)
    1bde:	8b89                	andi	a5,a5,2
    1be0:	cbb1                	beqz	a5,1c34 <printInt+0xfa>
    1be2:	02d00593          	li	a1,45
    1be6:	8526                	mv	a0,s1
    1be8:	c432                	sw	a2,8(sp)
    1bea:	e23ff0ef          	jal	ra,1a0c <printchar>
    1bee:	4622                	lw	a2,8(sp)
    1bf0:	167d                	addi	a2,a2,-1
    1bf2:	4792                	lw	a5,4(sp)
    1bf4:	8b91                	andi	a5,a5,4
    1bf6:	c785                	beqz	a5,1c1e <printInt+0xe4>
    1bf8:	4706                	lw	a4,64(sp)
    1bfa:	06100793          	li	a5,97
    1bfe:	c432                	sw	a2,8(sp)
    1c00:	03000593          	li	a1,48
    1c04:	8526                	mv	a0,s1
    1c06:	04f71163          	bne	a4,a5,1c48 <printInt+0x10e>
    1c0a:	e03ff0ef          	jal	ra,1a0c <printchar>
    1c0e:	07800593          	li	a1,120
    1c12:	8526                	mv	a0,s1
    1c14:	df9ff0ef          	jal	ra,1a0c <printchar>
    1c18:	4622                	lw	a2,8(sp)
    1c1a:	0409                	addi	s0,s0,2
    1c1c:	1679                	addi	a2,a2,-2
    1c1e:	4716                	lw	a4,68(sp)
    1c20:	4692                	lw	a3,4(sp)
    1c22:	4582                	lw	a1,0(sp)
    1c24:	8526                	mv	a0,s1
    1c26:	e2fff0ef          	jal	ra,1a54 <prints>
    1c2a:	9522                	add	a0,a0,s0
    1c2c:	b735                	j	1b58 <printInt+0x1e>
    1c2e:	87aa                	mv	a5,a0
    1c30:	4702                	lw	a4,0(sp)
    1c32:	bfa9                	j	1b8c <printInt+0x52>
    1c34:	4682                	lw	a3,0(sp)
    1c36:	02d00793          	li	a5,45
    1c3a:	4401                	li	s0,0
    1c3c:	fef68fa3          	sb	a5,-1(a3)
    1c40:	ffe70793          	addi	a5,a4,-2
    1c44:	c03e                	sw	a5,0(sp)
    1c46:	b775                	j	1bf2 <printInt+0xb8>
    1c48:	dc5ff0ef          	jal	ra,1a0c <printchar>
    1c4c:	05800593          	li	a1,88
    1c50:	b7c9                	j	1c12 <printInt+0xd8>

00001c52 <printLongLongInt>:
    1c52:	4501                	li	a0,0
    1c54:	8082                	ret

00001c56 <printDouble>:
    1c56:	4501                	li	a0,0
    1c58:	8082                	ret

00001c5a <print>:
    1c5a:	fd810113          	addi	sp,sp,-40
    1c5e:	d022                	sw	s0,32(sp)
    1c60:	ce26                	sw	s1,28(sp)
    1c62:	d206                	sw	ra,36(sp)
    1c64:	c42a                	sw	a0,8(sp)
    1c66:	82ae                	mv	t0,a1
    1c68:	8432                	mv	s0,a2
    1c6a:	c602                	sw	zero,12(sp)
    1c6c:	4481                	li	s1,0
    1c6e:	00028583          	lb	a1,0(t0)
    1c72:	ed91                	bnez	a1,1c8e <print+0x34>
    1c74:	47a2                	lw	a5,8(sp)
    1c76:	c789                	beqz	a5,1c80 <print+0x26>
    1c78:	4581                	li	a1,0
    1c7a:	853e                	mv	a0,a5
    1c7c:	d91ff0ef          	jal	ra,1a0c <printchar>
    1c80:	5092                	lw	ra,36(sp)
    1c82:	5402                	lw	s0,32(sp)
    1c84:	8526                	mv	a0,s1
    1c86:	44f2                	lw	s1,28(sp)
    1c88:	02810113          	addi	sp,sp,40
    1c8c:	8082                	ret
    1c8e:	02500793          	li	a5,37
    1c92:	00f58963          	beq	a1,a5,1ca4 <print+0x4a>
    1c96:	4522                	lw	a0,8(sp)
    1c98:	c816                	sw	t0,16(sp)
    1c9a:	0485                	addi	s1,s1,1
    1c9c:	d71ff0ef          	jal	ra,1a0c <printchar>
    1ca0:	42c2                	lw	t0,16(sp)
    1ca2:	a005                	j	1cc2 <print+0x68>
    1ca4:	00128783          	lb	a5,1(t0)
    1ca8:	00128713          	addi	a4,t0,1
    1cac:	00b79d63          	bne	a5,a1,1cc6 <print+0x6c>
    1cb0:	4522                	lw	a0,8(sp)
    1cb2:	02500593          	li	a1,37
    1cb6:	c83a                	sw	a4,16(sp)
    1cb8:	d55ff0ef          	jal	ra,1a0c <printchar>
    1cbc:	4742                	lw	a4,16(sp)
    1cbe:	0485                	addi	s1,s1,1
    1cc0:	82ba                	mv	t0,a4
    1cc2:	0285                	addi	t0,t0,1
    1cc4:	b76d                	j	1c6e <print+0x14>
    1cc6:	d7dd                	beqz	a5,1c74 <print+0x1a>
    1cc8:	02b00693          	li	a3,43
    1ccc:	04d78963          	beq	a5,a3,1d1e <print+0xc4>
    1cd0:	00f6c863          	blt	a3,a5,1ce0 <print+0x86>
    1cd4:	02300693          	li	a3,35
    1cd8:	04d78663          	beq	a5,a3,1d24 <print+0xca>
    1cdc:	4781                	li	a5,0
    1cde:	a005                	j	1cfe <print+0xa4>
    1ce0:	02d00693          	li	a3,45
    1ce4:	00d78a63          	beq	a5,a3,1cf8 <print+0x9e>
    1ce8:	03000693          	li	a3,48
    1cec:	fed798e3          	bne	a5,a3,1cdc <print+0x82>
    1cf0:	00228713          	addi	a4,t0,2
    1cf4:	4789                	li	a5,2
    1cf6:	a021                	j	1cfe <print+0xa4>
    1cf8:	00228713          	addi	a4,t0,2
    1cfc:	4785                	li	a5,1
    1cfe:	00070683          	lb	a3,0(a4)
    1d02:	02b00613          	li	a2,43
    1d06:	04c68363          	beq	a3,a2,1d4c <print+0xf2>
    1d0a:	02d64163          	blt	a2,a3,1d2c <print+0xd2>
    1d0e:	02300613          	li	a2,35
    1d12:	02c68b63          	beq	a3,a2,1d48 <print+0xee>
    1d16:	82ba                	mv	t0,a4
    1d18:	4501                	li	a0,0
    1d1a:	46a5                	li	a3,9
    1d1c:	a081                	j	1d5c <print+0x102>
    1d1e:	00228713          	addi	a4,t0,2
    1d22:	bf6d                	j	1cdc <print+0x82>
    1d24:	00228713          	addi	a4,t0,2
    1d28:	4791                	li	a5,4
    1d2a:	bfd1                	j	1cfe <print+0xa4>
    1d2c:	02d00613          	li	a2,45
    1d30:	00c68963          	beq	a3,a2,1d42 <print+0xe8>
    1d34:	03000613          	li	a2,48
    1d38:	fcc69fe3          	bne	a3,a2,1d16 <print+0xbc>
    1d3c:	0027e793          	ori	a5,a5,2
    1d40:	a031                	j	1d4c <print+0xf2>
    1d42:	0705                	addi	a4,a4,1
    1d44:	4785                	li	a5,1
    1d46:	bfc1                	j	1d16 <print+0xbc>
    1d48:	0047e793          	ori	a5,a5,4
    1d4c:	0705                	addi	a4,a4,1
    1d4e:	b7e1                	j	1d16 <print+0xbc>
    1d50:	00251613          	slli	a2,a0,0x2
    1d54:	9532                	add	a0,a0,a2
    1d56:	0506                	slli	a0,a0,0x1
    1d58:	953a                	add	a0,a0,a4
    1d5a:	0285                	addi	t0,t0,1
    1d5c:	00028603          	lb	a2,0(t0)
    1d60:	fd060713          	addi	a4,a2,-48
    1d64:	0ff77593          	andi	a1,a4,255
    1d68:	feb6f4e3          	bgeu	a3,a1,1d50 <print+0xf6>
    1d6c:	02e00713          	li	a4,46
    1d70:	4699                	li	a3,6
    1d72:	00e61e63          	bne	a2,a4,1d8e <print+0x134>
    1d76:	0285                	addi	t0,t0,1
    1d78:	4681                	li	a3,0
    1d7a:	45a5                	li	a1,9
    1d7c:	00028603          	lb	a2,0(t0)
    1d80:	fd060613          	addi	a2,a2,-48
    1d84:	0ff67713          	andi	a4,a2,255
    1d88:	02e5f563          	bgeu	a1,a4,1db2 <print+0x158>
    1d8c:	c636                	sw	a3,12(sp)
    1d8e:	00028703          	lb	a4,0(t0)
    1d92:	06a00613          	li	a2,106
    1d96:	0ac70d63          	beq	a4,a2,1e50 <print+0x1f6>
    1d9a:	02e64363          	blt	a2,a4,1dc0 <print+0x166>
    1d9e:	04c00613          	li	a2,76
    1da2:	0ac70763          	beq	a4,a2,1e50 <print+0x1f6>
    1da6:	06800613          	li	a2,104
    1daa:	08c70c63          	beq	a4,a2,1e42 <print+0x1e8>
    1dae:	4581                	li	a1,0
    1db0:	a82d                	j	1dea <print+0x190>
    1db2:	00269713          	slli	a4,a3,0x2
    1db6:	96ba                	add	a3,a3,a4
    1db8:	0686                	slli	a3,a3,0x1
    1dba:	96b2                	add	a3,a3,a2
    1dbc:	0285                	addi	t0,t0,1
    1dbe:	bf7d                	j	1d7c <print+0x122>
    1dc0:	07400613          	li	a2,116
    1dc4:	08c70663          	beq	a4,a2,1e50 <print+0x1f6>
    1dc8:	07a00613          	li	a2,122
    1dcc:	08c70263          	beq	a4,a2,1e50 <print+0x1f6>
    1dd0:	06c00613          	li	a2,108
    1dd4:	4581                	li	a1,0
    1dd6:	00c71a63          	bne	a4,a2,1dea <print+0x190>
    1dda:	00128603          	lb	a2,1(t0)
    1dde:	458d                	li	a1,3
    1de0:	00e61463          	bne	a2,a4,1de8 <print+0x18e>
    1de4:	0285                	addi	t0,t0,1
    1de6:	4591                	li	a1,4
    1de8:	0285                	addi	t0,t0,1
    1dea:	00028603          	lb	a2,0(t0)
    1dee:	06000393          	li	t2,96
    1df2:	06100713          	li	a4,97
    1df6:	00c3c463          	blt	t2,a2,1dfe <print+0x1a4>
    1dfa:	04100713          	li	a4,65
    1dfe:	06700393          	li	t2,103
    1e02:	06c3c463          	blt	t2,a2,1e6a <print+0x210>
    1e06:	06500393          	li	t2,101
    1e0a:	18765663          	bge	a2,t2,1f96 <print+0x33c>
    1e0e:	04700393          	li	t2,71
    1e12:	04c3c163          	blt	t2,a2,1e54 <print+0x1fa>
    1e16:	04500593          	li	a1,69
    1e1a:	16b65e63          	bge	a2,a1,1f96 <print+0x33c>
    1e1e:	04300713          	li	a4,67
    1e22:	eae610e3          	bne	a2,a4,1cc2 <print+0x68>
    1e26:	4018                	lw	a4,0(s0)
    1e28:	00440393          	addi	t2,s0,4
    1e2c:	ca16                	sw	t0,20(sp)
    1e2e:	00e10c23          	sb	a4,24(sp)
    1e32:	c81e                	sw	t2,16(sp)
    1e34:	00010ca3          	sb	zero,25(sp)
    1e38:	4701                	li	a4,0
    1e3a:	86be                	mv	a3,a5
    1e3c:	862a                	mv	a2,a0
    1e3e:	082c                	addi	a1,sp,24
    1e40:	a849                	j	1ed2 <print+0x278>
    1e42:	00128603          	lb	a2,1(t0)
    1e46:	4581                	li	a1,0
    1e48:	fae611e3          	bne	a2,a4,1dea <print+0x190>
    1e4c:	0289                	addi	t0,t0,2
    1e4e:	bf71                	j	1dea <print+0x190>
    1e50:	0285                	addi	t0,t0,1
    1e52:	bfb1                	j	1dae <print+0x154>
    1e54:	06300693          	li	a3,99
    1e58:	fcd607e3          	beq	a2,a3,1e26 <print+0x1cc>
    1e5c:	06c6cf63          	blt	a3,a2,1eda <print+0x280>
    1e60:	05800693          	li	a3,88
    1e64:	02d60363          	beq	a2,a3,1e8a <print+0x230>
    1e68:	bda9                	j	1cc2 <print+0x68>
    1e6a:	07300693          	li	a3,115
    1e6e:	04d60463          	beq	a2,a3,1eb6 <print+0x25c>
    1e72:	02c6cb63          	blt	a3,a2,1ea8 <print+0x24e>
    1e76:	06f00693          	li	a3,111
    1e7a:	0ed60563          	beq	a2,a3,1f64 <print+0x30a>
    1e7e:	07000693          	li	a3,112
    1e82:	0047e793          	ori	a5,a5,4
    1e86:	e2d61ee3          	bne	a2,a3,1cc2 <print+0x68>
    1e8a:	4691                	li	a3,4
    1e8c:	0cd59263          	bne	a1,a3,1f50 <print+0x2f6>
    1e90:	00840393          	addi	t2,s0,8
    1e94:	400c                	lw	a1,0(s0)
    1e96:	4050                	lw	a2,4(s0)
    1e98:	ca16                	sw	t0,20(sp)
    1e9a:	c23a                	sw	a4,4(sp)
    1e9c:	c03e                	sw	a5,0(sp)
    1e9e:	c81e                	sw	t2,16(sp)
    1ea0:	87aa                	mv	a5,a0
    1ea2:	4701                	li	a4,0
    1ea4:	46c1                	li	a3,16
    1ea6:	a881                	j	1ef6 <print+0x29c>
    1ea8:	07500693          	li	a3,117
    1eac:	06d60b63          	beq	a2,a3,1f22 <print+0x2c8>
    1eb0:	07800693          	li	a3,120
    1eb4:	bf45                	j	1e64 <print+0x20a>
    1eb6:	4018                	lw	a4,0(s0)
    1eb8:	000036b7          	lui	a3,0x3
    1ebc:	00440393          	addi	t2,s0,4
    1ec0:	82468593          	addi	a1,a3,-2012 # 2824 <font+0x500>
    1ec4:	c311                	beqz	a4,1ec8 <print+0x26e>
    1ec6:	85ba                	mv	a1,a4
    1ec8:	4732                	lw	a4,12(sp)
    1eca:	ca16                	sw	t0,20(sp)
    1ecc:	c81e                	sw	t2,16(sp)
    1ece:	86be                	mv	a3,a5
    1ed0:	862a                	mv	a2,a0
    1ed2:	4522                	lw	a0,8(sp)
    1ed4:	b81ff0ef          	jal	ra,1a54 <prints>
    1ed8:	a015                	j	1efc <print+0x2a2>
    1eda:	4691                	li	a3,4
    1edc:	02d59563          	bne	a1,a3,1f06 <print+0x2ac>
    1ee0:	00840393          	addi	t2,s0,8
    1ee4:	400c                	lw	a1,0(s0)
    1ee6:	4050                	lw	a2,4(s0)
    1ee8:	ca16                	sw	t0,20(sp)
    1eea:	c23a                	sw	a4,4(sp)
    1eec:	c03e                	sw	a5,0(sp)
    1eee:	c81e                	sw	t2,16(sp)
    1ef0:	87aa                	mv	a5,a0
    1ef2:	4705                	li	a4,1
    1ef4:	46a9                	li	a3,10
    1ef6:	4522                	lw	a0,8(sp)
    1ef8:	d5bff0ef          	jal	ra,1c52 <printLongLongInt>
    1efc:	43c2                	lw	t2,16(sp)
    1efe:	94aa                	add	s1,s1,a0
    1f00:	841e                	mv	s0,t2
    1f02:	42d2                	lw	t0,20(sp)
    1f04:	bb7d                	j	1cc2 <print+0x68>
    1f06:	46b2                	lw	a3,12(sp)
    1f08:	400c                	lw	a1,0(s0)
    1f0a:	c816                	sw	t0,16(sp)
    1f0c:	c236                	sw	a3,4(sp)
    1f0e:	c03a                	sw	a4,0(sp)
    1f10:	0411                	addi	s0,s0,4
    1f12:	872a                	mv	a4,a0
    1f14:	4685                	li	a3,1
    1f16:	4629                	li	a2,10
    1f18:	4522                	lw	a0,8(sp)
    1f1a:	c21ff0ef          	jal	ra,1b3a <printInt>
    1f1e:	94aa                	add	s1,s1,a0
    1f20:	b341                	j	1ca0 <print+0x46>
    1f22:	4691                	li	a3,4
    1f24:	00d59d63          	bne	a1,a3,1f3e <print+0x2e4>
    1f28:	00840393          	addi	t2,s0,8
    1f2c:	400c                	lw	a1,0(s0)
    1f2e:	4050                	lw	a2,4(s0)
    1f30:	ca16                	sw	t0,20(sp)
    1f32:	c23a                	sw	a4,4(sp)
    1f34:	c03e                	sw	a5,0(sp)
    1f36:	c81e                	sw	t2,16(sp)
    1f38:	87aa                	mv	a5,a0
    1f3a:	4701                	li	a4,0
    1f3c:	bf65                	j	1ef4 <print+0x29a>
    1f3e:	46b2                	lw	a3,12(sp)
    1f40:	400c                	lw	a1,0(s0)
    1f42:	c816                	sw	t0,16(sp)
    1f44:	c236                	sw	a3,4(sp)
    1f46:	c03a                	sw	a4,0(sp)
    1f48:	0411                	addi	s0,s0,4
    1f4a:	872a                	mv	a4,a0
    1f4c:	4681                	li	a3,0
    1f4e:	b7e1                	j	1f16 <print+0x2bc>
    1f50:	46b2                	lw	a3,12(sp)
    1f52:	c816                	sw	t0,16(sp)
    1f54:	400c                	lw	a1,0(s0)
    1f56:	4641                	li	a2,16
    1f58:	c236                	sw	a3,4(sp)
    1f5a:	c03a                	sw	a4,0(sp)
    1f5c:	0411                	addi	s0,s0,4
    1f5e:	872a                	mv	a4,a0
    1f60:	4681                	li	a3,0
    1f62:	bf5d                	j	1f18 <print+0x2be>
    1f64:	4691                	li	a3,4
    1f66:	00d59e63          	bne	a1,a3,1f82 <print+0x328>
    1f6a:	00840393          	addi	t2,s0,8
    1f6e:	400c                	lw	a1,0(s0)
    1f70:	4050                	lw	a2,4(s0)
    1f72:	ca16                	sw	t0,20(sp)
    1f74:	c23a                	sw	a4,4(sp)
    1f76:	c03e                	sw	a5,0(sp)
    1f78:	c81e                	sw	t2,16(sp)
    1f7a:	87aa                	mv	a5,a0
    1f7c:	4701                	li	a4,0
    1f7e:	46a1                	li	a3,8
    1f80:	bf9d                	j	1ef6 <print+0x29c>
    1f82:	46b2                	lw	a3,12(sp)
    1f84:	400c                	lw	a1,0(s0)
    1f86:	c816                	sw	t0,16(sp)
    1f88:	c236                	sw	a3,4(sp)
    1f8a:	c03a                	sw	a4,0(sp)
    1f8c:	0411                	addi	s0,s0,4
    1f8e:	872a                	mv	a4,a0
    1f90:	4681                	li	a3,0
    1f92:	4621                	li	a2,8
    1f94:	b751                	j	1f18 <print+0x2be>
    1f96:	400c                	lw	a1,0(s0)
    1f98:	00840613          	addi	a2,s0,8
    1f9c:	4040                	lw	s0,4(s0)
    1f9e:	c23a                	sw	a4,4(sp)
    1fa0:	872a                	mv	a4,a0
    1fa2:	4522                	lw	a0,8(sp)
    1fa4:	c832                	sw	a2,16(sp)
    1fa6:	c03e                	sw	a5,0(sp)
    1fa8:	8622                	mv	a2,s0
    1faa:	87b6                	mv	a5,a3
    1fac:	46a9                	li	a3,10
    1fae:	ca16                	sw	t0,20(sp)
    1fb0:	ca7ff0ef          	jal	ra,1c56 <printDouble>
    1fb4:	94aa                	add	s1,s1,a0
    1fb6:	4442                	lw	s0,16(sp)
    1fb8:	b7a9                	j	1f02 <print+0x2a8>

00001fba <printf>:
    1fba:	fdc10113          	addi	sp,sp,-36
    1fbe:	c82e                	sw	a1,16(sp)
    1fc0:	ca32                	sw	a2,20(sp)
    1fc2:	85aa                	mv	a1,a0
    1fc4:	0810                	addi	a2,sp,16
    1fc6:	4501                	li	a0,0
    1fc8:	c606                	sw	ra,12(sp)
    1fca:	cc36                	sw	a3,24(sp)
    1fcc:	ce3a                	sw	a4,28(sp)
    1fce:	d03e                	sw	a5,32(sp)
    1fd0:	c032                	sw	a2,0(sp)
    1fd2:	c89ff0ef          	jal	ra,1c5a <print>
    1fd6:	40b2                	lw	ra,12(sp)
    1fd8:	02410113          	addi	sp,sp,36
    1fdc:	8082                	ret

00001fde <snprintf>:
    1fde:	fd810113          	addi	sp,sp,-40
    1fe2:	8332                	mv	t1,a2
    1fe4:	d23e                	sw	a5,36(sp)
    1fe6:	c42e                	sw	a1,8(sp)
    1fe8:	c62a                	sw	a0,12(sp)
    1fea:	0870                	addi	a2,sp,28
    1fec:	4785                	li	a5,1
    1fee:	0048                	addi	a0,sp,4
    1ff0:	859a                	mv	a1,t1
    1ff2:	cc06                	sw	ra,24(sp)
    1ff4:	ce36                	sw	a3,28(sp)
    1ff6:	d03a                	sw	a4,32(sp)
    1ff8:	c23e                	sw	a5,4(sp)
    1ffa:	c032                	sw	a2,0(sp)
    1ffc:	c5fff0ef          	jal	ra,1c5a <print>
    2000:	40e2                	lw	ra,24(sp)
    2002:	02810113          	addi	sp,sp,40
    2006:	8082                	ret

00002008 <puts>:
    2008:	1141                	addi	sp,sp,-16
    200a:	c422                	sw	s0,8(sp)
    200c:	c226                	sw	s1,4(sp)
    200e:	c606                	sw	ra,12(sp)
    2010:	211c                	lbu	a5,0(a0)
    2012:	84aa                	mv	s1,a0
    2014:	4401                	li	s0,0
    2016:	81dc                	sb	a5,3(sp)
    2018:	00310783          	lb	a5,3(sp)
    201c:	0405                	addi	s0,s0,1
    201e:	ef99                	bnez	a5,203c <puts+0x34>
    2020:	47a9                	li	a5,10
    2022:	00310593          	addi	a1,sp,3
    2026:	4605                	li	a2,1
    2028:	4501                	li	a0,0
    202a:	81dc                	sb	a5,3(sp)
    202c:	995ff0ef          	jal	ra,19c0 <_write>
    2030:	8522                	mv	a0,s0
    2032:	40b2                	lw	ra,12(sp)
    2034:	4422                	lw	s0,8(sp)
    2036:	4492                	lw	s1,4(sp)
    2038:	0141                	addi	sp,sp,16
    203a:	8082                	ret
    203c:	4605                	li	a2,1
    203e:	00310593          	addi	a1,sp,3
    2042:	4501                	li	a0,0
    2044:	97dff0ef          	jal	ra,19c0 <_write>
    2048:	008487b3          	add	a5,s1,s0
    204c:	239c                	lbu	a5,0(a5)
    204e:	81dc                	sb	a5,3(sp)
    2050:	b7e1                	j	2018 <puts+0x10>

00002052 <memcpy>:
    2052:	00a5c7b3          	xor	a5,a1,a0
    2056:	8b8d                	andi	a5,a5,3
    2058:	00c50733          	add	a4,a0,a2
    205c:	e781                	bnez	a5,2064 <memcpy+0x12>
    205e:	478d                	li	a5,3
    2060:	02c7e763          	bltu	a5,a2,208e <memcpy+0x3c>
    2064:	87aa                	mv	a5,a0
    2066:	0ae57e63          	bgeu	a0,a4,2122 <memcpy+0xd0>
    206a:	2194                	lbu	a3,0(a1)
    206c:	0785                	addi	a5,a5,1
    206e:	0585                	addi	a1,a1,1
    2070:	fed78fa3          	sb	a3,-1(a5)
    2074:	fee7ebe3          	bltu	a5,a4,206a <memcpy+0x18>
    2078:	8082                	ret
    207a:	2194                	lbu	a3,0(a1)
    207c:	0785                	addi	a5,a5,1
    207e:	0585                	addi	a1,a1,1
    2080:	fed78fa3          	sb	a3,-1(a5)
    2084:	fee7ebe3          	bltu	a5,a4,207a <memcpy+0x28>
    2088:	4402                	lw	s0,0(sp)
    208a:	0111                	addi	sp,sp,4
    208c:	8082                	ret
    208e:	00357693          	andi	a3,a0,3
    2092:	87aa                	mv	a5,a0
    2094:	ca89                	beqz	a3,20a6 <memcpy+0x54>
    2096:	2194                	lbu	a3,0(a1)
    2098:	0785                	addi	a5,a5,1
    209a:	0585                	addi	a1,a1,1
    209c:	fed78fa3          	sb	a3,-1(a5)
    20a0:	0037f693          	andi	a3,a5,3
    20a4:	bfc5                	j	2094 <memcpy+0x42>
    20a6:	ffc77693          	andi	a3,a4,-4
    20aa:	fe068613          	addi	a2,a3,-32
    20ae:	06c7f563          	bgeu	a5,a2,2118 <memcpy+0xc6>
    20b2:	1171                	addi	sp,sp,-4
    20b4:	c022                	sw	s0,0(sp)
    20b6:	49c0                	lw	s0,20(a1)
    20b8:	0005a303          	lw	t1,0(a1)
    20bc:	0085a383          	lw	t2,8(a1)
    20c0:	cbc0                	sw	s0,20(a5)
    20c2:	4d80                	lw	s0,24(a1)
    20c4:	0067a023          	sw	t1,0(a5)
    20c8:	0045a303          	lw	t1,4(a1)
    20cc:	cf80                	sw	s0,24(a5)
    20ce:	4dc0                	lw	s0,28(a1)
    20d0:	0067a223          	sw	t1,4(a5)
    20d4:	00c5a283          	lw	t0,12(a1)
    20d8:	0105a303          	lw	t1,16(a1)
    20dc:	02458593          	addi	a1,a1,36
    20e0:	cfc0                	sw	s0,28(a5)
    20e2:	ffc5a403          	lw	s0,-4(a1)
    20e6:	0077a423          	sw	t2,8(a5)
    20ea:	0057a623          	sw	t0,12(a5)
    20ee:	0067a823          	sw	t1,16(a5)
    20f2:	02478793          	addi	a5,a5,36
    20f6:	fe87ae23          	sw	s0,-4(a5)
    20fa:	fac7eee3          	bltu	a5,a2,20b6 <memcpy+0x64>
    20fe:	f8d7f3e3          	bgeu	a5,a3,2084 <memcpy+0x32>
    2102:	4190                	lw	a2,0(a1)
    2104:	0791                	addi	a5,a5,4
    2106:	0591                	addi	a1,a1,4
    2108:	fec7ae23          	sw	a2,-4(a5)
    210c:	bfcd                	j	20fe <memcpy+0xac>
    210e:	4190                	lw	a2,0(a1)
    2110:	0791                	addi	a5,a5,4
    2112:	0591                	addi	a1,a1,4
    2114:	fec7ae23          	sw	a2,-4(a5)
    2118:	fed7ebe3          	bltu	a5,a3,210e <memcpy+0xbc>
    211c:	f4e7e7e3          	bltu	a5,a4,206a <memcpy+0x18>
    2120:	8082                	ret
    2122:	8082                	ret
    2124:	1609                	addi	a2,a2,-30
    2126:	2009                	jal	2128 <memcpy+0xd6>
    2128:	1b21                	addi	s6,s6,-24
    212a:	15171913          	0x15171913
    212e:	2b1e                	lhu	a5,16(a4)
    2130:	0504                	addi	s1,sp,640
    2132:	0e02                	c.slli64	t3
    2134:	1e08140b          	0x1e08140b
    2138:	1d22                	slli	s10,s10,0x28
    213a:	1e18                	addi	a4,sp,816
    213c:	2b241a1b          	0x2b241a1b
    2140:	0606                	slli	a2,a2,0x1
    2142:	0f02                	c.slli64	t5
    2144:	6425                	lui	s0,0x9
    2146:	252e                	lhu	a1,10(a0)
    2148:	3230                	lbu	a2,3(a2)
    214a:	5664                	lw	s1,108(a2)
    214c:	0000                	unimp
    214e:	0000                	unimp
    2150:	6425                	lui	s0,0x9
    2152:	2525                	jal	277a <font+0x456>
    2154:	0000                	unimp
    2156:	0000                	unimp
    2158:	00004843          	fmadd.s	fa6,ft0,ft0,ft0,rmm
    215c:	003a                	c.slli	zero,0xe
    215e:	0000                	unimp
    2160:	4441                	li	s0,16
    2162:	6f4d2043          	fmadd.q	ft0,fs10,fs4,fa3,rdn
    2166:	696e                	flw	fs2,216(sp)
    2168:	6f74                	flw	fa3,92(a4)
    216a:	0072                	c.slli	zero,0x1c

0000216c <CSWTCH.2>:
    216c:	07ff 07e0 ffe0 f81f 6944 7073 616c 2079     ........Display 
    217c:	664f 0066 4843 3233 3056 3330 4120 4344     Off.CH32V003 ADC
    218c:	0000 0000 6f4d 696e 6f74 2072 3176 302e     ....Monitor v1.0
    219c:	0000 0000 6f54 6375 3a68 5420 7275 206e     ....Touch: Turn 
    21ac:	6e4f 0000 6f48 646c 203a 6f54 6767 656c     On..Hold: Toggle
    21bc:	0000 0000 4441 2043 6e69 7469 6169 696c     ....ADC initiali
    21cc:	657a 0d64 0000 0000 5750 204d 6e69 7469     zed.....PWM init
    21dc:	6169 696c 657a 0d64 0000 0000 6f54 6375     ialized.....Touc
    21ec:	2068 7562 7474 6e6f 6920 696e 6974 6c61     h button initial
    21fc:	7a69 6465 000d 0000 6944 7073 616c 2079     ized....Display 
    220c:	6f63 746e 6f72 206c 6e69 7469 6169 696c     control initiali
    221c:	657a 0d64 0000 0000 4441 2043 6964 7073     zed.....ADC disp
    222c:	616c 2079 6e69 7469 6169 696c 657a 0d64     lay initialized.
    223c:	0000 0000 7953 7473 6d65 6920 696e 6974     ....System initi
    224c:	6c61 7a69 7461 6f69 206e 6f63 706d 656c     alization comple
    225c:	6574 000d 0a0d 3d3d 203d 4843 3233 3056     te....=== CH32V0
    226c:	3330 4120 4344 4d20 6e6f 7469 726f 3d20     03 ADC Monitor =
    227c:	3d3d 000d 7953 7473 6d65 6c43 3a6b 2520     ==..SystemClk: %
    228c:	2064 7a48 0a0d 0000 6843 7069 4449 203a     d Hz....ChipID: 
    229c:	3025 7838 0a0d 0000 6f54 6375 3a68 5320     %08x....Touch: S
    22ac:	6f68 7472 7020 6572 7373 2d20 7420 7275     hort press - tur
    22bc:	696e 676e 6f20 206e 6964 7073 616c 0d79     ning on display.
    22cc:	0000 0000 6f54 6375 3a68 4c20 6e6f 2067     ....Touch: Long 
    22dc:	7270 7365 2073 202d 6f74 6767 696c 676e     press - toggling
    22ec:	6420 7369 6c70 7961 6d20 646f 0d65 0000      display mode...
    22fc:	6f54 6375 3a68 5420 6d69 6f65 7475 2d20     Touch: Timeout -
    230c:	7420 7275 696e 676e 6f20 6666 6420 7369      turning off dis
    231c:	6c70 7961 000d 0000                         play....

00002324 <font>:
    2324:	0000 0000 3e00 4f5b 3e5b 6b3e 6b4f 1c3e     .....>[O[>>kOk>.
    2334:	7c3e 1c3e 3c18 3c7e 1c18 7d57 1c57 5e1c     >|>..<~<..W}W..^
    2344:	5e7f 001c 3c18 0018 e7ff e7c3 00ff 2418     .^...<.........$
    2354:	0018 e7ff e7db 30ff 3a48 0e06 2926 2979     .......0H:..&)y)
    2364:	4026 057f 0705 7f40 2505 5a3f e73c 5a3c     &@....@..%?Z<.<Z
    2374:	3e7f 1c1c 0808 1c1c 7f3e 2214 227f 5f14     .>......>.."."._
    2384:	005f 5f5f 0906 017f 007f 8966 6a95 6060     _.__......f..j``
    2394:	6060 9460 ffa2 94a2 0408 047e 1008 7e20     ```.......~... ~
    23a4:	1020 0808 1c2a 0808 2a1c 0808 101e 1010      ...*....*......
    23b4:	0c10 0c1e 0c1e 3830 383e 0630 3e0e 060e     ......08>80..>..
    23c4:	0000 0000 0000 5f00 0000 0700 0700 1400     ......._........
    23d4:	147f 147f 2a24 2a7f 2312 0813 6264 4936     ....$*.*.#..db6I
    23e4:	2056 0050 0708 0003 1c00 4122 0000 2241     V P......."A..A"
    23f4:	001c 1c2a 1c7f 082a 3e08 0808 8000 3070     ..*...*..>....p0
    2404:	0800 0808 0808 0000 6060 2000 0810 0204     ........``. ....
    2414:	513e 4549 003e 7f42 0040 4972 4949 2146     >QIE>.B.@.rIIIF!
    2424:	4941 334d 1418 7f12 2710 4545 3945 4a3c     AIM3.....'EEE9<J
    2434:	4949 4131 1121 0709 4936 4949 4636 4949     II1A!...6III6FII
    2444:	1e29 0000 0014 0000 3440 0000 0800 2214     ).......@4....."
    2454:	1441 1414 1414 4100 1422 0208 5901 0609     A......A"....Y..
    2464:	413e 595d 7c4e 1112 7c12 497f 4949 3e36     >A]YN|...|.III6>
    2474:	4141 2241 417f 4141 7f3e 4949 4149 097f     AAA".AAA>.IIIA..
    2484:	0909 3e01 4141 7351 087f 0808 007f 7f41     ...>AAQs......A.
    2494:	0041 4020 3f41 7f01 1408 4122 407f 4040     A. @A?...."A.@@@
    24a4:	7f40 1c02 7f02 047f 1008 3e7f 4141 3e41     @..........>AAA>
    24b4:	097f 0909 3e06 5141 5e21 097f 2919 2646     .....>AQ!^...)F&
    24c4:	4949 3249 0103 017f 3f03 4040 3f40 201f     III2.....?@@@?. 
    24d4:	2040 3f1f 3840 3f40 1463 1408 0363 7804     @ .?@8@?c...c..x
    24e4:	0304 5961 4d49 0043 417f 4141 0402 1008     ..aYIMC..AAA....
    24f4:	0020 4141 7f41 0204 0201 4004 4040 4040      .AAA......@@@@@
    2504:	0300 0807 2000 5454 4078 287f 4444 3838     ..... TTx@.(DD88
    2514:	4444 2844 4438 2844 387f 5454 1854 0800     DDD(8DD(.8TTT...
    2524:	097e 1802 a4a4 789c 087f 0404 0078 7d44     ~......x....x.D}
    2534:	0040 4020 3d40 7f00 2810 0044 4100 407f     @. @@=...(D..A.@
    2544:	7c00 7804 7804 087c 0404 3878 4444 3844     .|.x.x|...x8DDD8
    2554:	18fc 2424 1818 2424 fc18 087c 0404 4808     ..$$..$$..|....H
    2564:	5454 2454 0404 443f 3c24 4040 7c20 201c     TTT$..?D$<@@ |. 
    2574:	2040 3c1c 3040 3c40 2844 2810 4c44 9090     @ .<@0@<D(.(DL..
    2584:	7c90 6444 4c54 0044 3608 0041 0000 0077     .|DdTLD..6A...w.
    2594:	0000 3641 0008 0102 0402 3c02 2326 3c26     ..A6.......<&#&<
    25a4:	a11e 61a1 3a12 4040 7a20 5438 5554 2159     ...a.:@@ z8TTUY!
    25b4:	5555 4179 5422 7854 2142 5455 4078 5420     UUyA"TTxB!UTx@ T
    25c4:	7955 0c40 521e 1272 5539 5555 3959 5454     <EMAIL>.9UUUY9TT
    25d4:	5954 5539 5454 0058 4500 417c 0200 7d45     TY9UTTX..E|A..E}
    25e4:	0042 4501 407c 127d 1211 f07d 2528 f028     B..E|@}...}.(%(.
    25f4:	547c 4555 2000 5454 547c 0a7c 7f09 3249     |TUE. TT|T|...I2
    2604:	4949 3249 443a 4444 323a 484a 3048 413a     III2:DDD:2JHH0:A
    2614:	2141 3a7a 4042 7820 9d00 a0a0 3d7d 4242     A!z:B@ x....}=BB
    2624:	3d42 403d 4040 3c3d ff24 2424 7e48 4349     B==@@@=<$.$$H~IC
    2634:	2b66 fc2f 2b2f 09ff f629 c020 7e88 0309     f+/./+..). ..~..
    2644:	5420 7954 0041 4400 417d 4830 4a48 3832      TTyA..D}A0HHJ28
    2654:	4040 7a22 7a00 0a0a 7d72 190d 7d31 2926     @@"z.z..r}..1}&)
    2664:	2f29 2628 2929 2629 4830 404d 3820 0808     )/(&)))&0HM@ 8..
    2674:	0808 0808 0808 2f38 c810 baac 102f 3428     ......8/..../.(4
    2684:	00fa 7b00 0000 1408 142a 2222 2a14 0814     ...{....*."".*..
    2694:	0055 0055 aa55 aa55 aa55 55ff 55ff 00ff     U.U.U.U.U..U.U..
    26a4:	0000 00ff 1010 ff10 1400 1414 00ff 1010     ................
    26b4:	00ff 10ff f010 f010 1414 fc14 1400 f714     ................
    26c4:	ff00 0000 00ff 14ff f414 fc04 1414 1017     ................
    26d4:	101f 1f10 1f10 1414 1f14 1000 1010 00f0     ................
    26e4:	0000 1f00 1010 1010 101f 1010 f010 0010     ................
    26f4:	0000 10ff 1010 1010 1010 1010 10ff 0000     ................
    2704:	ff00 0014 ff00 ff00 0000 101f 0017 fc00     ................
    2714:	f404 1414 1017 1417 f414 f404 0000 00ff     ................
    2724:	14f7 1414 1414 1414 00f7 14f7 1414 1417     ................
    2734:	1010 101f 141f 1414 14f4 1010 10f0 00f0     ................
    2744:	1f00 1f10 0000 1f00 0014 0000 14fc 0000     ................
    2754:	10f0 10f0 ff10 ff10 1414 ff14 1014 1010     ................
    2764:	001f 0000 f000 ff10 ffff ffff f0f0 f0f0     ................
    2774:	fff0 ffff 0000 0000 ff00 0fff 0f0f 0f0f     ................
    2784:	4438 3844 fc44 4a4a 344a 027e 0602 0206     8DD8D.JJJ4~.....
    2794:	027e 027e 5563 4149 3863 4444 043c 7e40     ~.~.cUIAc8DD<.@~
    27a4:	1e20 0620 7e02 0202 a599 a5e7 1c99 492a      . ..~........*I
    27b4:	1c2a 724c 7201 304c 4d4a 304d 4830 4878     *.Lr.rL0JMM00HxH
    27c4:	bc30 5a62 3d46 493e 4949 7e00 0101 7e01     0.bZF=>III.~...~
    27d4:	2a2a 2a2a 442a 5f44 4444 5140 444a 4040     *****DD_DD@QJD@@
    27e4:	4a44 4051 0000 01ff e003 ff80 0000 0808     DJQ@............
    27f4:	6b6b 3608 3612 3624 0600 0909 0006 1800     kk.6.6$6........
    2804:	0018 0000 1010 3000 ff40 0101 1f00 0101     .......0@.......
    2814:	001e 1d19 1217 3c00 3c3c 003c 0000 0000     .......<<<<.....
    2824:	6e28 6c75 296c 0000                         (null)..
