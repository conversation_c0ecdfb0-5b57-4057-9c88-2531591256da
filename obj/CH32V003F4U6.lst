
CH32V003F4U6.elf:     file format elf32-littleriscv
CH32V003F4U6.elf
architecture: riscv:rv32, flags 0x00000112:
EXEC_P, HAS_SYMS, D_PAGED
start address 0x00000000

Program Header:
    LOAD off    0x00001000 vaddr 0x00000000 paddr 0x00000000 align 2**12
         filesz 0x00000d8c memsz 0x00000d8c flags r-x
    LOAD off    0x00002000 vaddr 0x20000000 paddr 0x00000d8c align 2**12
         filesz 0x00000038 memsz 0x00000044 flags rw-
    LOAD off    0x00002700 vaddr 0x20000700 paddr 0x20000700 align 2**12
         filesz 0x00000000 memsz 0x00000100 flags rw-

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .init         000000a0  00000000  00000000  00001000  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  1 .highcodelalign 00000000  000000a0  000000a0  00002038  2**0
                  CONTENTS
  2 .highcode     00000000  20000000  20000000  00002038  2**0
                  CONTENTS
  3 .text         00000cec  000000a0  000000a0  000010a0  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  4 .fini         00000000  00000d8c  00000d8c  00002038  2**0
                  CONTENTS, ALLOC, LOAD, CODE
  5 .dalign       00000000  20000000  20000000  00002038  2**0
                  CONTENTS
  6 .dlalign      00000000  00000d8c  00000d8c  00002038  2**0
                  CONTENTS
  7 .data         00000038  20000000  00000d8c  00002000  2**2
                  CONTENTS, ALLOC, LOAD, DATA
  8 .bss          0000000c  20000038  00000dc4  00002038  2**2
                  ALLOC
  9 .stack        00000100  20000700  20000700  00002700  2**0
                  ALLOC
 10 .debug_info   00008291  00000000  00000000  00002038  2**0
                  CONTENTS, READONLY, DEBUGGING
 11 .debug_abbrev 00001a69  00000000  00000000  0000a2c9  2**0
                  CONTENTS, READONLY, DEBUGGING
 12 .debug_aranges 000003e0  00000000  00000000  0000bd38  2**3
                  CONTENTS, READONLY, DEBUGGING
 13 .debug_ranges 000003b0  00000000  00000000  0000c118  2**3
                  CONTENTS, READONLY, DEBUGGING
 14 .debug_line   00004e90  00000000  00000000  0000c4c8  2**0
                  CONTENTS, READONLY, DEBUGGING
 15 .debug_str    0000160c  00000000  00000000  00011358  2**0
                  CONTENTS, READONLY, DEBUGGING
 16 .comment      00000033  00000000  00000000  00012964  2**0
                  CONTENTS, READONLY
 17 .debug_frame  00000790  00000000  00000000  00012998  2**2
                  CONTENTS, READONLY, DEBUGGING
 18 .debug_loc    000015ae  00000000  00000000  00013128  2**0
                  CONTENTS, READONLY, DEBUGGING
SYMBOL TABLE:
00000000 l    d  .init	00000000 .init
000000a0 l    d  .highcodelalign	00000000 .highcodelalign
20000000 l    d  .highcode	00000000 .highcode
000000a0 l    d  .text	00000000 .text
00000d8c l    d  .fini	00000000 .fini
20000000 l    d  .dalign	00000000 .dalign
00000d8c l    d  .dlalign	00000000 .dlalign
20000000 l    d  .data	00000000 .data
20000038 l    d  .bss	00000000 .bss
20000700 l    d  .stack	00000000 .stack
00000000 l    d  .debug_info	00000000 .debug_info
00000000 l    d  .debug_abbrev	00000000 .debug_abbrev
00000000 l    d  .debug_aranges	00000000 .debug_aranges
00000000 l    d  .debug_ranges	00000000 .debug_ranges
00000000 l    d  .debug_line	00000000 .debug_line
00000000 l    d  .debug_str	00000000 .debug_str
00000000 l    d  .comment	00000000 .comment
00000000 l    d  .debug_frame	00000000 .debug_frame
00000000 l    d  .debug_loc	00000000 .debug_loc
00000000 l    df *ABS*	00000000 ch32v00x_it.c
00000000 l    df *ABS*	00000000 main.c
00000000 l    df *ABS*	00000000 system_ch32v00x.c
00000000 l    df *ABS*	00000000 ch32v00x_dbgmcu.c
00000000 l    df *ABS*	00000000 ch32v00x_gpio.c
00000000 l    df *ABS*	00000000 ch32v00x_misc.c
00000000 l    df *ABS*	00000000 ch32v00x_rcc.c
20000014 l     O .data	00000014 ADCPrescTable
20000028 l     O .data	00000010 APBAHBPrescTable
00000000 l    df *ABS*	00000000 ch32v00x_usart.c
00000000 l    df *ABS*	00000000 debug.c
20000040 l     O .bss	00000002 p_ms
20000042 l     O .bss	00000001 p_us
00000000 l    df *ABS*	00000000 wchprintf.c
000009ec  w    F .text	00000004 printDouble
000009f0  w    F .text	00000352 print
00000d42  w    F .text	00000022 printf
20000838 g       .data	00000000 __global_pointer$
000003e0  w      .text	00000000 TIM1_CC_IRQHandler
00000134 g     F .text	00000010 HardFault_Handler
000008da  w    F .text	0000010e printInt
000003e0  w      .text	00000000 SysTick_Handler
000003e0  w      .text	00000000 PVD_IRQHandler
00000132 g     F .text	00000002 NMI_Handler
00000492 g     F .text	0000000a DBGMCU_GetCHIPID
000006d4 g     F .text	0000000a USART_GetFlagStatus
20000038 g       .bss	00000000 _sbss
00000100 g       *ABS*	00000000 __stack_size
0000070e g     F .text	00000052 USART_Printf_Init
000000aa g     F .text	0000000a .hidden __riscv_restore_2
000009e8  w    F .text	00000004 printLongLongInt
20000010 g     O .data	00000004 SystemCoreClock
000006cc g     F .text	00000008 USART_ReceiveData
000000bc g     F .text	0000002c .hidden __udivsi3
000000a0 g       .init	00000000 _einit
000005d0 g     F .text	0000001e RCC_APB2PeriphClockCmd
0000049c g     F .text	0000007c GPIO_Init
2000003c g     O .bss	00000004 NVIC_Priority_Group
000003e0  w      .text	00000000 SPI1_IRQHandler
000006ae g     F .text	00000016 USART_Cmd
000000a0 g     F .text	0000000a .hidden __riscv_save_1
000000aa g     F .text	0000000a .hidden __riscv_restore_0
000003e0  w      .text	00000000 AWU_IRQHandler
000003e0  w      .text	00000000 EXTI7_0_IRQHandler
20000700 g       .stack	00000000 _heap_end
000003e0  w      .text	00000000 DMA1_Channel4_IRQHandler
000003e0  w      .text	00000000 ADC1_IRQHandler
20000044 g       .bss	00000000 _ebss
000003e0  w      .text	00000000 DMA1_Channel7_IRQHandler
000006de g     F .text	00000030 Delay_Init
000000e8 g     F .text	00000008 .hidden __umodsi3
000003e0  w      .text	00000000 I2C1_EV_IRQHandler
00000534 g     F .text	0000009c RCC_GetClocksFreq
000003e0  w      .text	00000000 DMA1_Channel6_IRQHandler
000005ee g     F .text	000000c0 USART_Init
20000038 g     O .bss	00000001 val
000003e0  w      .text	00000000 RCC_IRQHandler
000003e0  w      .text	00000000 TIM1_TRG_COM_IRQHandler
000003e0  w      .text	00000000 DMA1_Channel1_IRQHandler
00000000 g       .init	00000000 _start
20000000 g     O .data	00000010 AHBPrescTable
000007f4  w    F .text	000000e6 prints
20000000 g       .highcode	00000000 _highcode_vma_start
20000000 g       .dalign	00000000 _data_vma
00000144 g     F .text	00000076 USARTx_CFG
00000522 g     F .text	00000012 RCC_AdjustHSICalibrationValue
000001ba g     F .text	0000008c main
000003e0  w      .text	00000000 DMA1_Channel5_IRQHandler
000000b4 g     F .text	00000058 .hidden __divsi3
000000a0 g       .highcodelalign	00000000 _highcode_lma
00000246 g     F .text	00000132 SystemInit
000007ac  w    F .text	00000048 printchar
00000000 g       .init	00000000 _sinit
000003e0  w      .text	00000000 DMA1_Channel3_IRQHandler
000003e0  w      .text	00000000 TIM1_UP_IRQHandler
000003e0  w      .text	00000000 WWDG_IRQHandler
000003e0  w      .text	00000000 TIM2_IRQHandler
20000800 g       .stack	00000000 _eusrstack
000000a0 g     F .text	0000000a .hidden __riscv_save_2
000003e0  w      .text	00000000 SW_Handler
000003e0  w      .text	00000000 TIM1_BRK_IRQHandler
000006c4 g     F .text	00000008 USART_SendData
00000760 g     F .text	0000004c _write
20000038 g       .data	00000000 _edata
20000044 g       .bss	00000000 _end
20000000 g       .highcode	00000000 _highcode_vma_end
00000d8c g       .dlalign	00000000 _data_lma
00000378 g     F .text	00000068 SystemCoreClockUpdate
0000010c g     F .text	00000024 .hidden __modsi3
000003e0  w      .text	00000000 DMA1_Channel2_IRQHandler
000003e2  w      .text	00000000 handle_reset
000003e0  w      .text	00000000 FLASH_IRQHandler
000000a0 g     F .text	0000000a .hidden __riscv_save_0
000003e0  w      .text	00000000 USART1_IRQHandler
000003e0  w      .text	00000000 I2C1_ER_IRQHandler
00000518 g     F .text	0000000a NVIC_PriorityGroupConfig
000000aa g     F .text	0000000a .hidden __riscv_restore_1



Disassembly of section .init:

00000000 <_sinit>:
   0:	3e20006f          	j	3e2 <handle_reset>
   4:	0000                	unimp
   6:	0000                	unimp
   8:	0132                	slli	sp,sp,0xc
   a:	0000                	unimp
   c:	0134                	addi	a3,sp,136
	...
  2e:	0000                	unimp
  30:	03e0                	addi	s0,sp,460
  32:	0000                	unimp
  34:	0000                	unimp
  36:	0000                	unimp
  38:	03e0                	addi	s0,sp,460
  3a:	0000                	unimp
  3c:	0000                	unimp
  3e:	0000                	unimp
  40:	03e0                	addi	s0,sp,460
  42:	0000                	unimp
  44:	03e0                	addi	s0,sp,460
  46:	0000                	unimp
  48:	03e0                	addi	s0,sp,460
  4a:	0000                	unimp
  4c:	03e0                	addi	s0,sp,460
  4e:	0000                	unimp
  50:	03e0                	addi	s0,sp,460
  52:	0000                	unimp
  54:	03e0                	addi	s0,sp,460
  56:	0000                	unimp
  58:	03e0                	addi	s0,sp,460
  5a:	0000                	unimp
  5c:	03e0                	addi	s0,sp,460
  5e:	0000                	unimp
  60:	03e0                	addi	s0,sp,460
  62:	0000                	unimp
  64:	03e0                	addi	s0,sp,460
  66:	0000                	unimp
  68:	03e0                	addi	s0,sp,460
  6a:	0000                	unimp
  6c:	03e0                	addi	s0,sp,460
  6e:	0000                	unimp
  70:	03e0                	addi	s0,sp,460
  72:	0000                	unimp
  74:	03e0                	addi	s0,sp,460
  76:	0000                	unimp
  78:	03e0                	addi	s0,sp,460
  7a:	0000                	unimp
  7c:	03e0                	addi	s0,sp,460
  7e:	0000                	unimp
  80:	03e0                	addi	s0,sp,460
  82:	0000                	unimp
  84:	03e0                	addi	s0,sp,460
  86:	0000                	unimp
  88:	03e0                	addi	s0,sp,460
  8a:	0000                	unimp
  8c:	03e0                	addi	s0,sp,460
  8e:	0000                	unimp
  90:	03e0                	addi	s0,sp,460
  92:	0000                	unimp
  94:	03e0                	addi	s0,sp,460
  96:	0000                	unimp
  98:	03e0                	addi	s0,sp,460
  9a:	0000                	unimp
  9c:	0000                	unimp
	...

Disassembly of section .text:

000000a0 <__riscv_save_0>:
  a0:	1151                	addi	sp,sp,-12
  a2:	c026                	sw	s1,0(sp)
  a4:	c222                	sw	s0,4(sp)
  a6:	c406                	sw	ra,8(sp)
  a8:	8282                	jr	t0

000000aa <__riscv_restore_0>:
  aa:	4482                	lw	s1,0(sp)
  ac:	4412                	lw	s0,4(sp)
  ae:	40a2                	lw	ra,8(sp)
  b0:	0131                	addi	sp,sp,12
  b2:	8082                	ret

000000b4 <__divsi3>:
  b4:	02054e63          	bltz	a0,f0 <__umodsi3+0x8>
  b8:	0405c363          	bltz	a1,fe <__umodsi3+0x16>

000000bc <__udivsi3>:
  bc:	862e                	mv	a2,a1
  be:	85aa                	mv	a1,a0
  c0:	557d                	li	a0,-1
  c2:	c215                	beqz	a2,e6 <__udivsi3+0x2a>
  c4:	4685                	li	a3,1
  c6:	00b67863          	bgeu	a2,a1,d6 <__udivsi3+0x1a>
  ca:	00c05663          	blez	a2,d6 <__udivsi3+0x1a>
  ce:	0606                	slli	a2,a2,0x1
  d0:	0686                	slli	a3,a3,0x1
  d2:	feb66ce3          	bltu	a2,a1,ca <__udivsi3+0xe>
  d6:	4501                	li	a0,0
  d8:	00c5e463          	bltu	a1,a2,e0 <__udivsi3+0x24>
  dc:	8d91                	sub	a1,a1,a2
  de:	8d55                	or	a0,a0,a3
  e0:	8285                	srli	a3,a3,0x1
  e2:	8205                	srli	a2,a2,0x1
  e4:	faf5                	bnez	a3,d8 <__udivsi3+0x1c>
  e6:	8082                	ret

000000e8 <__umodsi3>:
  e8:	8286                	mv	t0,ra
  ea:	3fc9                	jal	bc <__udivsi3>
  ec:	852e                	mv	a0,a1
  ee:	8282                	jr	t0
  f0:	40a00533          	neg	a0,a0
  f4:	0005d763          	bgez	a1,102 <__stack_size+0x2>
  f8:	40b005b3          	neg	a1,a1
  fc:	b7c1                	j	bc <__udivsi3>
  fe:	40b005b3          	neg	a1,a1
 102:	8286                	mv	t0,ra
 104:	3f65                	jal	bc <__udivsi3>
 106:	40a00533          	neg	a0,a0
 10a:	8282                	jr	t0

0000010c <__modsi3>:
 10c:	8286                	mv	t0,ra
 10e:	0005c763          	bltz	a1,11c <__modsi3+0x10>
 112:	00054963          	bltz	a0,124 <__modsi3+0x18>
 116:	375d                	jal	bc <__udivsi3>
 118:	852e                	mv	a0,a1
 11a:	8282                	jr	t0
 11c:	40b005b3          	neg	a1,a1
 120:	fe055be3          	bgez	a0,116 <__modsi3+0xa>
 124:	40a00533          	neg	a0,a0
 128:	3f51                	jal	bc <__udivsi3>
 12a:	40b00533          	neg	a0,a1
 12e:	8282                	jr	t0
	...

00000132 <NMI_Handler>:
 132:	a001                	j	132 <NMI_Handler>

00000134 <HardFault_Handler>:
 134:	beef07b7          	lui	a5,0xbeef0
 138:	e000e737          	lui	a4,0xe000e
 13c:	08078793          	addi	a5,a5,128 # beef0080 <__global_pointer$+0x9eeef848>
 140:	c73c                	sw	a5,72(a4)
 142:	a001                	j	142 <HardFault_Handler+0xe>

00000144 <USARTx_CFG>:
 144:	f5dff2ef          	jal	t0,a0 <__riscv_save_0>
 148:	6511                	lui	a0,0x4
 14a:	1111                	addi	sp,sp,-28
 14c:	4585                	li	a1,1
 14e:	02050513          	addi	a0,a0,32 # 4020 <_data_lma+0x3294>
 152:	c002                	sw	zero,0(sp)
 154:	c202                	sw	zero,4(sp)
 156:	c402                	sw	zero,8(sp)
 158:	c602                	sw	zero,12(sp)
 15a:	c802                	sw	zero,16(sp)
 15c:	ca02                	sw	zero,20(sp)
 15e:	cc02                	sw	zero,24(sp)
 160:	2985                	jal	5d0 <RCC_APB2PeriphClockCmd>
 162:	02000793          	li	a5,32
 166:	807c                	sh	a5,0(sp)
 168:	40011437          	lui	s0,0x40011
 16c:	478d                	li	a5,3
 16e:	c23e                	sw	a5,4(sp)
 170:	858a                	mv	a1,sp
 172:	47e1                	li	a5,24
 174:	40040513          	addi	a0,s0,1024 # 40011400 <__global_pointer$+0x20010bc8>
 178:	c43e                	sw	a5,8(sp)
 17a:	260d                	jal	49c <GPIO_Init>
 17c:	04000793          	li	a5,64
 180:	807c                	sh	a5,0(sp)
 182:	858a                	mv	a1,sp
 184:	4791                	li	a5,4
 186:	40040513          	addi	a0,s0,1024
 18a:	c43e                	sw	a5,8(sp)
 18c:	2e01                	jal	49c <GPIO_Init>
 18e:	67f1                	lui	a5,0x1c
 190:	40014437          	lui	s0,0x40014
 194:	20078793          	addi	a5,a5,512 # 1c200 <_data_lma+0x1b474>
 198:	c63e                	sw	a5,12(sp)
 19a:	006c                	addi	a1,sp,12
 19c:	000c07b7          	lui	a5,0xc0
 1a0:	80040513          	addi	a0,s0,-2048 # 40013800 <__global_pointer$+0x20012fc8>
 1a4:	ca3e                	sw	a5,20(sp)
 1a6:	c802                	sw	zero,16(sp)
 1a8:	00011c23          	sh	zero,24(sp)
 1ac:	2189                	jal	5ee <USART_Init>
 1ae:	4585                	li	a1,1
 1b0:	80040513          	addi	a0,s0,-2048
 1b4:	29ed                	jal	6ae <USART_Cmd>
 1b6:	0171                	addi	sp,sp,28
 1b8:	bdcd                	j	aa <__riscv_restore_0>

000001ba <main>:
 1ba:	ee7ff2ef          	jal	t0,a0 <__riscv_save_0>
 1be:	4505                	li	a0,1
 1c0:	2ea1                	jal	518 <NVIC_PriorityGroupConfig>
 1c2:	2a5d                	jal	378 <SystemCoreClockUpdate>
 1c4:	2b29                	jal	6de <Delay_Init>
 1c6:	6571                	lui	a0,0x1c
 1c8:	20050513          	addi	a0,a0,512 # 1c200 <_data_lma+0x1b474>
 1cc:	2389                	jal	70e <USART_Printf_Init>
 1ce:	200007b7          	lui	a5,0x20000
 1d2:	0107a583          	lw	a1,16(a5) # 20000010 <SystemCoreClock>
 1d6:	00001537          	lui	a0,0x1
 1da:	d6450513          	addi	a0,a0,-668 # d64 <printf+0x22>
 1de:	365000ef          	jal	ra,d42 <printf>
 1e2:	2c45                	jal	492 <DBGMCU_GetCHIPID>
 1e4:	85aa                	mv	a1,a0
 1e6:	00001537          	lui	a0,0x1
 1ea:	d7450513          	addi	a0,a0,-652 # d74 <printf+0x32>
 1ee:	355000ef          	jal	ra,d42 <printf>
 1f2:	3f89                	jal	144 <USARTx_CFG>
 1f4:	200004b7          	lui	s1,0x20000
 1f8:	40014437          	lui	s0,0x40014
 1fc:	80040793          	addi	a5,s0,-2048 # 40013800 <__global_pointer$+0x20012fc8>
 200:	853e                	mv	a0,a5
 202:	02000593          	li	a1,32
 206:	21f9                	jal	6d4 <USART_GetFlagStatus>
 208:	400147b7          	lui	a5,0x40014
 20c:	80078793          	addi	a5,a5,-2048 # 40013800 <__global_pointer$+0x20012fc8>
 210:	d965                	beqz	a0,200 <main+0x46>
 212:	80040513          	addi	a0,s0,-2048
 216:	295d                	jal	6cc <USART_ReceiveData>
 218:	03848793          	addi	a5,s1,56 # 20000038 <_edata>
 21c:	0ff57513          	andi	a0,a0,255
 220:	a388                	sb	a0,0(a5)
 222:	238c                	lbu	a1,0(a5)
 224:	80040513          	addi	a0,s0,-2048
 228:	40014437          	lui	s0,0x40014
 22c:	fff5c593          	not	a1,a1
 230:	05c2                	slli	a1,a1,0x10
 232:	81c1                	srli	a1,a1,0x10
 234:	2941                	jal	6c4 <USART_SendData>
 236:	80040413          	addi	s0,s0,-2048 # 40013800 <__global_pointer$+0x20012fc8>
 23a:	08000593          	li	a1,128
 23e:	8522                	mv	a0,s0
 240:	2951                	jal	6d4 <USART_GetFlagStatus>
 242:	dd65                	beqz	a0,23a <main+0x80>
 244:	bf55                	j	1f8 <main+0x3e>

00000246 <SystemInit>:
 246:	e5bff2ef          	jal	t0,a0 <__riscv_save_0>
 24a:	40021437          	lui	s0,0x40021
 24e:	401c                	lw	a5,0(s0)
 250:	f8ff0737          	lui	a4,0xf8ff0
 254:	1161                	addi	sp,sp,-8
 256:	0017e793          	ori	a5,a5,1
 25a:	c01c                	sw	a5,0(s0)
 25c:	405c                	lw	a5,4(s0)
 25e:	4541                	li	a0,16
 260:	8ff9                	and	a5,a5,a4
 262:	c05c                	sw	a5,4(s0)
 264:	401c                	lw	a5,0(s0)
 266:	fef70737          	lui	a4,0xfef70
 26a:	177d                	addi	a4,a4,-1
 26c:	8ff9                	and	a5,a5,a4
 26e:	c01c                	sw	a5,0(s0)
 270:	401c                	lw	a5,0(s0)
 272:	fffc0737          	lui	a4,0xfffc0
 276:	177d                	addi	a4,a4,-1
 278:	8ff9                	and	a5,a5,a4
 27a:	c01c                	sw	a5,0(s0)
 27c:	405c                	lw	a5,4(s0)
 27e:	7741                	lui	a4,0xffff0
 280:	177d                	addi	a4,a4,-1
 282:	8ff9                	and	a5,a5,a4
 284:	c05c                	sw	a5,4(s0)
 286:	009f07b7          	lui	a5,0x9f0
 28a:	c41c                	sw	a5,8(s0)
 28c:	2c59                	jal	522 <RCC_AdjustHSICalibrationValue>
 28e:	4c1c                	lw	a5,24(s0)
 290:	00020637          	lui	a2,0x20
 294:	0207e793          	ori	a5,a5,32
 298:	cc1c                	sw	a5,24(s0)
 29a:	400117b7          	lui	a5,0x40011
 29e:	4007a703          	lw	a4,1024(a5) # 40011400 <__global_pointer$+0x20010bc8>
 2a2:	40078693          	addi	a3,a5,1024
 2a6:	f0f77713          	andi	a4,a4,-241
 2aa:	40e7a023          	sw	a4,1024(a5)
 2ae:	4007a703          	lw	a4,1024(a5)
 2b2:	08076713          	ori	a4,a4,128
 2b6:	40e7a023          	sw	a4,1024(a5)
 2ba:	4789                	li	a5,2
 2bc:	ca9c                	sw	a5,16(a3)
 2be:	c002                	sw	zero,0(sp)
 2c0:	c202                	sw	zero,4(sp)
 2c2:	4c1c                	lw	a5,24(s0)
 2c4:	40010737          	lui	a4,0x40010
 2c8:	66a1                	lui	a3,0x8
 2ca:	0017e793          	ori	a5,a5,1
 2ce:	cc1c                	sw	a5,24(s0)
 2d0:	435c                	lw	a5,4(a4)
 2d2:	8fd5                	or	a5,a5,a3
 2d4:	c35c                	sw	a5,4(a4)
 2d6:	401c                	lw	a5,0(s0)
 2d8:	6741                	lui	a4,0x10
 2da:	400216b7          	lui	a3,0x40021
 2de:	8fd9                	or	a5,a5,a4
 2e0:	c01c                	sw	a5,0(s0)
 2e2:	6709                	lui	a4,0x2
 2e4:	429c                	lw	a5,0(a3)
 2e6:	8ff1                	and	a5,a5,a2
 2e8:	c23e                	sw	a5,4(sp)
 2ea:	4782                	lw	a5,0(sp)
 2ec:	0785                	addi	a5,a5,1
 2ee:	c03e                	sw	a5,0(sp)
 2f0:	4792                	lw	a5,4(sp)
 2f2:	e781                	bnez	a5,2fa <SystemInit+0xb4>
 2f4:	4782                	lw	a5,0(sp)
 2f6:	fee797e3          	bne	a5,a4,2e4 <SystemInit+0x9e>
 2fa:	400217b7          	lui	a5,0x40021
 2fe:	439c                	lw	a5,0(a5)
 300:	00e79713          	slli	a4,a5,0xe
 304:	06075863          	bgez	a4,374 <SystemInit+0x12e>
 308:	4785                	li	a5,1
 30a:	c23e                	sw	a5,4(sp)
 30c:	4712                	lw	a4,4(sp)
 30e:	4785                	li	a5,1
 310:	06f71063          	bne	a4,a5,370 <SystemInit+0x12a>
 314:	400227b7          	lui	a5,0x40022
 318:	4398                	lw	a4,0(a5)
 31a:	76c1                	lui	a3,0xffff0
 31c:	16fd                	addi	a3,a3,-1
 31e:	9b71                	andi	a4,a4,-4
 320:	c398                	sw	a4,0(a5)
 322:	4398                	lw	a4,0(a5)
 324:	00176713          	ori	a4,a4,1
 328:	c398                	sw	a4,0(a5)
 32a:	400217b7          	lui	a5,0x40021
 32e:	43d8                	lw	a4,4(a5)
 330:	c3d8                	sw	a4,4(a5)
 332:	43d8                	lw	a4,4(a5)
 334:	8f75                	and	a4,a4,a3
 336:	c3d8                	sw	a4,4(a5)
 338:	43d8                	lw	a4,4(a5)
 33a:	66c1                	lui	a3,0x10
 33c:	8f55                	or	a4,a4,a3
 33e:	c3d8                	sw	a4,4(a5)
 340:	4398                	lw	a4,0(a5)
 342:	010006b7          	lui	a3,0x1000
 346:	8f55                	or	a4,a4,a3
 348:	c398                	sw	a4,0(a5)
 34a:	4398                	lw	a4,0(a5)
 34c:	00671693          	slli	a3,a4,0x6
 350:	fe06dde3          	bgez	a3,34a <SystemInit+0x104>
 354:	43d8                	lw	a4,4(a5)
 356:	400216b7          	lui	a3,0x40021
 35a:	9b71                	andi	a4,a4,-4
 35c:	c3d8                	sw	a4,4(a5)
 35e:	43d8                	lw	a4,4(a5)
 360:	00276713          	ori	a4,a4,2
 364:	c3d8                	sw	a4,4(a5)
 366:	4721                	li	a4,8
 368:	42dc                	lw	a5,4(a3)
 36a:	8bb1                	andi	a5,a5,12
 36c:	fee79ee3          	bne	a5,a4,368 <SystemInit+0x122>
 370:	0121                	addi	sp,sp,8
 372:	bb25                	j	aa <__riscv_restore_0>
 374:	c202                	sw	zero,4(sp)
 376:	bf59                	j	30c <SystemInit+0xc6>

00000378 <SystemCoreClockUpdate>:
 378:	d29ff2ef          	jal	t0,a0 <__riscv_save_0>
 37c:	40021737          	lui	a4,0x40021
 380:	435c                	lw	a5,4(a4)
 382:	20000437          	lui	s0,0x20000
 386:	4691                	li	a3,4
 388:	8bb1                	andi	a5,a5,12
 38a:	01040413          	addi	s0,s0,16 # 20000010 <SystemCoreClock>
 38e:	00d78563          	beq	a5,a3,398 <SystemCoreClockUpdate+0x20>
 392:	46a1                	li	a3,8
 394:	04d78063          	beq	a5,a3,3d4 <SystemCoreClockUpdate+0x5c>
 398:	016e37b7          	lui	a5,0x16e3
 39c:	60078793          	addi	a5,a5,1536 # 16e3600 <_data_lma+0x16e2874>
 3a0:	c01c                	sw	a5,0(s0)
 3a2:	400216b7          	lui	a3,0x40021
 3a6:	42dc                	lw	a5,4(a3)
 3a8:	4008                	lw	a0,0(s0)
 3aa:	8391                	srli	a5,a5,0x4
 3ac:	00f7f713          	andi	a4,a5,15
 3b0:	200007b7          	lui	a5,0x20000
 3b4:	00078793          	mv	a5,a5
 3b8:	97ba                	add	a5,a5,a4
 3ba:	238c                	lbu	a1,0(a5)
 3bc:	42dc                	lw	a5,4(a3)
 3be:	0ff5f593          	andi	a1,a1,255
 3c2:	0807f793          	andi	a5,a5,128
 3c6:	00b55733          	srl	a4,a0,a1
 3ca:	e399                	bnez	a5,3d0 <SystemCoreClockUpdate+0x58>
 3cc:	39c5                	jal	bc <__udivsi3>
 3ce:	872a                	mv	a4,a0
 3d0:	c018                	sw	a4,0(s0)
 3d2:	b9e1                	j	aa <__riscv_restore_0>
 3d4:	435c                	lw	a5,4(a4)
 3d6:	02dc77b7          	lui	a5,0x2dc7
 3da:	c0078793          	addi	a5,a5,-1024 # 2dc6c00 <_data_lma+0x2dc5e74>
 3de:	b7c9                	j	3a0 <SystemCoreClockUpdate+0x28>

000003e0 <ADC1_IRQHandler>:
 3e0:	a001                	j	3e0 <ADC1_IRQHandler>

000003e2 <handle_reset>:
 3e2:	20000197          	auipc	gp,0x20000
 3e6:	45618193          	addi	gp,gp,1110 # 20000838 <__global_pointer$>
 3ea:	fc818113          	addi	sp,gp,-56 # 20000800 <_eusrstack>
 3ee:	0a000513          	li	a0,160
 3f2:	20000597          	auipc	a1,0x20000
 3f6:	c0e58593          	addi	a1,a1,-1010 # 20000000 <_highcode_vma_end>
 3fa:	20000617          	auipc	a2,0x20000
 3fe:	c0660613          	addi	a2,a2,-1018 # 20000000 <_highcode_vma_end>
 402:	00c5fa63          	bgeu	a1,a2,416 <handle_reset+0x34>
 406:	00052283          	lw	t0,0(a0)
 40a:	0055a023          	sw	t0,0(a1)
 40e:	0511                	addi	a0,a0,4
 410:	0591                	addi	a1,a1,4
 412:	fec5eae3          	bltu	a1,a2,406 <handle_reset+0x24>
 416:	00001517          	auipc	a0,0x1
 41a:	97650513          	addi	a0,a0,-1674 # d8c <_data_lma>
 41e:	20000597          	auipc	a1,0x20000
 422:	be258593          	addi	a1,a1,-1054 # 20000000 <_highcode_vma_end>
 426:	20000617          	auipc	a2,0x20000
 42a:	c1260613          	addi	a2,a2,-1006 # 20000038 <_edata>
 42e:	00c5fa63          	bgeu	a1,a2,442 <handle_reset+0x60>
 432:	00052283          	lw	t0,0(a0)
 436:	0055a023          	sw	t0,0(a1)
 43a:	0511                	addi	a0,a0,4
 43c:	0591                	addi	a1,a1,4
 43e:	fec5eae3          	bltu	a1,a2,432 <handle_reset+0x50>
 442:	20000517          	auipc	a0,0x20000
 446:	bf650513          	addi	a0,a0,-1034 # 20000038 <_edata>
 44a:	80c18593          	addi	a1,gp,-2036 # 20000044 <_ebss>
 44e:	00b57763          	bgeu	a0,a1,45c <handle_reset+0x7a>
 452:	00052023          	sw	zero,0(a0)
 456:	0511                	addi	a0,a0,4
 458:	feb56de3          	bltu	a0,a1,452 <handle_reset+0x70>
 45c:	000022b7          	lui	t0,0x2
 460:	88028293          	addi	t0,t0,-1920 # 1880 <_data_lma+0xaf4>
 464:	30029073          	csrw	mstatus,t0
 468:	428d                	li	t0,3
 46a:	80429073          	csrw	0x804,t0
 46e:	00000297          	auipc	t0,0x0
 472:	b9228293          	addi	t0,t0,-1134 # 0 <_sinit>
 476:	0032e293          	ori	t0,t0,3
 47a:	30529073          	csrw	mtvec,t0
 47e:	dc9ff0ef          	jal	ra,246 <SystemInit>
 482:	00000297          	auipc	t0,0x0
 486:	d3828293          	addi	t0,t0,-712 # 1ba <main>
 48a:	34129073          	csrw	mepc,t0
 48e:	30200073          	mret

00000492 <DBGMCU_GetCHIPID>:
 492:	1ffff7b7          	lui	a5,0x1ffff
 496:	7c47a503          	lw	a0,1988(a5) # 1ffff7c4 <_data_lma+0x1fffea38>
 49a:	8082                	ret

0000049c <GPIO_Init>:
 49c:	4594                	lw	a3,8(a1)
 49e:	0106f793          	andi	a5,a3,16
 4a2:	00f6f293          	andi	t0,a3,15
 4a6:	c781                	beqz	a5,4ae <GPIO_Init+0x12>
 4a8:	41dc                	lw	a5,4(a1)
 4aa:	00f2e2b3          	or	t0,t0,a5
 4ae:	0005d383          	lhu	t2,0(a1)
 4b2:	0ff3f793          	andi	a5,t2,255
 4b6:	c3a5                	beqz	a5,516 <GPIO_Init+0x7a>
 4b8:	00052303          	lw	t1,0(a0)
 4bc:	1161                	addi	sp,sp,-8
 4be:	c222                	sw	s0,4(sp)
 4c0:	c026                	sw	s1,0(sp)
 4c2:	4781                	li	a5,0
 4c4:	02800413          	li	s0,40
 4c8:	04800493          	li	s1,72
 4cc:	4705                	li	a4,1
 4ce:	00f71633          	sll	a2,a4,a5
 4d2:	00c3f733          	and	a4,t2,a2
 4d6:	02e61263          	bne	a2,a4,4fa <GPIO_Init+0x5e>
 4da:	00279593          	slli	a1,a5,0x2
 4de:	473d                	li	a4,15
 4e0:	00b71733          	sll	a4,a4,a1
 4e4:	fff74713          	not	a4,a4
 4e8:	00677333          	and	t1,a4,t1
 4ec:	00b295b3          	sll	a1,t0,a1
 4f0:	0065e333          	or	t1,a1,t1
 4f4:	00869d63          	bne	a3,s0,50e <GPIO_Init+0x72>
 4f8:	c950                	sw	a2,20(a0)
 4fa:	0785                	addi	a5,a5,1
 4fc:	4721                	li	a4,8
 4fe:	fce797e3          	bne	a5,a4,4cc <GPIO_Init+0x30>
 502:	4412                	lw	s0,4(sp)
 504:	00652023          	sw	t1,0(a0)
 508:	4482                	lw	s1,0(sp)
 50a:	0121                	addi	sp,sp,8
 50c:	8082                	ret
 50e:	fe9696e3          	bne	a3,s1,4fa <GPIO_Init+0x5e>
 512:	c910                	sw	a2,16(a0)
 514:	b7dd                	j	4fa <GPIO_Init+0x5e>
 516:	8082                	ret

00000518 <NVIC_PriorityGroupConfig>:
 518:	200007b7          	lui	a5,0x20000
 51c:	02a7ae23          	sw	a0,60(a5) # 2000003c <NVIC_Priority_Group>
 520:	8082                	ret

00000522 <RCC_AdjustHSICalibrationValue>:
 522:	40021737          	lui	a4,0x40021
 526:	431c                	lw	a5,0(a4)
 528:	050e                	slli	a0,a0,0x3
 52a:	f077f793          	andi	a5,a5,-249
 52e:	8d5d                	or	a0,a0,a5
 530:	c308                	sw	a0,0(a4)
 532:	8082                	ret

00000534 <RCC_GetClocksFreq>:
 534:	b6dff2ef          	jal	t0,a0 <__riscv_save_0>
 538:	40021737          	lui	a4,0x40021
 53c:	435c                	lw	a5,4(a4)
 53e:	4691                	li	a3,4
 540:	842a                	mv	s0,a0
 542:	8bb1                	andi	a5,a5,12
 544:	00d78563          	beq	a5,a3,54e <RCC_GetClocksFreq+0x1a>
 548:	46a1                	li	a3,8
 54a:	06d78d63          	beq	a5,a3,5c4 <RCC_GetClocksFreq+0x90>
 54e:	016e37b7          	lui	a5,0x16e3
 552:	60078793          	addi	a5,a5,1536 # 16e3600 <_data_lma+0x16e2874>
 556:	c01c                	sw	a5,0(s0)
 558:	400216b7          	lui	a3,0x40021
 55c:	42dc                	lw	a5,4(a3)
 55e:	8391                	srli	a5,a5,0x4
 560:	00f7f713          	andi	a4,a5,15
 564:	200007b7          	lui	a5,0x20000
 568:	02878793          	addi	a5,a5,40 # 20000028 <APBAHBPrescTable>
 56c:	97ba                	add	a5,a5,a4
 56e:	238c                	lbu	a1,0(a5)
 570:	42dc                	lw	a5,4(a3)
 572:	4018                	lw	a4,0(s0)
 574:	0ff5f593          	andi	a1,a1,255
 578:	0807f793          	andi	a5,a5,128
 57c:	00b75533          	srl	a0,a4,a1
 580:	e399                	bnez	a5,586 <RCC_GetClocksFreq+0x52>
 582:	853a                	mv	a0,a4
 584:	3e25                	jal	bc <__udivsi3>
 586:	c048                	sw	a0,4(s0)
 588:	c408                	sw	a0,8(s0)
 58a:	c448                	sw	a0,12(s0)
 58c:	400217b7          	lui	a5,0x40021
 590:	43dc                	lw	a5,4(a5)
 592:	468d                	li	a3,3
 594:	83ad                	srli	a5,a5,0xb
 596:	8bfd                	andi	a5,a5,31
 598:	0037d713          	srli	a4,a5,0x3
 59c:	078a                	slli	a5,a5,0x2
 59e:	8bf1                	andi	a5,a5,28
 5a0:	8fd9                	or	a5,a5,a4
 5a2:	0137f613          	andi	a2,a5,19
 5a6:	0037f713          	andi	a4,a5,3
 5aa:	00c6f463          	bgeu	a3,a2,5b2 <RCC_GetClocksFreq+0x7e>
 5ae:	ff478713          	addi	a4,a5,-12 # 40020ff4 <__global_pointer$+0x200207bc>
 5b2:	200007b7          	lui	a5,0x20000
 5b6:	01478793          	addi	a5,a5,20 # 20000014 <ADCPrescTable>
 5ba:	97ba                	add	a5,a5,a4
 5bc:	238c                	lbu	a1,0(a5)
 5be:	3cfd                	jal	bc <__udivsi3>
 5c0:	c808                	sw	a0,16(s0)
 5c2:	b4e5                	j	aa <__riscv_restore_0>
 5c4:	435c                	lw	a5,4(a4)
 5c6:	02dc77b7          	lui	a5,0x2dc7
 5ca:	c0078793          	addi	a5,a5,-1024 # 2dc6c00 <_data_lma+0x2dc5e74>
 5ce:	b761                	j	556 <RCC_GetClocksFreq+0x22>

000005d0 <RCC_APB2PeriphClockCmd>:
 5d0:	c599                	beqz	a1,5de <RCC_APB2PeriphClockCmd+0xe>
 5d2:	40021737          	lui	a4,0x40021
 5d6:	4f1c                	lw	a5,24(a4)
 5d8:	8d5d                	or	a0,a0,a5
 5da:	cf08                	sw	a0,24(a4)
 5dc:	8082                	ret
 5de:	400217b7          	lui	a5,0x40021
 5e2:	4f98                	lw	a4,24(a5)
 5e4:	fff54513          	not	a0,a0
 5e8:	8d79                	and	a0,a0,a4
 5ea:	cf88                	sw	a0,24(a5)
 5ec:	8082                	ret

000005ee <USART_Init>:
 5ee:	ab3ff2ef          	jal	t0,a0 <__riscv_save_0>
 5f2:	2916                	lhu	a3,16(a0)
 5f4:	77f5                	lui	a5,0xffffd
 5f6:	17fd                	addi	a5,a5,-1
 5f8:	8ff5                	and	a5,a5,a3
 5fa:	21f6                	lhu	a3,6(a1)
 5fc:	25da                	lhu	a4,12(a1)
 5fe:	1121                	addi	sp,sp,-24
 600:	8fd5                	or	a5,a5,a3
 602:	a91e                	sh	a5,16(a0)
 604:	2556                	lhu	a3,12(a0)
 606:	77fd                	lui	a5,0xfffff
 608:	9f378793          	addi	a5,a5,-1549 # ffffe9f3 <__global_pointer$+0xdfffe1bb>
 60c:	8ff5                	and	a5,a5,a3
 60e:	21d6                	lhu	a3,4(a1)
 610:	842a                	mv	s0,a0
 612:	c02e                	sw	a1,0(sp)
 614:	8fd5                	or	a5,a5,a3
 616:	2596                	lhu	a3,8(a1)
 618:	8fd5                	or	a5,a5,a3
 61a:	25b6                	lhu	a3,10(a1)
 61c:	8fd5                	or	a5,a5,a3
 61e:	a55e                	sh	a5,12(a0)
 620:	295e                	lhu	a5,20(a0)
 622:	07c2                	slli	a5,a5,0x10
 624:	83c1                	srli	a5,a5,0x10
 626:	cff7f793          	andi	a5,a5,-769
 62a:	8f5d                	or	a4,a4,a5
 62c:	a95a                	sh	a4,20(a0)
 62e:	0048                	addi	a0,sp,4
 630:	3711                	jal	534 <RCC_GetClocksFreq>
 632:	400147b7          	lui	a5,0x40014
 636:	80078793          	addi	a5,a5,-2048 # 40013800 <__global_pointer$+0x20012fc8>
 63a:	4582                	lw	a1,0(sp)
 63c:	04f41d63          	bne	s0,a5,696 <USART_Init+0xa8>
 640:	47c2                	lw	a5,16(sp)
 642:	245a                	lhu	a4,12(s0)
 644:	00179513          	slli	a0,a5,0x1
 648:	953e                	add	a0,a0,a5
 64a:	0742                	slli	a4,a4,0x10
 64c:	050e                	slli	a0,a0,0x3
 64e:	8741                	srai	a4,a4,0x10
 650:	953e                	add	a0,a0,a5
 652:	418c                	lw	a1,0(a1)
 654:	04075363          	bgez	a4,69a <USART_Init+0xac>
 658:	0586                	slli	a1,a1,0x1
 65a:	348d                	jal	bc <__udivsi3>
 65c:	06400593          	li	a1,100
 660:	c02a                	sw	a0,0(sp)
 662:	3ca9                	jal	bc <__udivsi3>
 664:	4782                	lw	a5,0(sp)
 666:	00451493          	slli	s1,a0,0x4
 66a:	06400593          	li	a1,100
 66e:	853e                	mv	a0,a5
 670:	3ca5                	jal	e8 <__umodsi3>
 672:	245e                	lhu	a5,12(s0)
 674:	07c2                	slli	a5,a5,0x10
 676:	87c1                	srai	a5,a5,0x10
 678:	0207d363          	bgez	a5,69e <USART_Init+0xb0>
 67c:	050e                	slli	a0,a0,0x3
 67e:	06400593          	li	a1,100
 682:	03250513          	addi	a0,a0,50
 686:	3c1d                	jal	bc <__udivsi3>
 688:	891d                	andi	a0,a0,7
 68a:	8cc9                	or	s1,s1,a0
 68c:	04c2                	slli	s1,s1,0x10
 68e:	80c1                	srli	s1,s1,0x10
 690:	a406                	sh	s1,8(s0)
 692:	0161                	addi	sp,sp,24
 694:	bc19                	j	aa <__riscv_restore_0>
 696:	47b2                	lw	a5,12(sp)
 698:	b76d                	j	642 <USART_Init+0x54>
 69a:	058a                	slli	a1,a1,0x2
 69c:	bf7d                	j	65a <USART_Init+0x6c>
 69e:	0512                	slli	a0,a0,0x4
 6a0:	06400593          	li	a1,100
 6a4:	03250513          	addi	a0,a0,50
 6a8:	3c11                	jal	bc <__udivsi3>
 6aa:	893d                	andi	a0,a0,15
 6ac:	bff9                	j	68a <USART_Init+0x9c>

000006ae <USART_Cmd>:
 6ae:	c591                	beqz	a1,6ba <USART_Cmd+0xc>
 6b0:	255e                	lhu	a5,12(a0)
 6b2:	6709                	lui	a4,0x2
 6b4:	8fd9                	or	a5,a5,a4
 6b6:	a55e                	sh	a5,12(a0)
 6b8:	8082                	ret
 6ba:	255a                	lhu	a4,12(a0)
 6bc:	77f9                	lui	a5,0xffffe
 6be:	17fd                	addi	a5,a5,-1
 6c0:	8ff9                	and	a5,a5,a4
 6c2:	bfd5                	j	6b6 <USART_Cmd+0x8>

000006c4 <USART_SendData>:
 6c4:	1ff5f593          	andi	a1,a1,511
 6c8:	a14e                	sh	a1,4(a0)
 6ca:	8082                	ret

000006cc <USART_ReceiveData>:
 6cc:	214a                	lhu	a0,4(a0)
 6ce:	1ff57513          	andi	a0,a0,511
 6d2:	8082                	ret

000006d4 <USART_GetFlagStatus>:
 6d4:	210a                	lhu	a0,0(a0)
 6d6:	8d6d                	and	a0,a0,a1
 6d8:	00a03533          	snez	a0,a0
 6dc:	8082                	ret

000006de <Delay_Init>:
 6de:	9c3ff2ef          	jal	t0,a0 <__riscv_save_0>
 6e2:	200007b7          	lui	a5,0x20000
 6e6:	0107a503          	lw	a0,16(a5) # 20000010 <SystemCoreClock>
 6ea:	007a15b7          	lui	a1,0x7a1
 6ee:	20058593          	addi	a1,a1,512 # 7a1200 <_data_lma+0x7a0474>
 6f2:	32e9                	jal	bc <__udivsi3>
 6f4:	0ff57513          	andi	a0,a0,255
 6f8:	80a18523          	sb	a0,-2038(gp) # 20000042 <p_us>
 6fc:	00551793          	slli	a5,a0,0x5
 700:	8f89                	sub	a5,a5,a0
 702:	078a                	slli	a5,a5,0x2
 704:	953e                	add	a0,a0,a5
 706:	050e                	slli	a0,a0,0x3
 708:	80a19423          	sh	a0,-2040(gp) # 20000040 <p_ms>
 70c:	ba79                	j	aa <__riscv_restore_0>

0000070e <USART_Printf_Init>:
 70e:	993ff2ef          	jal	t0,a0 <__riscv_save_0>
 712:	842a                	mv	s0,a0
 714:	6511                	lui	a0,0x4
 716:	1111                	addi	sp,sp,-28
 718:	4585                	li	a1,1
 71a:	02050513          	addi	a0,a0,32 # 4020 <_data_lma+0x3294>
 71e:	3d4d                	jal	5d0 <RCC_APB2PeriphClockCmd>
 720:	02000793          	li	a5,32
 724:	807c                	sh	a5,0(sp)
 726:	40011537          	lui	a0,0x40011
 72a:	478d                	li	a5,3
 72c:	c23e                	sw	a5,4(sp)
 72e:	858a                	mv	a1,sp
 730:	47e1                	li	a5,24
 732:	40050513          	addi	a0,a0,1024 # 40011400 <__global_pointer$+0x20010bc8>
 736:	c43e                	sw	a5,8(sp)
 738:	3395                	jal	49c <GPIO_Init>
 73a:	c622                	sw	s0,12(sp)
 73c:	40014437          	lui	s0,0x40014
 740:	000807b7          	lui	a5,0x80
 744:	006c                	addi	a1,sp,12
 746:	80040513          	addi	a0,s0,-2048 # 40013800 <__global_pointer$+0x20012fc8>
 74a:	ca3e                	sw	a5,20(sp)
 74c:	c802                	sw	zero,16(sp)
 74e:	00011c23          	sh	zero,24(sp)
 752:	3d71                	jal	5ee <USART_Init>
 754:	4585                	li	a1,1
 756:	80040513          	addi	a0,s0,-2048
 75a:	3f91                	jal	6ae <USART_Cmd>
 75c:	0171                	addi	sp,sp,28
 75e:	b2b1                	j	aa <__riscv_restore_0>

00000760 <_write>:
 760:	941ff2ef          	jal	t0,a0 <__riscv_save_0>
 764:	1171                	addi	sp,sp,-4
 766:	84ae                	mv	s1,a1
 768:	4401                	li	s0,0
 76a:	02c45d63          	bge	s0,a2,7a4 <_write+0x44>
 76e:	400147b7          	lui	a5,0x40014
 772:	80078713          	addi	a4,a5,-2048 # 40013800 <__global_pointer$+0x20012fc8>
 776:	853a                	mv	a0,a4
 778:	04000593          	li	a1,64
 77c:	c032                	sw	a2,0(sp)
 77e:	3f99                	jal	6d4 <USART_GetFlagStatus>
 780:	400147b7          	lui	a5,0x40014
 784:	80078713          	addi	a4,a5,-2048 # 40013800 <__global_pointer$+0x20012fc8>
 788:	4602                	lw	a2,0(sp)
 78a:	d575                	beqz	a0,776 <_write+0x16>
 78c:	00848733          	add	a4,s1,s0
 790:	00070583          	lb	a1,0(a4) # 2000 <_data_lma+0x1274>
 794:	80078513          	addi	a0,a5,-2048
 798:	0405                	addi	s0,s0,1
 79a:	05c2                	slli	a1,a1,0x10
 79c:	81c1                	srli	a1,a1,0x10
 79e:	371d                	jal	6c4 <USART_SendData>
 7a0:	4602                	lw	a2,0(sp)
 7a2:	b7e1                	j	76a <_write+0xa>
 7a4:	8532                	mv	a0,a2
 7a6:	0111                	addi	sp,sp,4
 7a8:	903ff06f          	j	aa <__riscv_restore_0>

000007ac <printchar>:
 7ac:	1141                	addi	sp,sp,-16
 7ae:	c606                	sw	ra,12(sp)
 7b0:	c02e                	sw	a1,0(sp)
 7b2:	cd0d                	beqz	a0,7ec <printchar+0x40>
 7b4:	4118                	lw	a4,0(a0)
 7b6:	87aa                	mv	a5,a0
 7b8:	c305                	beqz	a4,7d8 <printchar+0x2c>
 7ba:	4158                	lw	a4,4(a0)
 7bc:	557d                	li	a0,-1
 7be:	cb11                	beqz	a4,7d2 <printchar+0x26>
 7c0:	4685                	li	a3,1
 7c2:	00d71b63          	bne	a4,a3,7d8 <printchar+0x2c>
 7c6:	4798                	lw	a4,8(a5)
 7c8:	00070023          	sb	zero,0(a4)
 7cc:	0007a223          	sw	zero,4(a5)
 7d0:	4505                	li	a0,1
 7d2:	40b2                	lw	ra,12(sp)
 7d4:	0141                	addi	sp,sp,16
 7d6:	8082                	ret
 7d8:	4798                	lw	a4,8(a5)
 7da:	4682                	lw	a3,0(sp)
 7dc:	a314                	sb	a3,0(a4)
 7de:	4798                	lw	a4,8(a5)
 7e0:	0705                	addi	a4,a4,1
 7e2:	c798                	sw	a4,8(a5)
 7e4:	43d8                	lw	a4,4(a5)
 7e6:	177d                	addi	a4,a4,-1
 7e8:	c3d8                	sw	a4,4(a5)
 7ea:	b7dd                	j	7d0 <printchar+0x24>
 7ec:	4605                	li	a2,1
 7ee:	858a                	mv	a1,sp
 7f0:	3f85                	jal	760 <_write>
 7f2:	bff9                	j	7d0 <printchar+0x24>

000007f4 <prints>:
 7f4:	1101                	addi	sp,sp,-32
 7f6:	cc22                	sw	s0,24(sp)
 7f8:	c22e                	sw	a1,4(sp)
 7fa:	ce06                	sw	ra,28(sp)
 7fc:	ca26                	sw	s1,20(sp)
 7fe:	842a                	mv	s0,a0
 800:	4781                	li	a5,0
 802:	02000593          	li	a1,32
 806:	02064563          	bltz	a2,830 <prints+0x3c>
 80a:	4592                	lw	a1,4(sp)
 80c:	95be                	add	a1,a1,a5
 80e:	00058583          	lb	a1,0(a1)
 812:	e58d                	bnez	a1,83c <prints+0x48>
 814:	02c7d863          	bge	a5,a2,844 <prints+0x50>
 818:	02e7d463          	bge	a5,a4,840 <prints+0x4c>
 81c:	8e19                	sub	a2,a2,a4
 81e:	02000513          	li	a0,32
 822:	0026f593          	andi	a1,a3,2
 826:	c02a                	sw	a0,0(sp)
 828:	c589                	beqz	a1,832 <prints+0x3e>
 82a:	e701                	bnez	a4,832 <prints+0x3e>
 82c:	03000593          	li	a1,48
 830:	c02e                	sw	a1,0(sp)
 832:	8a85                	andi	a3,a3,1
 834:	4481                	li	s1,0
 836:	ea95                	bnez	a3,86a <prints+0x76>
 838:	84b2                	mv	s1,a2
 83a:	a00d                	j	85c <prints+0x68>
 83c:	0785                	addi	a5,a5,1
 83e:	b7f1                	j	80a <prints+0x16>
 840:	8e1d                	sub	a2,a2,a5
 842:	bff1                	j	81e <prints+0x2a>
 844:	4601                	li	a2,0
 846:	bfe1                	j	81e <prints+0x2a>
 848:	4582                	lw	a1,0(sp)
 84a:	8522                	mv	a0,s0
 84c:	c83a                	sw	a4,16(sp)
 84e:	c632                	sw	a2,12(sp)
 850:	c43e                	sw	a5,8(sp)
 852:	3fa9                	jal	7ac <printchar>
 854:	47a2                	lw	a5,8(sp)
 856:	4632                	lw	a2,12(sp)
 858:	4742                	lw	a4,16(sp)
 85a:	14fd                	addi	s1,s1,-1
 85c:	fe9046e3          	bgtz	s1,848 <prints+0x54>
 860:	84b2                	mv	s1,a2
 862:	00065363          	bgez	a2,868 <prints+0x74>
 866:	4481                	li	s1,0
 868:	8e05                	sub	a2,a2,s1
 86a:	02e7c763          	blt	a5,a4,898 <prints+0xa4>
 86e:	87a6                	mv	a5,s1
 870:	4692                	lw	a3,4(sp)
 872:	40978733          	sub	a4,a5,s1
 876:	9736                	add	a4,a4,a3
 878:	00070583          	lb	a1,0(a4)
 87c:	ed95                	bnez	a1,8b8 <prints+0xc4>
 87e:	84b2                	mv	s1,a2
 880:	04904463          	bgtz	s1,8c8 <prints+0xd4>
 884:	00065363          	bgez	a2,88a <prints+0x96>
 888:	4601                	li	a2,0
 88a:	40f2                	lw	ra,28(sp)
 88c:	4462                	lw	s0,24(sp)
 88e:	44d2                	lw	s1,20(sp)
 890:	00f60533          	add	a0,a2,a5
 894:	6105                	addi	sp,sp,32
 896:	8082                	ret
 898:	8f1d                	sub	a4,a4,a5
 89a:	87ba                	mv	a5,a4
 89c:	03000593          	li	a1,48
 8a0:	8522                	mv	a0,s0
 8a2:	c832                	sw	a2,16(sp)
 8a4:	c63e                	sw	a5,12(sp)
 8a6:	c43a                	sw	a4,8(sp)
 8a8:	3711                	jal	7ac <printchar>
 8aa:	47b2                	lw	a5,12(sp)
 8ac:	4722                	lw	a4,8(sp)
 8ae:	4642                	lw	a2,16(sp)
 8b0:	17fd                	addi	a5,a5,-1
 8b2:	f7ed                	bnez	a5,89c <prints+0xa8>
 8b4:	94ba                	add	s1,s1,a4
 8b6:	bf65                	j	86e <prints+0x7a>
 8b8:	8522                	mv	a0,s0
 8ba:	c632                	sw	a2,12(sp)
 8bc:	c43e                	sw	a5,8(sp)
 8be:	35fd                	jal	7ac <printchar>
 8c0:	47a2                	lw	a5,8(sp)
 8c2:	4632                	lw	a2,12(sp)
 8c4:	0785                	addi	a5,a5,1
 8c6:	b76d                	j	870 <prints+0x7c>
 8c8:	4582                	lw	a1,0(sp)
 8ca:	8522                	mv	a0,s0
 8cc:	c432                	sw	a2,8(sp)
 8ce:	c23e                	sw	a5,4(sp)
 8d0:	3df1                	jal	7ac <printchar>
 8d2:	14fd                	addi	s1,s1,-1
 8d4:	4622                	lw	a2,8(sp)
 8d6:	4792                	lw	a5,4(sp)
 8d8:	b765                	j	880 <prints+0x8c>

000008da <printInt>:
 8da:	7139                	addi	sp,sp,-64
 8dc:	de06                	sw	ra,60(sp)
 8de:	dc22                	sw	s0,56(sp)
 8e0:	da26                	sw	s1,52(sp)
 8e2:	c23e                	sw	a5,4(sp)
 8e4:	8332                	mv	t1,a2
 8e6:	863a                	mv	a2,a4
 8e8:	ed89                	bnez	a1,902 <printInt+0x28>
 8ea:	4692                	lw	a3,4(sp)
 8ec:	03000793          	li	a5,48
 8f0:	4701                	li	a4,0
 8f2:	086c                	addi	a1,sp,28
 8f4:	86fc                	sh	a5,28(sp)
 8f6:	3dfd                	jal	7f4 <prints>
 8f8:	50f2                	lw	ra,60(sp)
 8fa:	5462                	lw	s0,56(sp)
 8fc:	54d2                	lw	s1,52(sp)
 8fe:	6121                	addi	sp,sp,64
 900:	8082                	ret
 902:	84aa                	mv	s1,a0
 904:	8436                	mv	s0,a3
 906:	87ae                	mv	a5,a1
 908:	ca91                	beqz	a3,91c <printInt+0x42>
 90a:	4729                	li	a4,10
 90c:	4401                	li	s0,0
 90e:	00e31763          	bne	t1,a4,91c <printInt+0x42>
 912:	0005d563          	bgez	a1,91c <printInt+0x42>
 916:	40b007b3          	neg	a5,a1
 91a:	4405                	li	s0,1
 91c:	4686                	lw	a3,64(sp)
 91e:	020109a3          	sb	zero,51(sp)
 922:	03310713          	addi	a4,sp,51
 926:	fc668693          	addi	a3,a3,-58 # 40020fc6 <__global_pointer$+0x2002078e>
 92a:	c436                	sw	a3,8(sp)
 92c:	859a                	mv	a1,t1
 92e:	853e                	mv	a0,a5
 930:	ca32                	sw	a2,20(sp)
 932:	c83a                	sw	a4,16(sp)
 934:	c61a                	sw	t1,12(sp)
 936:	c03e                	sw	a5,0(sp)
 938:	fb0ff0ef          	jal	ra,e8 <__umodsi3>
 93c:	46a5                	li	a3,9
 93e:	4782                	lw	a5,0(sp)
 940:	4332                	lw	t1,12(sp)
 942:	4742                	lw	a4,16(sp)
 944:	4652                	lw	a2,20(sp)
 946:	00a6d463          	bge	a3,a0,94e <printInt+0x74>
 94a:	46a2                	lw	a3,8(sp)
 94c:	9536                	add	a0,a0,a3
 94e:	03050513          	addi	a0,a0,48
 952:	fff70693          	addi	a3,a4,-1
 956:	fea70fa3          	sb	a0,-1(a4)
 95a:	859a                	mv	a1,t1
 95c:	853e                	mv	a0,a5
 95e:	cc32                	sw	a2,24(sp)
 960:	ca3a                	sw	a4,20(sp)
 962:	c81a                	sw	t1,16(sp)
 964:	c63e                	sw	a5,12(sp)
 966:	c036                	sw	a3,0(sp)
 968:	f54ff0ef          	jal	ra,bc <__udivsi3>
 96c:	47b2                	lw	a5,12(sp)
 96e:	4342                	lw	t1,16(sp)
 970:	4752                	lw	a4,20(sp)
 972:	4662                	lw	a2,24(sp)
 974:	0467f963          	bgeu	a5,t1,9c6 <printInt+0xec>
 978:	cc01                	beqz	s0,990 <printInt+0xb6>
 97a:	ca29                	beqz	a2,9cc <printInt+0xf2>
 97c:	4792                	lw	a5,4(sp)
 97e:	8b89                	andi	a5,a5,2
 980:	c7b1                	beqz	a5,9cc <printInt+0xf2>
 982:	02d00593          	li	a1,45
 986:	8526                	mv	a0,s1
 988:	c432                	sw	a2,8(sp)
 98a:	350d                	jal	7ac <printchar>
 98c:	4622                	lw	a2,8(sp)
 98e:	167d                	addi	a2,a2,-1
 990:	4792                	lw	a5,4(sp)
 992:	8b91                	andi	a5,a5,4
 994:	c395                	beqz	a5,9b8 <printInt+0xde>
 996:	4706                	lw	a4,64(sp)
 998:	06100793          	li	a5,97
 99c:	c432                	sw	a2,8(sp)
 99e:	03000593          	li	a1,48
 9a2:	8526                	mv	a0,s1
 9a4:	02f71e63          	bne	a4,a5,9e0 <printInt+0x106>
 9a8:	3511                	jal	7ac <printchar>
 9aa:	07800593          	li	a1,120
 9ae:	8526                	mv	a0,s1
 9b0:	3bf5                	jal	7ac <printchar>
 9b2:	4622                	lw	a2,8(sp)
 9b4:	0409                	addi	s0,s0,2
 9b6:	1679                	addi	a2,a2,-2
 9b8:	4716                	lw	a4,68(sp)
 9ba:	4692                	lw	a3,4(sp)
 9bc:	4582                	lw	a1,0(sp)
 9be:	8526                	mv	a0,s1
 9c0:	3d15                	jal	7f4 <prints>
 9c2:	9522                	add	a0,a0,s0
 9c4:	bf15                	j	8f8 <printInt+0x1e>
 9c6:	87aa                	mv	a5,a0
 9c8:	4702                	lw	a4,0(sp)
 9ca:	b78d                	j	92c <printInt+0x52>
 9cc:	4682                	lw	a3,0(sp)
 9ce:	02d00793          	li	a5,45
 9d2:	4401                	li	s0,0
 9d4:	fef68fa3          	sb	a5,-1(a3)
 9d8:	ffe70793          	addi	a5,a4,-2
 9dc:	c03e                	sw	a5,0(sp)
 9de:	bf4d                	j	990 <printInt+0xb6>
 9e0:	33f1                	jal	7ac <printchar>
 9e2:	05800593          	li	a1,88
 9e6:	b7e1                	j	9ae <printInt+0xd4>

000009e8 <printLongLongInt>:
 9e8:	4501                	li	a0,0
 9ea:	8082                	ret

000009ec <printDouble>:
 9ec:	4501                	li	a0,0
 9ee:	8082                	ret

000009f0 <print>:
 9f0:	fd810113          	addi	sp,sp,-40
 9f4:	d022                	sw	s0,32(sp)
 9f6:	ce26                	sw	s1,28(sp)
 9f8:	d206                	sw	ra,36(sp)
 9fa:	c42a                	sw	a0,8(sp)
 9fc:	82ae                	mv	t0,a1
 9fe:	8432                	mv	s0,a2
 a00:	c602                	sw	zero,12(sp)
 a02:	4481                	li	s1,0
 a04:	00028583          	lb	a1,0(t0)
 a08:	ed89                	bnez	a1,a22 <print+0x32>
 a0a:	47a2                	lw	a5,8(sp)
 a0c:	c781                	beqz	a5,a14 <print+0x24>
 a0e:	4581                	li	a1,0
 a10:	853e                	mv	a0,a5
 a12:	3b69                	jal	7ac <printchar>
 a14:	5092                	lw	ra,36(sp)
 a16:	5402                	lw	s0,32(sp)
 a18:	8526                	mv	a0,s1
 a1a:	44f2                	lw	s1,28(sp)
 a1c:	02810113          	addi	sp,sp,40
 a20:	8082                	ret
 a22:	02500793          	li	a5,37
 a26:	00f58863          	beq	a1,a5,a36 <print+0x46>
 a2a:	4522                	lw	a0,8(sp)
 a2c:	c816                	sw	t0,16(sp)
 a2e:	0485                	addi	s1,s1,1
 a30:	3bb5                	jal	7ac <printchar>
 a32:	42c2                	lw	t0,16(sp)
 a34:	a839                	j	a52 <print+0x62>
 a36:	00128783          	lb	a5,1(t0)
 a3a:	00128713          	addi	a4,t0,1
 a3e:	00b79c63          	bne	a5,a1,a56 <print+0x66>
 a42:	4522                	lw	a0,8(sp)
 a44:	02500593          	li	a1,37
 a48:	c83a                	sw	a4,16(sp)
 a4a:	338d                	jal	7ac <printchar>
 a4c:	4742                	lw	a4,16(sp)
 a4e:	0485                	addi	s1,s1,1
 a50:	82ba                	mv	t0,a4
 a52:	0285                	addi	t0,t0,1
 a54:	bf45                	j	a04 <print+0x14>
 a56:	dbd5                	beqz	a5,a0a <print+0x1a>
 a58:	02b00693          	li	a3,43
 a5c:	04d78963          	beq	a5,a3,aae <print+0xbe>
 a60:	00f6c863          	blt	a3,a5,a70 <print+0x80>
 a64:	02300693          	li	a3,35
 a68:	04d78663          	beq	a5,a3,ab4 <print+0xc4>
 a6c:	4781                	li	a5,0
 a6e:	a005                	j	a8e <print+0x9e>
 a70:	02d00693          	li	a3,45
 a74:	00d78a63          	beq	a5,a3,a88 <print+0x98>
 a78:	03000693          	li	a3,48
 a7c:	fed798e3          	bne	a5,a3,a6c <print+0x7c>
 a80:	00228713          	addi	a4,t0,2
 a84:	4789                	li	a5,2
 a86:	a021                	j	a8e <print+0x9e>
 a88:	00228713          	addi	a4,t0,2
 a8c:	4785                	li	a5,1
 a8e:	00070683          	lb	a3,0(a4)
 a92:	02b00613          	li	a2,43
 a96:	04c68363          	beq	a3,a2,adc <print+0xec>
 a9a:	02d64163          	blt	a2,a3,abc <print+0xcc>
 a9e:	02300613          	li	a2,35
 aa2:	02c68b63          	beq	a3,a2,ad8 <print+0xe8>
 aa6:	82ba                	mv	t0,a4
 aa8:	4501                	li	a0,0
 aaa:	46a5                	li	a3,9
 aac:	a081                	j	aec <print+0xfc>
 aae:	00228713          	addi	a4,t0,2
 ab2:	bf6d                	j	a6c <print+0x7c>
 ab4:	00228713          	addi	a4,t0,2
 ab8:	4791                	li	a5,4
 aba:	bfd1                	j	a8e <print+0x9e>
 abc:	02d00613          	li	a2,45
 ac0:	00c68963          	beq	a3,a2,ad2 <print+0xe2>
 ac4:	03000613          	li	a2,48
 ac8:	fcc69fe3          	bne	a3,a2,aa6 <print+0xb6>
 acc:	0027e793          	ori	a5,a5,2
 ad0:	a031                	j	adc <print+0xec>
 ad2:	0705                	addi	a4,a4,1
 ad4:	4785                	li	a5,1
 ad6:	bfc1                	j	aa6 <print+0xb6>
 ad8:	0047e793          	ori	a5,a5,4
 adc:	0705                	addi	a4,a4,1
 ade:	b7e1                	j	aa6 <print+0xb6>
 ae0:	00251613          	slli	a2,a0,0x2
 ae4:	9532                	add	a0,a0,a2
 ae6:	0506                	slli	a0,a0,0x1
 ae8:	953a                	add	a0,a0,a4
 aea:	0285                	addi	t0,t0,1
 aec:	00028603          	lb	a2,0(t0)
 af0:	fd060713          	addi	a4,a2,-48
 af4:	0ff77593          	andi	a1,a4,255
 af8:	feb6f4e3          	bgeu	a3,a1,ae0 <print+0xf0>
 afc:	02e00713          	li	a4,46
 b00:	4699                	li	a3,6
 b02:	00e61e63          	bne	a2,a4,b1e <print+0x12e>
 b06:	0285                	addi	t0,t0,1
 b08:	4681                	li	a3,0
 b0a:	45a5                	li	a1,9
 b0c:	00028603          	lb	a2,0(t0)
 b10:	fd060613          	addi	a2,a2,-48
 b14:	0ff67713          	andi	a4,a2,255
 b18:	02e5f563          	bgeu	a1,a4,b42 <print+0x152>
 b1c:	c636                	sw	a3,12(sp)
 b1e:	00028703          	lb	a4,0(t0)
 b22:	06a00613          	li	a2,106
 b26:	0ac70d63          	beq	a4,a2,be0 <print+0x1f0>
 b2a:	02e64363          	blt	a2,a4,b50 <print+0x160>
 b2e:	04c00613          	li	a2,76
 b32:	0ac70763          	beq	a4,a2,be0 <print+0x1f0>
 b36:	06800613          	li	a2,104
 b3a:	08c70c63          	beq	a4,a2,bd2 <print+0x1e2>
 b3e:	4581                	li	a1,0
 b40:	a82d                	j	b7a <print+0x18a>
 b42:	00269713          	slli	a4,a3,0x2
 b46:	96ba                	add	a3,a3,a4
 b48:	0686                	slli	a3,a3,0x1
 b4a:	96b2                	add	a3,a3,a2
 b4c:	0285                	addi	t0,t0,1
 b4e:	bf7d                	j	b0c <print+0x11c>
 b50:	07400613          	li	a2,116
 b54:	08c70663          	beq	a4,a2,be0 <print+0x1f0>
 b58:	07a00613          	li	a2,122
 b5c:	08c70263          	beq	a4,a2,be0 <print+0x1f0>
 b60:	06c00613          	li	a2,108
 b64:	4581                	li	a1,0
 b66:	00c71a63          	bne	a4,a2,b7a <print+0x18a>
 b6a:	00128603          	lb	a2,1(t0)
 b6e:	458d                	li	a1,3
 b70:	00e61463          	bne	a2,a4,b78 <print+0x188>
 b74:	0285                	addi	t0,t0,1
 b76:	4591                	li	a1,4
 b78:	0285                	addi	t0,t0,1
 b7a:	00028603          	lb	a2,0(t0)
 b7e:	06000393          	li	t2,96
 b82:	06100713          	li	a4,97
 b86:	00c3c463          	blt	t2,a2,b8e <print+0x19e>
 b8a:	04100713          	li	a4,65
 b8e:	06700393          	li	t2,103
 b92:	06c3c463          	blt	t2,a2,bfa <print+0x20a>
 b96:	06500393          	li	t2,101
 b9a:	18765363          	bge	a2,t2,d20 <print+0x330>
 b9e:	04700393          	li	t2,71
 ba2:	04c3c163          	blt	t2,a2,be4 <print+0x1f4>
 ba6:	04500593          	li	a1,69
 baa:	16b65b63          	bge	a2,a1,d20 <print+0x330>
 bae:	04300713          	li	a4,67
 bb2:	eae610e3          	bne	a2,a4,a52 <print+0x62>
 bb6:	4018                	lw	a4,0(s0)
 bb8:	00440393          	addi	t2,s0,4
 bbc:	ca16                	sw	t0,20(sp)
 bbe:	00e10c23          	sb	a4,24(sp)
 bc2:	c81e                	sw	t2,16(sp)
 bc4:	00010ca3          	sb	zero,25(sp)
 bc8:	4701                	li	a4,0
 bca:	86be                	mv	a3,a5
 bcc:	862a                	mv	a2,a0
 bce:	082c                	addi	a1,sp,24
 bd0:	a849                	j	c62 <print+0x272>
 bd2:	00128603          	lb	a2,1(t0)
 bd6:	4581                	li	a1,0
 bd8:	fae611e3          	bne	a2,a4,b7a <print+0x18a>
 bdc:	0289                	addi	t0,t0,2
 bde:	bf71                	j	b7a <print+0x18a>
 be0:	0285                	addi	t0,t0,1
 be2:	bfb1                	j	b3e <print+0x14e>
 be4:	06300693          	li	a3,99
 be8:	fcd607e3          	beq	a2,a3,bb6 <print+0x1c6>
 bec:	06c6ce63          	blt	a3,a2,c68 <print+0x278>
 bf0:	05800693          	li	a3,88
 bf4:	02d60363          	beq	a2,a3,c1a <print+0x22a>
 bf8:	bda9                	j	a52 <print+0x62>
 bfa:	07300693          	li	a3,115
 bfe:	04d60463          	beq	a2,a3,c46 <print+0x256>
 c02:	02c6cb63          	blt	a3,a2,c38 <print+0x248>
 c06:	06f00693          	li	a3,111
 c0a:	0ed60263          	beq	a2,a3,cee <print+0x2fe>
 c0e:	07000693          	li	a3,112
 c12:	0047e793          	ori	a5,a5,4
 c16:	e2d61ee3          	bne	a2,a3,a52 <print+0x62>
 c1a:	4691                	li	a3,4
 c1c:	0ad59f63          	bne	a1,a3,cda <print+0x2ea>
 c20:	00840393          	addi	t2,s0,8
 c24:	400c                	lw	a1,0(s0)
 c26:	4050                	lw	a2,4(s0)
 c28:	ca16                	sw	t0,20(sp)
 c2a:	c23a                	sw	a4,4(sp)
 c2c:	c03e                	sw	a5,0(sp)
 c2e:	c81e                	sw	t2,16(sp)
 c30:	87aa                	mv	a5,a0
 c32:	4701                	li	a4,0
 c34:	46c1                	li	a3,16
 c36:	a0b9                	j	c84 <print+0x294>
 c38:	07500693          	li	a3,117
 c3c:	06d60863          	beq	a2,a3,cac <print+0x2bc>
 c40:	07800693          	li	a3,120
 c44:	bf45                	j	bf4 <print+0x204>
 c46:	4018                	lw	a4,0(s0)
 c48:	000016b7          	lui	a3,0x1
 c4c:	00440393          	addi	t2,s0,4
 c50:	d8468593          	addi	a1,a3,-636 # d84 <printf+0x42>
 c54:	c311                	beqz	a4,c58 <print+0x268>
 c56:	85ba                	mv	a1,a4
 c58:	4732                	lw	a4,12(sp)
 c5a:	ca16                	sw	t0,20(sp)
 c5c:	c81e                	sw	t2,16(sp)
 c5e:	86be                	mv	a3,a5
 c60:	862a                	mv	a2,a0
 c62:	4522                	lw	a0,8(sp)
 c64:	3e41                	jal	7f4 <prints>
 c66:	a00d                	j	c88 <print+0x298>
 c68:	4691                	li	a3,4
 c6a:	02d59463          	bne	a1,a3,c92 <print+0x2a2>
 c6e:	00840393          	addi	t2,s0,8
 c72:	400c                	lw	a1,0(s0)
 c74:	4050                	lw	a2,4(s0)
 c76:	ca16                	sw	t0,20(sp)
 c78:	c23a                	sw	a4,4(sp)
 c7a:	c03e                	sw	a5,0(sp)
 c7c:	c81e                	sw	t2,16(sp)
 c7e:	87aa                	mv	a5,a0
 c80:	4705                	li	a4,1
 c82:	46a9                	li	a3,10
 c84:	4522                	lw	a0,8(sp)
 c86:	338d                	jal	9e8 <printLongLongInt>
 c88:	43c2                	lw	t2,16(sp)
 c8a:	94aa                	add	s1,s1,a0
 c8c:	841e                	mv	s0,t2
 c8e:	42d2                	lw	t0,20(sp)
 c90:	b3c9                	j	a52 <print+0x62>
 c92:	46b2                	lw	a3,12(sp)
 c94:	400c                	lw	a1,0(s0)
 c96:	c816                	sw	t0,16(sp)
 c98:	c236                	sw	a3,4(sp)
 c9a:	c03a                	sw	a4,0(sp)
 c9c:	0411                	addi	s0,s0,4
 c9e:	872a                	mv	a4,a0
 ca0:	4685                	li	a3,1
 ca2:	4629                	li	a2,10
 ca4:	4522                	lw	a0,8(sp)
 ca6:	3915                	jal	8da <printInt>
 ca8:	94aa                	add	s1,s1,a0
 caa:	b361                	j	a32 <print+0x42>
 cac:	4691                	li	a3,4
 cae:	00d59d63          	bne	a1,a3,cc8 <print+0x2d8>
 cb2:	00840393          	addi	t2,s0,8
 cb6:	400c                	lw	a1,0(s0)
 cb8:	4050                	lw	a2,4(s0)
 cba:	ca16                	sw	t0,20(sp)
 cbc:	c23a                	sw	a4,4(sp)
 cbe:	c03e                	sw	a5,0(sp)
 cc0:	c81e                	sw	t2,16(sp)
 cc2:	87aa                	mv	a5,a0
 cc4:	4701                	li	a4,0
 cc6:	bf75                	j	c82 <print+0x292>
 cc8:	46b2                	lw	a3,12(sp)
 cca:	400c                	lw	a1,0(s0)
 ccc:	c816                	sw	t0,16(sp)
 cce:	c236                	sw	a3,4(sp)
 cd0:	c03a                	sw	a4,0(sp)
 cd2:	0411                	addi	s0,s0,4
 cd4:	872a                	mv	a4,a0
 cd6:	4681                	li	a3,0
 cd8:	b7e9                	j	ca2 <print+0x2b2>
 cda:	46b2                	lw	a3,12(sp)
 cdc:	c816                	sw	t0,16(sp)
 cde:	400c                	lw	a1,0(s0)
 ce0:	4641                	li	a2,16
 ce2:	c236                	sw	a3,4(sp)
 ce4:	c03a                	sw	a4,0(sp)
 ce6:	0411                	addi	s0,s0,4
 ce8:	872a                	mv	a4,a0
 cea:	4681                	li	a3,0
 cec:	bf65                	j	ca4 <print+0x2b4>
 cee:	4691                	li	a3,4
 cf0:	00d59e63          	bne	a1,a3,d0c <print+0x31c>
 cf4:	00840393          	addi	t2,s0,8
 cf8:	400c                	lw	a1,0(s0)
 cfa:	4050                	lw	a2,4(s0)
 cfc:	ca16                	sw	t0,20(sp)
 cfe:	c23a                	sw	a4,4(sp)
 d00:	c03e                	sw	a5,0(sp)
 d02:	c81e                	sw	t2,16(sp)
 d04:	87aa                	mv	a5,a0
 d06:	4701                	li	a4,0
 d08:	46a1                	li	a3,8
 d0a:	bfad                	j	c84 <print+0x294>
 d0c:	46b2                	lw	a3,12(sp)
 d0e:	400c                	lw	a1,0(s0)
 d10:	c816                	sw	t0,16(sp)
 d12:	c236                	sw	a3,4(sp)
 d14:	c03a                	sw	a4,0(sp)
 d16:	0411                	addi	s0,s0,4
 d18:	872a                	mv	a4,a0
 d1a:	4681                	li	a3,0
 d1c:	4621                	li	a2,8
 d1e:	b759                	j	ca4 <print+0x2b4>
 d20:	400c                	lw	a1,0(s0)
 d22:	00840613          	addi	a2,s0,8
 d26:	4040                	lw	s0,4(s0)
 d28:	c23a                	sw	a4,4(sp)
 d2a:	872a                	mv	a4,a0
 d2c:	4522                	lw	a0,8(sp)
 d2e:	c832                	sw	a2,16(sp)
 d30:	c03e                	sw	a5,0(sp)
 d32:	8622                	mv	a2,s0
 d34:	87b6                	mv	a5,a3
 d36:	46a9                	li	a3,10
 d38:	ca16                	sw	t0,20(sp)
 d3a:	394d                	jal	9ec <printDouble>
 d3c:	94aa                	add	s1,s1,a0
 d3e:	4442                	lw	s0,16(sp)
 d40:	b7b9                	j	c8e <print+0x29e>

00000d42 <printf>:
 d42:	fdc10113          	addi	sp,sp,-36
 d46:	c82e                	sw	a1,16(sp)
 d48:	ca32                	sw	a2,20(sp)
 d4a:	85aa                	mv	a1,a0
 d4c:	0810                	addi	a2,sp,16
 d4e:	4501                	li	a0,0
 d50:	c606                	sw	ra,12(sp)
 d52:	cc36                	sw	a3,24(sp)
 d54:	ce3a                	sw	a4,28(sp)
 d56:	d03e                	sw	a5,32(sp)
 d58:	c032                	sw	a2,0(sp)
 d5a:	3959                	jal	9f0 <print>
 d5c:	40b2                	lw	ra,12(sp)
 d5e:	02410113          	addi	sp,sp,36
 d62:	8082                	ret
 d64:	74737953          	0x74737953
 d68:	6d65                	lui	s10,0x19
 d6a:	3a6b6c43          	fmadd.d	fs8,fs6,ft6,ft7,unknown
 d6e:	6425                	lui	s0,0x9
 d70:	0a0d                	addi	s4,s4,3
 d72:	0000                	unimp
 d74:	70696843          	fmadd.s	fa6,fs2,ft6,fa4,unknown
 d78:	4449                	li	s0,18
 d7a:	253a                	lhu	a4,10(a0)
 d7c:	3830                	lbu	a2,19(s0)
 d7e:	0d78                	addi	a4,sp,668
 d80:	000a                	c.slli	zero,0x2
 d82:	0000                	unimp
 d84:	6e28                	flw	fa0,88(a2)
 d86:	6c75                	lui	s8,0x1d
 d88:	296c                	lbu	a1,22(a0)
	...
