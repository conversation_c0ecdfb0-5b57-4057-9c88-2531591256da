
CH32V003F4U6.elf:     file format elf32-littleriscv
CH32V003F4U6.elf
architecture: riscv:rv32, flags 0x00000112:
EXEC_P, HAS_SYMS, D_PAGED
start address 0x00000000

Program Header:
    LOAD off    0x00001000 vaddr 0x00000000 paddr 0x00000000 align 2**12
         filesz 0x000020d4 memsz 0x000020d4 flags r-x
    LOAD off    0x00004000 vaddr 0x20000000 paddr 0x000020d4 align 2**12
         filesz 0x00000040 memsz 0x000001d4 flags rw-
    LOAD off    0x00004700 vaddr 0x20000700 paddr 0x20000700 align 2**12
         filesz 0x00000000 memsz 0x00000100 flags rw-

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .init         000000a0  00000000  00000000  00001000  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  1 .highcodelalign 00000000  000000a0  000000a0  00004040  2**0
                  CONTENTS
  2 .highcode     00000000  20000000  20000000  00004040  2**0
                  CONTENTS
  3 .text         00002034  000000a0  000000a0  000010a0  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  4 .fini         00000000  000020d4  000020d4  00004040  2**0
                  CONTENTS, ALLOC, LOAD, CODE
  5 .dalign       00000000  20000000  20000000  00004040  2**0
                  CONTENTS
  6 .dlalign      00000000  000020d4  000020d4  00004040  2**0
                  CONTENTS
  7 .data         00000040  20000000  000020d4  00004000  2**2
                  CONTENTS, ALLOC, LOAD, DATA
  8 .bss          00000194  20000040  00002114  00004040  2**2
                  ALLOC
  9 .stack        00000100  20000700  20000700  00004700  2**0
                  ALLOC
 10 .debug_info   00011cbb  00000000  00000000  00004040  2**0
                  CONTENTS, READONLY, DEBUGGING
 11 .debug_abbrev 000032a6  00000000  00000000  00015cfb  2**0
                  CONTENTS, READONLY, DEBUGGING
 12 .debug_loc    000045d5  00000000  00000000  00018fa1  2**0
                  CONTENTS, READONLY, DEBUGGING
 13 .debug_aranges 00000980  00000000  00000000  0001d578  2**3
                  CONTENTS, READONLY, DEBUGGING
 14 .debug_ranges 00000ad8  00000000  00000000  0001def8  2**3
                  CONTENTS, READONLY, DEBUGGING
 15 .debug_line   0000b967  00000000  00000000  0001e9d0  2**0
                  CONTENTS, READONLY, DEBUGGING
 16 .debug_str    00002ec7  00000000  00000000  0002a337  2**0
                  CONTENTS, READONLY, DEBUGGING
 17 .comment      00000033  00000000  00000000  0002d1fe  2**0
                  CONTENTS, READONLY
 18 .debug_frame  000015a4  00000000  00000000  0002d234  2**2
                  CONTENTS, READONLY, DEBUGGING
SYMBOL TABLE:
00000000 l    d  .init	00000000 .init
000000a0 l    d  .highcodelalign	00000000 .highcodelalign
20000000 l    d  .highcode	00000000 .highcode
000000a0 l    d  .text	00000000 .text
000020d4 l    d  .fini	00000000 .fini
20000000 l    d  .dalign	00000000 .dalign
000020d4 l    d  .dlalign	00000000 .dlalign
20000000 l    d  .data	00000000 .data
20000040 l    d  .bss	00000000 .bss
20000700 l    d  .stack	00000000 .stack
00000000 l    d  .debug_info	00000000 .debug_info
00000000 l    d  .debug_abbrev	00000000 .debug_abbrev
00000000 l    d  .debug_loc	00000000 .debug_loc
00000000 l    d  .debug_aranges	00000000 .debug_aranges
00000000 l    d  .debug_ranges	00000000 .debug_ranges
00000000 l    d  .debug_line	00000000 .debug_line
00000000 l    d  .debug_str	00000000 .debug_str
00000000 l    d  .comment	00000000 .comment
00000000 l    d  .debug_frame	00000000 .debug_frame
00000000 l    df *ABS*	00000000 adc_display.c
00001a14 l     O .text	00000008 CSWTCH.2
00000000 l    df *ABS*	00000000 ch32v00x_it.c
00000000 l    df *ABS*	00000000 display_control.c
00000000 l    df *ABS*	00000000 main.c
00000000 l    df *ABS*	00000000 pwm_config.c
00000000 l    df *ABS*	00000000 st7735.c
0000051a l     F .text	0000003e SPI_send_DMA
00000558 l     F .text	00000012 SPI_send
0000056a l     F .text	00000014 write_command_8
0000057e l     F .text	0000001e write_data_16
0000059c l     F .text	0000003a tft_set_window
20000062 l     O .bss	00000002 _bg_color
20000064 l     O .bss	00000140 _buffer
200001a4 l     O .bss	00000002 _cursor_x
200001a6 l     O .bss	00000002 _cursor_y
200001a8 l     O .bss	0000000c str.4169
20000000 l     O .data	00000002 _color
00001bcc l     O .text	00000500 font
00000000 l    df *ABS*	00000000 system_ch32v00x.c
00000000 l    df *ABS*	00000000 touch_button.c
00000000 l    df *ABS*	00000000 ch32v00x_dbgmcu.c
00000000 l    df *ABS*	00000000 ch32v00x_exti.c
00000000 l    df *ABS*	00000000 ch32v00x_gpio.c
00000000 l    df *ABS*	00000000 ch32v00x_misc.c
00000000 l    df *ABS*	00000000 ch32v00x_rcc.c
20000018 l     O .data	00000014 ADCPrescTable
2000002c l     O .data	00000010 APBAHBPrescTable
00000000 l    df *ABS*	00000000 ch32v00x_tim.c
00000000 l    df *ABS*	00000000 ch32v00x_usart.c
00000000 l    df *ABS*	00000000 debug.c
200001d0 l     O .bss	00000002 p_ms
200001d2 l     O .bss	00000001 p_us
00000000 l    df *ABS*	00000000 wchprintf.c
00000000 l    df *ABS*	00000000 memcpy.c
00001544  w    F .text	00000004 printDouble
000001ce g     F .text	00000026 ADC_Display_Init
20000040 g     O .bss	0000000c adc_display_config
00001548  w    F .text	00000358 print
000018a0  w    F .text	00000024 printf
20000840 g       .data	00000000 __global_pointer$
000002f6 g     F .text	00000024 Display_Control_Init
00000d12  w      .text	00000000 TIM1_CC_IRQHandler
000001f6 g     F .text	00000010 HardFault_Handler
00001432  w    F .text	0000010e printInt
00000c24 g     F .text	0000002a Touch_Button_Init
00000d12  w      .text	00000000 SysTick_Handler
00000f0e g     F .text	00000062 NVIC_Init
00000d12  w      .text	00000000 PVD_IRQHandler
000001f4 g     F .text	00000002 NMI_Handler
00000dc4 g     F .text	0000000a DBGMCU_GetCHIPID
200001b4 g     O .bss	00000004 system_tick_ms
00000766 g     F .text	0000000e tft_set_cursor
000011ea g     F .text	0000000a USART_GetFlagStatus
20000040 g       .bss	00000000 _sbss
00000100 g       *ABS*	00000000 __stack_size
00001262 g     F .text	00000056 USART_Printf_Init
000000aa g     F .text	0000000a .hidden __riscv_restore_2
0000190e g     F .text	000000d2 memcpy
00001540  w    F .text	00000004 printLongLongInt
00000172 g     F .text	0000005c ADC_Display_Draw_Channel_Labels
000010ae g     F .text	00000018 TIM_Cmd
000018c4 g     F .text	0000004a puts
0000014a g     F .text	00000028 ADC_Display_Draw_Header
20000014 g     O .data	00000004 SystemCoreClock
2000004c g     O .bss	0000000c display_control
00000232 g     F .text	00000016 Display_Control_Turn_On
000000d4 g     F .text	0000002c .hidden __udivsi3
000000a0 g       .init	00000000 _einit
000010f4 g     F .text	0000000c TIM_ClearITPendingBit
00001024 g     F .text	0000001e RCC_APB2PeriphClockCmd
00000e60 g     F .text	0000007c GPIO_Init
200001cc g     O .bss	00000004 NVIC_Priority_Group
00000d12  w      .text	00000000 SPI1_IRQHandler
000011cc g     F .text	00000016 USART_Cmd
000000a0 g     F .text	0000000a .hidden __riscv_save_1
00000868 g     F .text	00000020 tft_print
000000aa g     F .text	0000000a .hidden __riscv_restore_0
00000d12  w      .text	00000000 AWU_IRQHandler
00000206 g     F .text	00000008 EXTI7_0_IRQHandler
00001042 g     F .text	0000001e RCC_APB1PeriphClockCmd
20000700 g       .stack	00000000 _heap_end
0000031a g     F .text	00000026 Display_Control_Show_Off_Message
00000340 g     F .text	0000003e Display_Control_Update
00000d12  w      .text	00000000 DMA1_Channel4_IRQHandler
00000d12  w      .text	00000000 ADC1_IRQHandler
00000c4e g     F .text	00000072 Touch_Button_Update
00000e56 g     F .text	0000000a EXTI_ClearITPendingBit
200001d4 g       .bss	00000000 _ebss
00000d12  w      .text	00000000 DMA1_Channel7_IRQHandler
000011f4 g     F .text	00000034 Delay_Init
00000be2 g     F .text	00000042 Touch_Button_EXTI_Config
0000077e g     F .text	00000006 tft_set_background_color
00000100 g     F .text	00000008 .hidden __umodsi3
00000d12  w      .text	00000000 I2C1_EV_IRQHandler
000010dc g     F .text	00000018 TIM_GetITStatus
00000f82 g     F .text	000000a2 RCC_GetClocksFreq
00000b5c g     F .text	00000030 Touch_Button_GPIO_Config
00000d12  w      .text	00000000 DMA1_Channel6_IRQHandler
00001100 g     F .text	000000cc USART_Init
00000d12  w      .text	00000000 RCC_IRQHandler
00000d12  w      .text	00000000 TIM1_TRG_COM_IRQHandler
00000d12  w      .text	00000000 DMA1_Channel1_IRQHandler
00000000 g       .init	00000000 _start
20000004 g     O .data	00000010 AHBPrescTable
00000506 g     F .text	0000000e PWM_Turn_Off
0000027a g     F .text	00000018 Display_Control_Clear_Screen
0000134c  w    F .text	000000e6 prints
000010c6 g     F .text	00000012 TIM_ITConfig
20000000 g       .highcode	00000000 _highcode_vma_start
00000dce g     F .text	0000006a EXTI_Init
000000b4 g     F .text	00000014 .hidden __mulsi3
00000262 g     F .text	00000018 Display_Control_Toggle
20000000 g       .dalign	00000000 _data_vma
00000cc0 g     F .text	0000000c Touch_Button_Get_Event
00000ee6 g     F .text	00000022 GPIO_EXTILineConfig
00000248 g     F .text	0000001a Display_Control_Turn_Off
00000e38 g     F .text	0000001e EXTI_GetITStatus
00000f70 g     F .text	00000012 RCC_AdjustHSICalibrationValue
000003da g     F .text	000000a2 main
0000037e g     F .text	0000005c System_Init
00000d12  w      .text	00000000 DMA1_Channel5_IRQHandler
000000cc g     F .text	00000058 .hidden __divsi3
00001228 g     F .text	0000003a Delay_Ms
00000888 g     F .text	000000aa tft_print_number
000005d6 g     F .text	00000190 tft_init
000000a0 g       .highcodelalign	00000000 _highcode_lma
000009bc g     F .text	00000134 SystemInit
00000514 g     F .text	00000006 PWM_Get_Brightness
00001304  w    F .text	00000048 printchar
00000000 g       .init	00000000 _sinit
00000932 g     F .text	0000008a tft_fill_rect
00000d12  w      .text	00000000 DMA1_Channel3_IRQHandler
000004f6 g     F .text	00000010 PWM_Turn_On
00000d12  w      .text	00000000 TIM1_UP_IRQHandler
20000058 g     O .bss	00000001 system_initialized
00000d12  w      .text	00000000 WWDG_IRQHandler
0000020e g     F .text	00000024 TIM2_IRQHandler
20000800 g       .stack	00000000 _eusrstack
000000a0 g     F .text	0000000a .hidden __riscv_save_2
00000d12  w      .text	00000000 SW_Handler
2000005c g     O .bss	00000006 pwm_control
00000d12  w      .text	00000000 TIM1_BRK_IRQHandler
000011e2 g     F .text	00000008 USART_SendData
000012b8 g     F .text	0000004c _write
20000040 g       .data	00000000 _edata
200001d4 g       .bss	00000000 _end
20000000 g       .highcode	00000000 _highcode_vma_end
00001060 g     F .text	0000004e TIM_TimeBaseInit
000020d4 g       .dlalign	00000000 _data_lma
00000af0 g     F .text	0000006c SystemCoreClockUpdate
00000ccc g     F .text	00000046 Touch_Button_IRQ_Handler
00000124 g     F .text	00000024 .hidden __modsi3
00000d12  w      .text	00000000 DMA1_Channel2_IRQHandler
00000d14  w      .text	00000000 handle_reset
00000d12  w      .text	00000000 FLASH_IRQHandler
000000a0 g     F .text	0000000a .hidden __riscv_save_0
00000d12  w      .text	00000000 USART1_IRQHandler
00000b8c g     F .text	00000056 Touch_Button_Timer_Init
00000774 g     F .text	0000000a tft_set_color
0000047c g     F .text	0000002a PWM_Set_Brightness
00000d12  w      .text	00000000 I2C1_ER_IRQHandler
00000f08 g     F .text	00000006 NVIC_PriorityGroupConfig
00000784 g     F .text	000000e4 tft_print_char
000004a6 g     F .text	00000050 PWM_Update_Fade
00000292 g     F .text	00000064 Display_Control_Show_Startup_Message
000000aa g     F .text	0000000a .hidden __riscv_restore_1
000010d8 g     F .text	00000004 TIM_SetCompare1
00000edc g     F .text	0000000a GPIO_ReadInputDataBit
200001b8 g     O .bss	00000014 touch_button



Disassembly of section .init:

00000000 <_sinit>:
   0:	5150006f          	j	d14 <handle_reset>
   4:	0000                	unimp
   6:	0000                	unimp
   8:	01f4                	addi	a3,sp,204
   a:	0000                	unimp
   c:	01f6                	slli	gp,gp,0x1d
	...
  2e:	0000                	unimp
  30:	0d12                	slli	s10,s10,0x4
  32:	0000                	unimp
  34:	0000                	unimp
  36:	0000                	unimp
  38:	0d12                	slli	s10,s10,0x4
  3a:	0000                	unimp
  3c:	0000                	unimp
  3e:	0000                	unimp
  40:	0d12                	slli	s10,s10,0x4
  42:	0000                	unimp
  44:	0d12                	slli	s10,s10,0x4
  46:	0000                	unimp
  48:	0d12                	slli	s10,s10,0x4
  4a:	0000                	unimp
  4c:	0d12                	slli	s10,s10,0x4
  4e:	0000                	unimp
  50:	0206                	slli	tp,tp,0x1
  52:	0000                	unimp
  54:	0d12                	slli	s10,s10,0x4
  56:	0000                	unimp
  58:	0d12                	slli	s10,s10,0x4
  5a:	0000                	unimp
  5c:	0d12                	slli	s10,s10,0x4
  5e:	0000                	unimp
  60:	0d12                	slli	s10,s10,0x4
  62:	0000                	unimp
  64:	0d12                	slli	s10,s10,0x4
  66:	0000                	unimp
  68:	0d12                	slli	s10,s10,0x4
  6a:	0000                	unimp
  6c:	0d12                	slli	s10,s10,0x4
  6e:	0000                	unimp
  70:	0d12                	slli	s10,s10,0x4
  72:	0000                	unimp
  74:	0d12                	slli	s10,s10,0x4
  76:	0000                	unimp
  78:	0d12                	slli	s10,s10,0x4
  7a:	0000                	unimp
  7c:	0d12                	slli	s10,s10,0x4
  7e:	0000                	unimp
  80:	0d12                	slli	s10,s10,0x4
  82:	0000                	unimp
  84:	0d12                	slli	s10,s10,0x4
  86:	0000                	unimp
  88:	0d12                	slli	s10,s10,0x4
  8a:	0000                	unimp
  8c:	0d12                	slli	s10,s10,0x4
  8e:	0000                	unimp
  90:	0d12                	slli	s10,s10,0x4
  92:	0000                	unimp
  94:	0d12                	slli	s10,s10,0x4
  96:	0000                	unimp
  98:	020e                	slli	tp,tp,0x3
  9a:	0000                	unimp
  9c:	0000                	unimp
	...

Disassembly of section .text:

000000a0 <__riscv_save_0>:
      a0:	1151                	addi	sp,sp,-12
      a2:	c026                	sw	s1,0(sp)
      a4:	c222                	sw	s0,4(sp)
      a6:	c406                	sw	ra,8(sp)
      a8:	8282                	jr	t0

000000aa <__riscv_restore_0>:
      aa:	4482                	lw	s1,0(sp)
      ac:	4412                	lw	s0,4(sp)
      ae:	40a2                	lw	ra,8(sp)
      b0:	0131                	addi	sp,sp,12
      b2:	8082                	ret

000000b4 <__mulsi3>:
      b4:	862a                	mv	a2,a0
      b6:	4501                	li	a0,0
      b8:	0015f693          	andi	a3,a1,1
      bc:	c291                	beqz	a3,c0 <__mulsi3+0xc>
      be:	9532                	add	a0,a0,a2
      c0:	8185                	srli	a1,a1,0x1
      c2:	0606                	slli	a2,a2,0x1
      c4:	f9f5                	bnez	a1,b8 <__mulsi3+0x4>
      c6:	8082                	ret
      c8:	0000                	unimp
	...

000000cc <__divsi3>:
      cc:	02054e63          	bltz	a0,108 <__umodsi3+0x8>
      d0:	0405c363          	bltz	a1,116 <__umodsi3+0x16>

000000d4 <__udivsi3>:
      d4:	862e                	mv	a2,a1
      d6:	85aa                	mv	a1,a0
      d8:	557d                	li	a0,-1
      da:	c215                	beqz	a2,fe <__udivsi3+0x2a>
      dc:	4685                	li	a3,1
      de:	00b67863          	bgeu	a2,a1,ee <__udivsi3+0x1a>
      e2:	00c05663          	blez	a2,ee <__udivsi3+0x1a>
      e6:	0606                	slli	a2,a2,0x1
      e8:	0686                	slli	a3,a3,0x1
      ea:	feb66ce3          	bltu	a2,a1,e2 <__udivsi3+0xe>
      ee:	4501                	li	a0,0
      f0:	00c5e463          	bltu	a1,a2,f8 <__udivsi3+0x24>
      f4:	8d91                	sub	a1,a1,a2
      f6:	8d55                	or	a0,a0,a3
      f8:	8285                	srli	a3,a3,0x1
      fa:	8205                	srli	a2,a2,0x1
      fc:	faf5                	bnez	a3,f0 <__udivsi3+0x1c>
      fe:	8082                	ret

00000100 <__umodsi3>:
     100:	8286                	mv	t0,ra
     102:	3fc9                	jal	d4 <__udivsi3>
     104:	852e                	mv	a0,a1
     106:	8282                	jr	t0
     108:	40a00533          	neg	a0,a0
     10c:	0005d763          	bgez	a1,11a <__umodsi3+0x1a>
     110:	40b005b3          	neg	a1,a1
     114:	b7c1                	j	d4 <__udivsi3>
     116:	40b005b3          	neg	a1,a1
     11a:	8286                	mv	t0,ra
     11c:	3f65                	jal	d4 <__udivsi3>
     11e:	40a00533          	neg	a0,a0
     122:	8282                	jr	t0

00000124 <__modsi3>:
     124:	8286                	mv	t0,ra
     126:	0005c763          	bltz	a1,134 <__modsi3+0x10>
     12a:	00054963          	bltz	a0,13c <__modsi3+0x18>
     12e:	375d                	jal	d4 <__udivsi3>
     130:	852e                	mv	a0,a1
     132:	8282                	jr	t0
     134:	40b005b3          	neg	a1,a1
     138:	fe055be3          	bgez	a0,12e <__modsi3+0xa>
     13c:	40a00533          	neg	a0,a0
     140:	3f51                	jal	d4 <__udivsi3>
     142:	40b00533          	neg	a0,a1
     146:	8282                	jr	t0
	...

0000014a <ADC_Display_Draw_Header>:
     14a:	f57ff2ef          	jal	t0,a0 <__riscv_save_0>
     14e:	6541                	lui	a0,0x10
     150:	157d                	addi	a0,a0,-1
     152:	622000ef          	jal	ra,774 <tft_set_color>
     156:	4501                	li	a0,0
     158:	626000ef          	jal	ra,77e <tft_set_background_color>
     15c:	4581                	li	a1,0
     15e:	4515                	li	a0,5
     160:	606000ef          	jal	ra,766 <tft_set_cursor>
     164:	00002537          	lui	a0,0x2
     168:	a0850513          	addi	a0,a0,-1528 # 1a08 <memcpy+0xfa>
     16c:	6fc000ef          	jal	ra,868 <tft_print>
     170:	bf2d                	j	aa <__riscv_restore_0>

00000172 <ADC_Display_Draw_Channel_Labels>:
     172:	f2fff2ef          	jal	t0,a0 <__riscv_save_0>
     176:	1171                	addi	sp,sp,-4
     178:	4501                	li	a0,0
     17a:	604000ef          	jal	ra,77e <tft_set_background_color>
     17e:	6789                	lui	a5,0x2
     180:	a1478793          	addi	a5,a5,-1516 # 1a14 <CSWTCH.2>
     184:	4451                	li	s0,20
     186:	4485                	li	s1,1
     188:	238a                	lhu	a0,0(a5)
     18a:	c03e                	sw	a5,0(sp)
     18c:	5e8000ef          	jal	ra,774 <tft_set_color>
     190:	85a2                	mv	a1,s0
     192:	4515                	li	a0,5
     194:	5d2000ef          	jal	ra,766 <tft_set_cursor>
     198:	000027b7          	lui	a5,0x2
     19c:	a0078513          	addi	a0,a5,-1536 # 1a00 <memcpy+0xf2>
     1a0:	6c8000ef          	jal	ra,868 <tft_print>
     1a4:	8526                	mv	a0,s1
     1a6:	4585                	li	a1,1
     1a8:	6e0000ef          	jal	ra,888 <tft_print_number>
     1ac:	00002537          	lui	a0,0x2
     1b0:	a0450513          	addi	a0,a0,-1532 # 1a04 <memcpy+0xf6>
     1b4:	6b4000ef          	jal	ra,868 <tft_print>
     1b8:	4782                	lw	a5,0(sp)
     1ba:	0449                	addi	s0,s0,18
     1bc:	0442                	slli	s0,s0,0x10
     1be:	0485                	addi	s1,s1,1
     1c0:	4695                	li	a3,5
     1c2:	0789                	addi	a5,a5,2
     1c4:	8041                	srli	s0,s0,0x10
     1c6:	fcd491e3          	bne	s1,a3,188 <ADC_Display_Draw_Channel_Labels+0x16>
     1ca:	0111                	addi	sp,sp,4
     1cc:	bdf9                	j	aa <__riscv_restore_0>

000001ce <ADC_Display_Init>:
     1ce:	ed3ff2ef          	jal	t0,a0 <__riscv_save_0>
     1d2:	200007b7          	lui	a5,0x20000
     1d6:	02010737          	lui	a4,0x2010
     1da:	04078793          	addi	a5,a5,64 # 20000040 <_edata>
     1de:	10070713          	addi	a4,a4,256 # 2010100 <_data_lma+0x200e02c>
     1e2:	c398                	sw	a4,0(a5)
     1e4:	06400713          	li	a4,100
     1e8:	a3da                	sh	a4,4(a5)
     1ea:	0007a423          	sw	zero,8(a5)
     1ee:	3fb1                	jal	14a <ADC_Display_Draw_Header>
     1f0:	3749                	jal	172 <ADC_Display_Draw_Channel_Labels>
     1f2:	bd65                	j	aa <__riscv_restore_0>

000001f4 <NMI_Handler>:
     1f4:	a001                	j	1f4 <NMI_Handler>

000001f6 <HardFault_Handler>:
     1f6:	beef07b7          	lui	a5,0xbeef0
     1fa:	e000e737          	lui	a4,0xe000e
     1fe:	08078793          	addi	a5,a5,128 # beef0080 <__global_pointer$+0x9eeef840>
     202:	c73c                	sw	a5,72(a4)
     204:	a001                	j	204 <HardFault_Handler+0xe>

00000206 <EXTI7_0_IRQHandler>:
     206:	2c7000ef          	jal	ra,ccc <Touch_Button_IRQ_Handler>
     20a:	30200073          	mret

0000020e <TIM2_IRQHandler>:
     20e:	4585                	li	a1,1
     210:	40000537          	lui	a0,0x40000
     214:	6c9000ef          	jal	ra,10dc <TIM_GetITStatus>
     218:	c919                	beqz	a0,22e <TIM2_IRQHandler+0x20>
     21a:	9741a783          	lw	a5,-1676(gp) # 200001b4 <system_tick_ms>
     21e:	4585                	li	a1,1
     220:	40000537          	lui	a0,0x40000
     224:	0785                	addi	a5,a5,1
     226:	96f1aa23          	sw	a5,-1676(gp) # 200001b4 <system_tick_ms>
     22a:	6cb000ef          	jal	ra,10f4 <TIM_ClearITPendingBit>
     22e:	30200073          	mret

00000232 <Display_Control_Turn_On>:
     232:	80c18793          	addi	a5,gp,-2036 # 2000004c <display_control>
     236:	4398                	lw	a4,0(a5)
     238:	e719                	bnez	a4,246 <Display_Control_Turn_On+0x14>
     23a:	e67ff2ef          	jal	t0,a0 <__riscv_save_0>
     23e:	4705                	li	a4,1
     240:	c398                	sw	a4,0(a5)
     242:	2c55                	jal	4f6 <PWM_Turn_On>
     244:	b59d                	j	aa <__riscv_restore_0>
     246:	8082                	ret

00000248 <Display_Control_Turn_Off>:
     248:	80c18793          	addi	a5,gp,-2036 # 2000004c <display_control>
     24c:	4394                	lw	a3,0(a5)
     24e:	4709                	li	a4,2
     250:	00e69863          	bne	a3,a4,260 <Display_Control_Turn_Off+0x18>
     254:	e4dff2ef          	jal	t0,a0 <__riscv_save_0>
     258:	470d                	li	a4,3
     25a:	c398                	sw	a4,0(a5)
     25c:	246d                	jal	506 <PWM_Turn_Off>
     25e:	b5b1                	j	aa <__riscv_restore_0>
     260:	8082                	ret

00000262 <Display_Control_Toggle>:
     262:	e3fff2ef          	jal	t0,a0 <__riscv_save_0>
     266:	80c1a783          	lw	a5,-2036(gp) # 2000004c <display_control>
     26a:	e399                	bnez	a5,270 <Display_Control_Toggle+0xe>
     26c:	37d9                	jal	232 <Display_Control_Turn_On>
     26e:	bd35                	j	aa <__riscv_restore_0>
     270:	4709                	li	a4,2
     272:	fee79ee3          	bne	a5,a4,26e <Display_Control_Toggle+0xc>
     276:	3fc9                	jal	248 <Display_Control_Turn_Off>
     278:	bfdd                	j	26e <Display_Control_Toggle+0xc>

0000027a <Display_Control_Clear_Screen>:
     27a:	e27ff2ef          	jal	t0,a0 <__riscv_save_0>
     27e:	4701                	li	a4,0
     280:	05000693          	li	a3,80
     284:	0a000613          	li	a2,160
     288:	4581                	li	a1,0
     28a:	4501                	li	a0,0
     28c:	6a6000ef          	jal	ra,932 <tft_fill_rect>
     290:	bd29                	j	aa <__riscv_restore_0>

00000292 <Display_Control_Show_Startup_Message>:
     292:	e0fff2ef          	jal	t0,a0 <__riscv_save_0>
     296:	37d5                	jal	27a <Display_Control_Clear_Screen>
     298:	6441                	lui	s0,0x10
     29a:	fff40513          	addi	a0,s0,-1 # ffff <_data_lma+0xdf2b>
     29e:	29d9                	jal	774 <tft_set_color>
     2a0:	4501                	li	a0,0
     2a2:	29f1                	jal	77e <tft_set_background_color>
     2a4:	45a9                	li	a1,10
     2a6:	4529                	li	a0,10
     2a8:	297d                	jal	766 <tft_set_cursor>
     2aa:	00002537          	lui	a0,0x2
     2ae:	a2850513          	addi	a0,a0,-1496 # 1a28 <CSWTCH.2+0x14>
     2b2:	5b6000ef          	jal	ra,868 <tft_print>
     2b6:	45e5                	li	a1,25
     2b8:	4529                	li	a0,10
     2ba:	2175                	jal	766 <tft_set_cursor>
     2bc:	00002537          	lui	a0,0x2
     2c0:	a3850513          	addi	a0,a0,-1480 # 1a38 <CSWTCH.2+0x24>
     2c4:	5a4000ef          	jal	ra,868 <tft_print>
     2c8:	02d00593          	li	a1,45
     2cc:	4515                	li	a0,5
     2ce:	2961                	jal	766 <tft_set_cursor>
     2d0:	fe040513          	addi	a0,s0,-32
     2d4:	2145                	jal	774 <tft_set_color>
     2d6:	00002537          	lui	a0,0x2
     2da:	a4850513          	addi	a0,a0,-1464 # 1a48 <CSWTCH.2+0x34>
     2de:	58a000ef          	jal	ra,868 <tft_print>
     2e2:	03c00593          	li	a1,60
     2e6:	4515                	li	a0,5
     2e8:	29bd                	jal	766 <tft_set_cursor>
     2ea:	00002537          	lui	a0,0x2
     2ee:	a5850513          	addi	a0,a0,-1448 # 1a58 <CSWTCH.2+0x44>
     2f2:	2b9d                	jal	868 <tft_print>
     2f4:	bb5d                	j	aa <__riscv_restore_0>

000002f6 <Display_Control_Init>:
     2f6:	dabff2ef          	jal	t0,a0 <__riscv_save_0>
     2fa:	80c18413          	addi	s0,gp,-2036 # 2000004c <display_control>
     2fe:	10000793          	li	a5,256
     302:	a05e                	sh	a5,4(s0)
     304:	00042023          	sw	zero,0(s0)
     308:	00042423          	sw	zero,8(s0)
     30c:	24e9                	jal	5d6 <tft_init>
     30e:	37b5                	jal	27a <Display_Control_Clear_Screen>
     310:	3749                	jal	292 <Display_Control_Show_Startup_Message>
     312:	4785                	li	a5,1
     314:	a05c                	sb	a5,4(s0)
     316:	2ac5                	jal	506 <PWM_Turn_Off>
     318:	bb49                	j	aa <__riscv_restore_0>

0000031a <Display_Control_Show_Off_Message>:
     31a:	d87ff2ef          	jal	t0,a0 <__riscv_save_0>
     31e:	3fb1                	jal	27a <Display_Control_Clear_Screen>
     320:	6521                	lui	a0,0x8
     322:	bef50513          	addi	a0,a0,-1041 # 7bef <_data_lma+0x5b1b>
     326:	21b9                	jal	774 <tft_set_color>
     328:	4501                	li	a0,0
     32a:	2991                	jal	77e <tft_set_background_color>
     32c:	02300593          	li	a1,35
     330:	4551                	li	a0,20
     332:	2915                	jal	766 <tft_set_cursor>
     334:	00002537          	lui	a0,0x2
     338:	a1c50513          	addi	a0,a0,-1508 # 1a1c <CSWTCH.2+0x8>
     33c:	2335                	jal	868 <tft_print>
     33e:	b3b5                	j	aa <__riscv_restore_0>

00000340 <Display_Control_Update>:
     340:	d61ff2ef          	jal	t0,a0 <__riscv_save_0>
     344:	80c18413          	addi	s0,gp,-2036 # 2000004c <display_control>
     348:	205c                	lbu	a5,4(s0)
     34a:	cb89                	beqz	a5,35c <Display_Control_Update+0x1c>
     34c:	2aa9                	jal	4a6 <PWM_Update_Fade>
     34e:	401c                	lw	a5,0(s0)
     350:	4705                	li	a4,1
     352:	00e78663          	beq	a5,a4,35e <Display_Control_Update+0x1e>
     356:	470d                	li	a4,3
     358:	00e78a63          	beq	a5,a4,36c <Display_Control_Update+0x2c>
     35c:	b3b9                	j	aa <__riscv_restore_0>
     35e:	8201c703          	lbu	a4,-2016(gp) # 20000060 <pwm_control+0x4>
     362:	ff6d                	bnez	a4,35c <Display_Control_Update+0x1c>
     364:	4709                	li	a4,2
     366:	c018                	sw	a4,0(s0)
     368:	b05c                	sb	a5,5(s0)
     36a:	bfcd                	j	35c <Display_Control_Update+0x1c>
     36c:	8201c783          	lbu	a5,-2016(gp) # 20000060 <pwm_control+0x4>
     370:	f7f5                	bnez	a5,35c <Display_Control_Update+0x1c>
     372:	224d                	jal	514 <PWM_Get_Brightness>
     374:	f565                	bnez	a0,35c <Display_Control_Update+0x1c>
     376:	00042023          	sw	zero,0(s0)
     37a:	3745                	jal	31a <Display_Control_Show_Off_Message>
     37c:	b7c5                	j	35c <Display_Control_Update+0x1c>

0000037e <System_Init>:
     37e:	d23ff2ef          	jal	t0,a0 <__riscv_save_0>
     382:	00002537          	lui	a0,0x2
     386:	a6850513          	addi	a0,a0,-1432 # 1a68 <CSWTCH.2+0x54>
     38a:	53a010ef          	jal	ra,18c4 <puts>
     38e:	00002537          	lui	a0,0x2
     392:	a7c50513          	addi	a0,a0,-1412 # 1a7c <CSWTCH.2+0x68>
     396:	52e010ef          	jal	ra,18c4 <puts>
     39a:	08b000ef          	jal	ra,c24 <Touch_Button_Init>
     39e:	00002537          	lui	a0,0x2
     3a2:	a9050513          	addi	a0,a0,-1392 # 1a90 <CSWTCH.2+0x7c>
     3a6:	51e010ef          	jal	ra,18c4 <puts>
     3aa:	37b1                	jal	2f6 <Display_Control_Init>
     3ac:	00002537          	lui	a0,0x2
     3b0:	aac50513          	addi	a0,a0,-1364 # 1aac <CSWTCH.2+0x98>
     3b4:	510010ef          	jal	ra,18c4 <puts>
     3b8:	3d19                	jal	1ce <ADC_Display_Init>
     3ba:	00002537          	lui	a0,0x2
     3be:	acc50513          	addi	a0,a0,-1332 # 1acc <CSWTCH.2+0xb8>
     3c2:	502010ef          	jal	ra,18c4 <puts>
     3c6:	00002537          	lui	a0,0x2
     3ca:	4705                	li	a4,1
     3cc:	ae850513          	addi	a0,a0,-1304 # 1ae8 <CSWTCH.2+0xd4>
     3d0:	80e18c23          	sb	a4,-2024(gp) # 20000058 <system_initialized>
     3d4:	4f0010ef          	jal	ra,18c4 <puts>
     3d8:	b9c9                	j	aa <__riscv_restore_0>

000003da <main>:
     3da:	cc7ff2ef          	jal	t0,a0 <__riscv_save_0>
     3de:	4505                	li	a0,1
     3e0:	329000ef          	jal	ra,f08 <NVIC_PriorityGroupConfig>
     3e4:	70c000ef          	jal	ra,af0 <SystemCoreClockUpdate>
     3e8:	60d000ef          	jal	ra,11f4 <Delay_Init>
     3ec:	6571                	lui	a0,0x1c
     3ee:	20050513          	addi	a0,a0,512 # 1c200 <_data_lma+0x1a12c>
     3f2:	671000ef          	jal	ra,1262 <USART_Printf_Init>
     3f6:	00002537          	lui	a0,0x2
     3fa:	b0850513          	addi	a0,a0,-1272 # 1b08 <CSWTCH.2+0xf4>
     3fe:	4c6010ef          	jal	ra,18c4 <puts>
     402:	200007b7          	lui	a5,0x20000
     406:	0147a583          	lw	a1,20(a5) # 20000014 <SystemCoreClock>
     40a:	00002537          	lui	a0,0x2
     40e:	b2850513          	addi	a0,a0,-1240 # 1b28 <CSWTCH.2+0x114>
     412:	48e010ef          	jal	ra,18a0 <printf>
     416:	1af000ef          	jal	ra,dc4 <DBGMCU_GetCHIPID>
     41a:	85aa                	mv	a1,a0
     41c:	00002537          	lui	a0,0x2
     420:	b3c50513          	addi	a0,a0,-1220 # 1b3c <CSWTCH.2+0x128>
     424:	47c010ef          	jal	ra,18a0 <printf>
     428:	3f99                	jal	37e <System_Init>
     42a:	00002437          	lui	s0,0x2
     42e:	021000ef          	jal	ra,c4e <Touch_Button_Update>
     432:	08f000ef          	jal	ra,cc0 <Touch_Button_Get_Event>
     436:	4789                	li	a5,2
     438:	02f50463          	beq	a0,a5,460 <main+0x86>
     43c:	478d                	li	a5,3
     43e:	02f50763          	beq	a0,a5,46c <main+0x92>
     442:	4785                	li	a5,1
     444:	00f51963          	bne	a0,a5,456 <main+0x7c>
     448:	00002537          	lui	a0,0x2
     44c:	b4c50513          	addi	a0,a0,-1204 # 1b4c <CSWTCH.2+0x138>
     450:	474010ef          	jal	ra,18c4 <puts>
     454:	3bf9                	jal	232 <Display_Control_Turn_On>
     456:	35ed                	jal	340 <Display_Control_Update>
     458:	4505                	li	a0,1
     45a:	5cf000ef          	jal	ra,1228 <Delay_Ms>
     45e:	bfc1                	j	42e <main+0x54>
     460:	b7840513          	addi	a0,s0,-1160 # 1b78 <CSWTCH.2+0x164>
     464:	460010ef          	jal	ra,18c4 <puts>
     468:	3bed                	jal	262 <Display_Control_Toggle>
     46a:	b7f5                	j	456 <main+0x7c>
     46c:	00002537          	lui	a0,0x2
     470:	ba450513          	addi	a0,a0,-1116 # 1ba4 <CSWTCH.2+0x190>
     474:	450010ef          	jal	ra,18c4 <puts>
     478:	3bc1                	jal	248 <Display_Control_Turn_Off>
     47a:	bff1                	j	456 <main+0x7c>

0000047c <PWM_Set_Brightness>:
     47c:	c25ff2ef          	jal	t0,a0 <__riscv_save_0>
     480:	3e800793          	li	a5,1000
     484:	3e800413          	li	s0,1000
     488:	00a7e363          	bltu	a5,a0,48e <PWM_Set_Brightness+0x12>
     48c:	842a                	mv	s0,a0
     48e:	01041593          	slli	a1,s0,0x10
     492:	40013537          	lui	a0,0x40013
     496:	81c1                	srli	a1,a1,0x10
     498:	c0050513          	addi	a0,a0,-1024 # 40012c00 <__global_pointer$+0x200123c0>
     49c:	43d000ef          	jal	ra,10d8 <TIM_SetCompare1>
     4a0:	80819e23          	sh	s0,-2020(gp) # 2000005c <pwm_control>
     4a4:	b119                	j	aa <__riscv_restore_0>

000004a6 <PWM_Update_Fade>:
     4a6:	81c18793          	addi	a5,gp,-2020 # 2000005c <pwm_control>
     4aa:	23d8                	lbu	a4,4(a5)
     4ac:	c721                	beqz	a4,4f4 <PWM_Update_Fade+0x4e>
     4ae:	bf3ff2ef          	jal	t0,a0 <__riscv_save_0>
     4b2:	238a                	lhu	a0,0(a5)
     4b4:	23ba                	lhu	a4,2(a5)
     4b6:	00e57d63          	bgeu	a0,a4,4d0 <PWM_Update_Fade+0x2a>
     4ba:	33d4                	lbu	a3,5(a5)
     4bc:	9536                	add	a0,a0,a3
     4be:	0542                	slli	a0,a0,0x10
     4c0:	8141                	srli	a0,a0,0x10
     4c2:	00e56563          	bltu	a0,a4,4cc <PWM_Update_Fade+0x26>
     4c6:	00078223          	sb	zero,4(a5)
     4ca:	853a                	mv	a0,a4
     4cc:	3f45                	jal	47c <PWM_Set_Brightness>
     4ce:	bef1                	j	aa <__riscv_restore_0>
     4d0:	00a77f63          	bgeu	a4,a0,4ee <PWM_Update_Fade+0x48>
     4d4:	81c18693          	addi	a3,gp,-2020 # 2000005c <pwm_control>
     4d8:	32dc                	lbu	a5,5(a3)
     4da:	00f56763          	bltu	a0,a5,4e8 <PWM_Update_Fade+0x42>
     4de:	8d1d                	sub	a0,a0,a5
     4e0:	0542                	slli	a0,a0,0x10
     4e2:	8141                	srli	a0,a0,0x10
     4e4:	fea764e3          	bltu	a4,a0,4cc <PWM_Update_Fade+0x26>
     4e8:	00068223          	sb	zero,4(a3)
     4ec:	bff9                	j	4ca <PWM_Update_Fade+0x24>
     4ee:	00078223          	sb	zero,4(a5)
     4f2:	bff1                	j	4ce <PWM_Update_Fade+0x28>
     4f4:	8082                	ret

000004f6 <PWM_Turn_On>:
     4f6:	81c18793          	addi	a5,gp,-2020 # 2000005c <pwm_control>
     4fa:	1f400713          	li	a4,500
     4fe:	a3ba                	sh	a4,2(a5)
     500:	4705                	li	a4,1
     502:	a3d8                	sb	a4,4(a5)
     504:	8082                	ret

00000506 <PWM_Turn_Off>:
     506:	81c18793          	addi	a5,gp,-2020 # 2000005c <pwm_control>
     50a:	4705                	li	a4,1
     50c:	00079123          	sh	zero,2(a5)
     510:	a3d8                	sb	a4,4(a5)
     512:	8082                	ret

00000514 <PWM_Get_Brightness>:
     514:	81c1d503          	lhu	a0,-2020(gp) # 2000005c <pwm_control>
     518:	8082                	ret

0000051a <SPI_send_DMA>:
     51a:	400207b7          	lui	a5,0x40020
     51e:	dfc8                	sw	a0,60(a5)
     520:	dbcc                	sw	a1,52(a5)
     522:	5b98                	lw	a4,48(a5)
     524:	400206b7          	lui	a3,0x40020
     528:	20000593          	li	a1,512
     52c:	00176713          	ori	a4,a4,1
     530:	db98                	sw	a4,48(a5)
     532:	67c1                	lui	a5,0x10
     534:	17fd                	addi	a5,a5,-1
     536:	167d                	addi	a2,a2,-1
     538:	0642                	slli	a2,a2,0x10
     53a:	8241                	srli	a2,a2,0x10
     53c:	00f61863          	bne	a2,a5,54c <SPI_send_DMA+0x32>
     540:	40020737          	lui	a4,0x40020
     544:	5b1c                	lw	a5,48(a4)
     546:	9bf9                	andi	a5,a5,-2
     548:	db1c                	sw	a5,48(a4)
     54a:	8082                	ret
     54c:	c2cc                	sw	a1,4(a3)
     54e:	4298                	lw	a4,0(a3)
     550:	20077713          	andi	a4,a4,512
     554:	df6d                	beqz	a4,54e <SPI_send_DMA+0x34>
     556:	b7c5                	j	536 <SPI_send_DMA+0x1c>

00000558 <SPI_send>:
     558:	400137b7          	lui	a5,0x40013
     55c:	a7ca                	sh	a0,12(a5)
     55e:	40013737          	lui	a4,0x40013
     562:	271e                	lhu	a5,8(a4)
     564:	8b89                	andi	a5,a5,2
     566:	dff5                	beqz	a5,562 <SPI_send+0xa>
     568:	8082                	ret

0000056a <write_command_8>:
     56a:	b37ff2ef          	jal	t0,a0 <__riscv_save_0>
     56e:	40011737          	lui	a4,0x40011
     572:	4b5c                	lw	a5,20(a4)
     574:	0087e793          	ori	a5,a5,8
     578:	cb5c                	sw	a5,20(a4)
     57a:	3ff9                	jal	558 <SPI_send>
     57c:	b63d                	j	aa <__riscv_restore_0>

0000057e <write_data_16>:
     57e:	b23ff2ef          	jal	t0,a0 <__riscv_save_0>
     582:	40011737          	lui	a4,0x40011
     586:	4b1c                	lw	a5,16(a4)
     588:	842a                	mv	s0,a0
     58a:	8121                	srli	a0,a0,0x8
     58c:	0087e793          	ori	a5,a5,8
     590:	cb1c                	sw	a5,16(a4)
     592:	37d9                	jal	558 <SPI_send>
     594:	0ff47513          	andi	a0,s0,255
     598:	37c1                	jal	558 <SPI_send>
     59a:	be01                	j	aa <__riscv_restore_0>

0000059c <tft_set_window>:
     59c:	b05ff2ef          	jal	t0,a0 <__riscv_save_0>
     5a0:	1151                	addi	sp,sp,-12
     5a2:	842a                	mv	s0,a0
     5a4:	02a00513          	li	a0,42
     5a8:	c036                	sw	a3,0(sp)
     5aa:	c42e                	sw	a1,8(sp)
     5ac:	c232                	sw	a2,4(sp)
     5ae:	3f75                	jal	56a <write_command_8>
     5b0:	8522                	mv	a0,s0
     5b2:	37f1                	jal	57e <write_data_16>
     5b4:	4612                	lw	a2,4(sp)
     5b6:	8532                	mv	a0,a2
     5b8:	37d9                	jal	57e <write_data_16>
     5ba:	02b00513          	li	a0,43
     5be:	3775                	jal	56a <write_command_8>
     5c0:	45a2                	lw	a1,8(sp)
     5c2:	852e                	mv	a0,a1
     5c4:	3f6d                	jal	57e <write_data_16>
     5c6:	4682                	lw	a3,0(sp)
     5c8:	8536                	mv	a0,a3
     5ca:	3f55                	jal	57e <write_data_16>
     5cc:	02c00513          	li	a0,44
     5d0:	3f69                	jal	56a <write_command_8>
     5d2:	0131                	addi	sp,sp,12
     5d4:	bcd9                	j	aa <__riscv_restore_0>

000005d6 <tft_init>:
     5d6:	acbff2ef          	jal	t0,a0 <__riscv_save_0>
     5da:	400216b7          	lui	a3,0x40021
     5de:	4e9c                	lw	a5,24(a3)
     5e0:	6705                	lui	a4,0x1
     5e2:	0741                	addi	a4,a4,16
     5e4:	8fd9                	or	a5,a5,a4
     5e6:	40011437          	lui	s0,0x40011
     5ea:	ce9c                	sw	a5,24(a3)
     5ec:	401c                	lw	a5,0(s0)
     5ee:	777d                	lui	a4,0xfffff
     5f0:	0ff70713          	addi	a4,a4,255 # fffff0ff <__global_pointer$+0xdfffe8bf>
     5f4:	8ff9                	and	a5,a5,a4
     5f6:	c01c                	sw	a5,0(s0)
     5f8:	401c                	lw	a5,0(s0)
     5fa:	7745                	lui	a4,0xffff1
     5fc:	177d                	addi	a4,a4,-1
     5fe:	3007e793          	ori	a5,a5,768
     602:	c01c                	sw	a5,0(s0)
     604:	401c                	lw	a5,0(s0)
     606:	fff10637          	lui	a2,0xfff10
     60a:	167d                	addi	a2,a2,-1
     60c:	8ff9                	and	a5,a5,a4
     60e:	c01c                	sw	a5,0(s0)
     610:	401c                	lw	a5,0(s0)
     612:	670d                	lui	a4,0x3
     614:	1101                	addi	sp,sp,-32
     616:	8fd9                	or	a5,a5,a4
     618:	c01c                	sw	a5,0(s0)
     61a:	401c                	lw	a5,0(s0)
     61c:	0b070713          	addi	a4,a4,176 # 30b0 <_data_lma+0xfdc>
     620:	03200513          	li	a0,50
     624:	8ff1                	and	a5,a5,a2
     626:	c01c                	sw	a5,0(s0)
     628:	401c                	lw	a5,0(s0)
     62a:	00030637          	lui	a2,0x30
     62e:	8fd1                	or	a5,a5,a2
     630:	c01c                	sw	a5,0(s0)
     632:	401c                	lw	a5,0(s0)
     634:	ff100637          	lui	a2,0xff100
     638:	167d                	addi	a2,a2,-1
     63a:	8ff1                	and	a5,a5,a2
     63c:	c01c                	sw	a5,0(s0)
     63e:	401c                	lw	a5,0(s0)
     640:	00b00637          	lui	a2,0xb00
     644:	8fd1                	or	a5,a5,a2
     646:	c01c                	sw	a5,0(s0)
     648:	401c                	lw	a5,0(s0)
     64a:	f1000637          	lui	a2,0xf1000
     64e:	167d                	addi	a2,a2,-1
     650:	8ff1                	and	a5,a5,a2
     652:	c01c                	sw	a5,0(s0)
     654:	401c                	lw	a5,0(s0)
     656:	0b000637          	lui	a2,0xb000
     65a:	8fd1                	or	a5,a5,a2
     65c:	7671                	lui	a2,0xffffc
     65e:	c01c                	sw	a5,0(s0)
     660:	30460613          	addi	a2,a2,772 # ffffc304 <__global_pointer$+0xdfffbac4>
     664:	400137b7          	lui	a5,0x40013
     668:	a392                	sh	a2,0(a5)
     66a:	461d                	li	a2,7
     66c:	ab92                	sh	a2,16(a5)
     66e:	23d2                	lhu	a2,4(a5)
     670:	07b1                	addi	a5,a5,12
     672:	00266613          	ori	a2,a2,2
     676:	fec79c23          	sh	a2,-8(a5) # 40012ff8 <__global_pointer$+0x200127b8>
     67a:	ff47d603          	lhu	a2,-12(a5)
     67e:	04066613          	ori	a2,a2,64
     682:	fec79a23          	sh	a2,-12(a5)
     686:	4ad0                	lw	a2,20(a3)
     688:	00166613          	ori	a2,a2,1
     68c:	cad0                	sw	a2,20(a3)
     68e:	400206b7          	lui	a3,0x40020
     692:	da98                	sw	a4,48(a3)
     694:	de9c                	sw	a5,56(a3)
     696:	485c                	lw	a5,20(s0)
     698:	0047e793          	ori	a5,a5,4
     69c:	c85c                	sw	a5,20(s0)
     69e:	38b000ef          	jal	ra,1228 <Delay_Ms>
     6a2:	481c                	lw	a5,16(s0)
     6a4:	03200513          	li	a0,50
     6a8:	0047e793          	ori	a5,a5,4
     6ac:	c81c                	sw	a5,16(s0)
     6ae:	37b000ef          	jal	ra,1228 <Delay_Ms>
     6b2:	485c                	lw	a5,20(s0)
     6b4:	4545                	li	a0,17
     6b6:	0107e793          	ori	a5,a5,16
     6ba:	c85c                	sw	a5,20(s0)
     6bc:	357d                	jal	56a <write_command_8>
     6be:	07800513          	li	a0,120
     6c2:	367000ef          	jal	ra,1228 <Delay_Ms>
     6c6:	03600513          	li	a0,54
     6ca:	3545                	jal	56a <write_command_8>
     6cc:	481c                	lw	a5,16(s0)
     6ce:	0a800513          	li	a0,168
     6d2:	0087e793          	ori	a5,a5,8
     6d6:	c81c                	sw	a5,16(s0)
     6d8:	3541                	jal	558 <SPI_send>
     6da:	03a00513          	li	a0,58
     6de:	3571                	jal	56a <write_command_8>
     6e0:	481c                	lw	a5,16(s0)
     6e2:	4515                	li	a0,5
     6e4:	0087e793          	ori	a5,a5,8
     6e8:	c81c                	sw	a5,16(s0)
     6ea:	35bd                	jal	558 <SPI_send>
     6ec:	6589                	lui	a1,0x2
     6ee:	9e058493          	addi	s1,a1,-1568 # 19e0 <memcpy+0xd2>
     6f2:	4641                	li	a2,16
     6f4:	9e058593          	addi	a1,a1,-1568
     6f8:	850a                	mv	a0,sp
     6fa:	214010ef          	jal	ra,190e <memcpy>
     6fe:	0e000513          	li	a0,224
     702:	35a5                	jal	56a <write_command_8>
     704:	481c                	lw	a5,16(s0)
     706:	850a                	mv	a0,sp
     708:	4605                	li	a2,1
     70a:	0087e793          	ori	a5,a5,8
     70e:	c81c                	sw	a5,16(s0)
     710:	45c1                	li	a1,16
     712:	3521                	jal	51a <SPI_send_DMA>
     714:	01048593          	addi	a1,s1,16
     718:	4641                	li	a2,16
     71a:	0808                	addi	a0,sp,16
     71c:	1f2010ef          	jal	ra,190e <memcpy>
     720:	0e100513          	li	a0,225
     724:	3599                	jal	56a <write_command_8>
     726:	481c                	lw	a5,16(s0)
     728:	4605                	li	a2,1
     72a:	45c1                	li	a1,16
     72c:	0087e793          	ori	a5,a5,8
     730:	c81c                	sw	a5,16(s0)
     732:	0808                	addi	a0,sp,16
     734:	33dd                	jal	51a <SPI_send_DMA>
     736:	4529                	li	a0,10
     738:	2f1000ef          	jal	ra,1228 <Delay_Ms>
     73c:	02100513          	li	a0,33
     740:	352d                	jal	56a <write_command_8>
     742:	454d                	li	a0,19
     744:	351d                	jal	56a <write_command_8>
     746:	4529                	li	a0,10
     748:	2e1000ef          	jal	ra,1228 <Delay_Ms>
     74c:	02900513          	li	a0,41
     750:	3d29                	jal	56a <write_command_8>
     752:	4529                	li	a0,10
     754:	2d5000ef          	jal	ra,1228 <Delay_Ms>
     758:	481c                	lw	a5,16(s0)
     75a:	0107e793          	ori	a5,a5,16
     75e:	c81c                	sw	a5,16(s0)
     760:	6105                	addi	sp,sp,32
     762:	949ff06f          	j	aa <__riscv_restore_0>

00000766 <tft_set_cursor>:
     766:	0505                	addi	a0,a0,1
     768:	96a19223          	sh	a0,-1692(gp) # 200001a4 <_cursor_x>
     76c:	05e9                	addi	a1,a1,26
     76e:	96b19323          	sh	a1,-1690(gp) # 200001a6 <_cursor_y>
     772:	8082                	ret

00000774 <tft_set_color>:
     774:	200007b7          	lui	a5,0x20000
     778:	00a79023          	sh	a0,0(a5) # 20000000 <_highcode_vma_end>
     77c:	8082                	ret

0000077e <tft_set_background_color>:
     77e:	82a19123          	sh	a0,-2014(gp) # 20000062 <_bg_color>
     782:	8082                	ret

00000784 <tft_print_char>:
     784:	91dff2ef          	jal	t0,a0 <__riscv_save_0>
     788:	00251793          	slli	a5,a0,0x2
     78c:	953e                	add	a0,a0,a5
     78e:	8221d783          	lhu	a5,-2014(gp) # 20000062 <_bg_color>
     792:	1131                	addi	sp,sp,-20
     794:	0087d713          	srli	a4,a5,0x8
     798:	0ff7f793          	andi	a5,a5,255
     79c:	c63e                	sw	a5,12(sp)
     79e:	200007b7          	lui	a5,0x20000
     7a2:	0007d783          	lhu	a5,0(a5) # 20000000 <_highcode_vma_end>
     7a6:	c43a                	sw	a4,8(sp)
     7a8:	4281                	li	t0,0
     7aa:	0087d713          	srli	a4,a5,0x8
     7ae:	0ff7f793          	andi	a5,a5,255
     7b2:	c23e                	sw	a5,4(sp)
     7b4:	6789                	lui	a5,0x2
     7b6:	bcc78793          	addi	a5,a5,-1076 # 1bcc <font>
     7ba:	c03a                	sw	a4,0(sp)
     7bc:	4681                	li	a3,0
     7be:	c83e                	sw	a5,16(sp)
     7c0:	82418313          	addi	t1,gp,-2012 # 20000064 <_buffer>
     7c4:	4785                	li	a5,1
     7c6:	005790b3          	sll	ra,a5,t0
     7ca:	85b6                	mv	a1,a3
     7cc:	4601                	li	a2,0
     7ce:	44c2                	lw	s1,16(sp)
     7d0:	00c503b3          	add	t2,a0,a2
     7d4:	872e                	mv	a4,a1
     7d6:	93a6                	add	t2,t2,s1
     7d8:	0003c383          	lbu	t2,0(t2)
     7dc:	00158793          	addi	a5,a1,1
     7e0:	0589                	addi	a1,a1,2
     7e2:	07c2                	slli	a5,a5,0x10
     7e4:	05c2                	slli	a1,a1,0x10
     7e6:	0013f3b3          	and	t2,t2,ra
     7ea:	83c1                	srli	a5,a5,0x10
     7ec:	81c1                	srli	a1,a1,0x10
     7ee:	971a                	add	a4,a4,t1
     7f0:	06038763          	beqz	t2,85e <tft_print_char+0xda>
     7f4:	4482                	lw	s1,0(sp)
     7f6:	979a                	add	a5,a5,t1
     7f8:	a304                	sb	s1,0(a4)
     7fa:	4712                	lw	a4,4(sp)
     7fc:	a398                	sb	a4,0(a5)
     7fe:	0605                	addi	a2,a2,1
     800:	4795                	li	a5,5
     802:	fcf616e3          	bne	a2,a5,7ce <tft_print_char+0x4a>
     806:	06a9                	addi	a3,a3,10
     808:	06c2                	slli	a3,a3,0x10
     80a:	82c1                	srli	a3,a3,0x10
     80c:	04600793          	li	a5,70
     810:	0285                	addi	t0,t0,1
     812:	faf699e3          	bne	a3,a5,7c4 <tft_print_char+0x40>
     816:	400114b7          	lui	s1,0x40011
     81a:	48dc                	lw	a5,20(s1)
     81c:	0107e793          	ori	a5,a5,16
     820:	c8dc                	sw	a5,20(s1)
     822:	9641d503          	lhu	a0,-1692(gp) # 200001a4 <_cursor_x>
     826:	9661d583          	lhu	a1,-1690(gp) # 200001a6 <_cursor_y>
     82a:	00450613          	addi	a2,a0,4
     82e:	0642                	slli	a2,a2,0x10
     830:	00658693          	addi	a3,a1,6
     834:	06c2                	slli	a3,a3,0x10
     836:	82c1                	srli	a3,a3,0x10
     838:	8241                	srli	a2,a2,0x10
     83a:	338d                	jal	59c <tft_set_window>
     83c:	489c                	lw	a5,16(s1)
     83e:	4605                	li	a2,1
     840:	04600593          	li	a1,70
     844:	0087e793          	ori	a5,a5,8
     848:	c89c                	sw	a5,16(s1)
     84a:	82418513          	addi	a0,gp,-2012 # 20000064 <_buffer>
     84e:	31f1                	jal	51a <SPI_send_DMA>
     850:	489c                	lw	a5,16(s1)
     852:	0107e793          	ori	a5,a5,16
     856:	c89c                	sw	a5,16(s1)
     858:	0151                	addi	sp,sp,20
     85a:	851ff06f          	j	aa <__riscv_restore_0>
     85e:	44a2                	lw	s1,8(sp)
     860:	979a                	add	a5,a5,t1
     862:	a304                	sb	s1,0(a4)
     864:	4732                	lw	a4,12(sp)
     866:	bf59                	j	7fc <tft_print_char+0x78>

00000868 <tft_print>:
     868:	839ff2ef          	jal	t0,a0 <__riscv_save_0>
     86c:	842a                	mv	s0,a0
     86e:	00040503          	lb	a0,0(s0) # 40011000 <__global_pointer$+0x200107c0>
     872:	e119                	bnez	a0,878 <tft_print+0x10>
     874:	837ff06f          	j	aa <__riscv_restore_0>
     878:	3731                	jal	784 <tft_print_char>
     87a:	96418713          	addi	a4,gp,-1692 # 200001a4 <_cursor_x>
     87e:	231e                	lhu	a5,0(a4)
     880:	0405                	addi	s0,s0,1
     882:	0799                	addi	a5,a5,6
     884:	a31e                	sh	a5,0(a4)
     886:	b7e5                	j	86e <tft_print+0x6>

00000888 <tft_print_number>:
     888:	819ff2ef          	jal	t0,a0 <__riscv_save_0>
     88c:	1141                	addi	sp,sp,-16
     88e:	87aa                	mv	a5,a0
     890:	86ae                	mv	a3,a1
     892:	4701                	li	a4,0
     894:	00055563          	bgez	a0,89e <tft_print_number+0x16>
     898:	40a007b3          	neg	a5,a0
     89c:	4705                	li	a4,1
     89e:	96818613          	addi	a2,gp,-1688 # 200001a8 <str.4169>
     8a2:	000605a3          	sb	zero,11(a2)
     8a6:	442d                	li	s0,11
     8a8:	96818493          	addi	s1,gp,-1688 # 200001a8 <str.4169>
     8ac:	eba9                	bnez	a5,8fe <tft_print_number+0x76>
     8ae:	47ad                	li	a5,11
     8b0:	00f41663          	bne	s0,a5,8bc <tft_print_number+0x34>
     8b4:	03000793          	li	a5,48
     8b8:	a4bc                	sb	a5,10(s1)
     8ba:	4429                	li	s0,10
     8bc:	cb09                	beqz	a4,8ce <tft_print_number+0x46>
     8be:	147d                	addi	s0,s0,-1
     8c0:	0ff47413          	andi	s0,s0,255
     8c4:	008487b3          	add	a5,s1,s0
     8c8:	02d00713          	li	a4,45
     8cc:	a398                	sb	a4,0(a5)
     8ce:	472d                	li	a4,11
     8d0:	8f01                	sub	a4,a4,s0
     8d2:	00171793          	slli	a5,a4,0x1
     8d6:	97ba                	add	a5,a5,a4
     8d8:	0786                	slli	a5,a5,0x1
     8da:	17fd                	addi	a5,a5,-1
     8dc:	07c2                	slli	a5,a5,0x10
     8de:	83c1                	srli	a5,a5,0x10
     8e0:	00d7f963          	bgeu	a5,a3,8f2 <tft_print_number+0x6a>
     8e4:	96418713          	addi	a4,gp,-1692 # 200001a4 <_cursor_x>
     8e8:	2312                	lhu	a2,0(a4)
     8ea:	96b2                	add	a3,a3,a2
     8ec:	40f687b3          	sub	a5,a3,a5
     8f0:	a31e                	sh	a5,0(a4)
     8f2:	00848533          	add	a0,s1,s0
     8f6:	3f8d                	jal	868 <tft_print>
     8f8:	0141                	addi	sp,sp,16
     8fa:	fb0ff06f          	j	aa <__riscv_restore_0>
     8fe:	147d                	addi	s0,s0,-1
     900:	0ff47413          	andi	s0,s0,255
     904:	00848633          	add	a2,s1,s0
     908:	45a9                	li	a1,10
     90a:	853e                	mv	a0,a5
     90c:	c636                	sw	a3,12(sp)
     90e:	c43a                	sw	a4,8(sp)
     910:	c232                	sw	a2,4(sp)
     912:	c03e                	sw	a5,0(sp)
     914:	811ff0ef          	jal	ra,124 <__modsi3>
     918:	4782                	lw	a5,0(sp)
     91a:	4612                	lw	a2,4(sp)
     91c:	03050513          	addi	a0,a0,48
     920:	45a9                	li	a1,10
     922:	a208                	sb	a0,0(a2)
     924:	853e                	mv	a0,a5
     926:	fa6ff0ef          	jal	ra,cc <__divsi3>
     92a:	87aa                	mv	a5,a0
     92c:	46b2                	lw	a3,12(sp)
     92e:	4722                	lw	a4,8(sp)
     930:	bfb5                	j	8ac <tft_print_number+0x24>

00000932 <tft_fill_rect>:
     932:	f6eff2ef          	jal	t0,a0 <__riscv_save_0>
     936:	0505                	addi	a0,a0,1
     938:	05e9                	addi	a1,a1,26
     93a:	0542                	slli	a0,a0,0x10
     93c:	05c2                	slli	a1,a1,0x10
     93e:	8336                	mv	t1,a3
     940:	1171                	addi	sp,sp,-4
     942:	8141                	srli	a0,a0,0x10
     944:	81c1                	srli	a1,a1,0x10
     946:	00875393          	srli	t2,a4,0x8
     94a:	4781                	li	a5,0
     94c:	82418693          	addi	a3,gp,-2012 # 20000064 <_buffer>
     950:	00179413          	slli	s0,a5,0x1
     954:	0442                	slli	s0,s0,0x10
     956:	8041                	srli	s0,s0,0x10
     958:	04c79563          	bne	a5,a2,9a2 <tft_fill_rect+0x70>
     95c:	400114b7          	lui	s1,0x40011
     960:	48d8                	lw	a4,20(s1)
     962:	fff30693          	addi	a3,t1,-1
     966:	fff78613          	addi	a2,a5,-1
     96a:	96ae                	add	a3,a3,a1
     96c:	962a                	add	a2,a2,a0
     96e:	01076713          	ori	a4,a4,16
     972:	06c2                	slli	a3,a3,0x10
     974:	0642                	slli	a2,a2,0x10
     976:	c8d8                	sw	a4,20(s1)
     978:	82c1                	srli	a3,a3,0x10
     97a:	8241                	srli	a2,a2,0x10
     97c:	c01a                	sw	t1,0(sp)
     97e:	3939                	jal	59c <tft_set_window>
     980:	489c                	lw	a5,16(s1)
     982:	4302                	lw	t1,0(sp)
     984:	0087e793          	ori	a5,a5,8
     988:	c89c                	sw	a5,16(s1)
     98a:	861a                	mv	a2,t1
     98c:	85a2                	mv	a1,s0
     98e:	82418513          	addi	a0,gp,-2012 # 20000064 <_buffer>
     992:	3661                	jal	51a <SPI_send_DMA>
     994:	489c                	lw	a5,16(s1)
     996:	0107e793          	ori	a5,a5,16
     99a:	c89c                	sw	a5,16(s1)
     99c:	0111                	addi	sp,sp,4
     99e:	f0cff06f          	j	aa <__riscv_restore_0>
     9a2:	008684b3          	add	s1,a3,s0
     9a6:	0405                	addi	s0,s0,1
     9a8:	0442                	slli	s0,s0,0x10
     9aa:	8041                	srli	s0,s0,0x10
     9ac:	0785                	addi	a5,a5,1
     9ae:	00748023          	sb	t2,0(s1) # 40011000 <__global_pointer$+0x200107c0>
     9b2:	9436                	add	s0,s0,a3
     9b4:	07c2                	slli	a5,a5,0x10
     9b6:	a018                	sb	a4,0(s0)
     9b8:	83c1                	srli	a5,a5,0x10
     9ba:	bf59                	j	950 <tft_fill_rect+0x1e>

000009bc <SystemInit>:
     9bc:	ee4ff2ef          	jal	t0,a0 <__riscv_save_0>
     9c0:	40021437          	lui	s0,0x40021
     9c4:	401c                	lw	a5,0(s0)
     9c6:	f8ff0737          	lui	a4,0xf8ff0
     9ca:	1161                	addi	sp,sp,-8
     9cc:	0017e793          	ori	a5,a5,1
     9d0:	c01c                	sw	a5,0(s0)
     9d2:	405c                	lw	a5,4(s0)
     9d4:	4541                	li	a0,16
     9d6:	8ff9                	and	a5,a5,a4
     9d8:	c05c                	sw	a5,4(s0)
     9da:	401c                	lw	a5,0(s0)
     9dc:	fef70737          	lui	a4,0xfef70
     9e0:	177d                	addi	a4,a4,-1
     9e2:	8ff9                	and	a5,a5,a4
     9e4:	c01c                	sw	a5,0(s0)
     9e6:	401c                	lw	a5,0(s0)
     9e8:	fffc0737          	lui	a4,0xfffc0
     9ec:	177d                	addi	a4,a4,-1
     9ee:	8ff9                	and	a5,a5,a4
     9f0:	c01c                	sw	a5,0(s0)
     9f2:	405c                	lw	a5,4(s0)
     9f4:	7741                	lui	a4,0xffff0
     9f6:	177d                	addi	a4,a4,-1
     9f8:	8ff9                	and	a5,a5,a4
     9fa:	c05c                	sw	a5,4(s0)
     9fc:	009f07b7          	lui	a5,0x9f0
     a00:	c41c                	sw	a5,8(s0)
     a02:	23bd                	jal	f70 <RCC_AdjustHSICalibrationValue>
     a04:	4c1c                	lw	a5,24(s0)
     a06:	00020637          	lui	a2,0x20
     a0a:	0207e793          	ori	a5,a5,32
     a0e:	cc1c                	sw	a5,24(s0)
     a10:	400117b7          	lui	a5,0x40011
     a14:	4007a703          	lw	a4,1024(a5) # 40011400 <__global_pointer$+0x20010bc0>
     a18:	40078693          	addi	a3,a5,1024
     a1c:	f0f77713          	andi	a4,a4,-241
     a20:	40e7a023          	sw	a4,1024(a5)
     a24:	4007a703          	lw	a4,1024(a5)
     a28:	08076713          	ori	a4,a4,128
     a2c:	40e7a023          	sw	a4,1024(a5)
     a30:	4789                	li	a5,2
     a32:	ca9c                	sw	a5,16(a3)
     a34:	c002                	sw	zero,0(sp)
     a36:	c202                	sw	zero,4(sp)
     a38:	4c1c                	lw	a5,24(s0)
     a3a:	40010737          	lui	a4,0x40010
     a3e:	66a1                	lui	a3,0x8
     a40:	0017e793          	ori	a5,a5,1
     a44:	cc1c                	sw	a5,24(s0)
     a46:	435c                	lw	a5,4(a4)
     a48:	8fd5                	or	a5,a5,a3
     a4a:	c35c                	sw	a5,4(a4)
     a4c:	401c                	lw	a5,0(s0)
     a4e:	6741                	lui	a4,0x10
     a50:	400216b7          	lui	a3,0x40021
     a54:	8fd9                	or	a5,a5,a4
     a56:	c01c                	sw	a5,0(s0)
     a58:	6709                	lui	a4,0x2
     a5a:	429c                	lw	a5,0(a3)
     a5c:	8ff1                	and	a5,a5,a2
     a5e:	c23e                	sw	a5,4(sp)
     a60:	4782                	lw	a5,0(sp)
     a62:	0785                	addi	a5,a5,1
     a64:	c03e                	sw	a5,0(sp)
     a66:	4792                	lw	a5,4(sp)
     a68:	e781                	bnez	a5,a70 <SystemInit+0xb4>
     a6a:	4782                	lw	a5,0(sp)
     a6c:	fee797e3          	bne	a5,a4,a5a <SystemInit+0x9e>
     a70:	400217b7          	lui	a5,0x40021
     a74:	439c                	lw	a5,0(a5)
     a76:	00e79713          	slli	a4,a5,0xe
     a7a:	06075963          	bgez	a4,aec <SystemInit+0x130>
     a7e:	4785                	li	a5,1
     a80:	c23e                	sw	a5,4(sp)
     a82:	4712                	lw	a4,4(sp)
     a84:	4785                	li	a5,1
     a86:	06f71063          	bne	a4,a5,ae6 <SystemInit+0x12a>
     a8a:	400227b7          	lui	a5,0x40022
     a8e:	4398                	lw	a4,0(a5)
     a90:	76c1                	lui	a3,0xffff0
     a92:	16fd                	addi	a3,a3,-1
     a94:	9b71                	andi	a4,a4,-4
     a96:	c398                	sw	a4,0(a5)
     a98:	4398                	lw	a4,0(a5)
     a9a:	00176713          	ori	a4,a4,1
     a9e:	c398                	sw	a4,0(a5)
     aa0:	400217b7          	lui	a5,0x40021
     aa4:	43d8                	lw	a4,4(a5)
     aa6:	c3d8                	sw	a4,4(a5)
     aa8:	43d8                	lw	a4,4(a5)
     aaa:	8f75                	and	a4,a4,a3
     aac:	c3d8                	sw	a4,4(a5)
     aae:	43d8                	lw	a4,4(a5)
     ab0:	66c1                	lui	a3,0x10
     ab2:	8f55                	or	a4,a4,a3
     ab4:	c3d8                	sw	a4,4(a5)
     ab6:	4398                	lw	a4,0(a5)
     ab8:	010006b7          	lui	a3,0x1000
     abc:	8f55                	or	a4,a4,a3
     abe:	c398                	sw	a4,0(a5)
     ac0:	4398                	lw	a4,0(a5)
     ac2:	00671693          	slli	a3,a4,0x6
     ac6:	fe06dde3          	bgez	a3,ac0 <SystemInit+0x104>
     aca:	43d8                	lw	a4,4(a5)
     acc:	400216b7          	lui	a3,0x40021
     ad0:	9b71                	andi	a4,a4,-4
     ad2:	c3d8                	sw	a4,4(a5)
     ad4:	43d8                	lw	a4,4(a5)
     ad6:	00276713          	ori	a4,a4,2
     ada:	c3d8                	sw	a4,4(a5)
     adc:	4721                	li	a4,8
     ade:	42dc                	lw	a5,4(a3)
     ae0:	8bb1                	andi	a5,a5,12
     ae2:	fee79ee3          	bne	a5,a4,ade <SystemInit+0x122>
     ae6:	0121                	addi	sp,sp,8
     ae8:	dc2ff06f          	j	aa <__riscv_restore_0>
     aec:	c202                	sw	zero,4(sp)
     aee:	bf51                	j	a82 <SystemInit+0xc6>

00000af0 <SystemCoreClockUpdate>:
     af0:	db0ff2ef          	jal	t0,a0 <__riscv_save_0>
     af4:	40021737          	lui	a4,0x40021
     af8:	435c                	lw	a5,4(a4)
     afa:	20000437          	lui	s0,0x20000
     afe:	4691                	li	a3,4
     b00:	8bb1                	andi	a5,a5,12
     b02:	01440413          	addi	s0,s0,20 # 20000014 <SystemCoreClock>
     b06:	00d78563          	beq	a5,a3,b10 <SystemCoreClockUpdate+0x20>
     b0a:	46a1                	li	a3,8
     b0c:	04d78263          	beq	a5,a3,b50 <SystemCoreClockUpdate+0x60>
     b10:	016e37b7          	lui	a5,0x16e3
     b14:	60078793          	addi	a5,a5,1536 # 16e3600 <_data_lma+0x16e152c>
     b18:	c01c                	sw	a5,0(s0)
     b1a:	400216b7          	lui	a3,0x40021
     b1e:	42dc                	lw	a5,4(a3)
     b20:	4008                	lw	a0,0(s0)
     b22:	8391                	srli	a5,a5,0x4
     b24:	00f7f713          	andi	a4,a5,15
     b28:	200007b7          	lui	a5,0x20000
     b2c:	00478793          	addi	a5,a5,4 # 20000004 <AHBPrescTable>
     b30:	97ba                	add	a5,a5,a4
     b32:	238c                	lbu	a1,0(a5)
     b34:	42dc                	lw	a5,4(a3)
     b36:	0ff5f593          	andi	a1,a1,255
     b3a:	0807f793          	andi	a5,a5,128
     b3e:	00b55733          	srl	a4,a0,a1
     b42:	e781                	bnez	a5,b4a <SystemCoreClockUpdate+0x5a>
     b44:	d90ff0ef          	jal	ra,d4 <__udivsi3>
     b48:	872a                	mv	a4,a0
     b4a:	c018                	sw	a4,0(s0)
     b4c:	d5eff06f          	j	aa <__riscv_restore_0>
     b50:	435c                	lw	a5,4(a4)
     b52:	02dc77b7          	lui	a5,0x2dc7
     b56:	c0078793          	addi	a5,a5,-1024 # 2dc6c00 <_data_lma+0x2dc4b2c>
     b5a:	bf7d                	j	b18 <SystemCoreClockUpdate+0x28>

00000b5c <Touch_Button_GPIO_Config>:
     b5c:	d44ff2ef          	jal	t0,a0 <__riscv_save_0>
     b60:	1151                	addi	sp,sp,-12
     b62:	4585                	li	a1,1
     b64:	02000513          	li	a0,32
     b68:	c002                	sw	zero,0(sp)
     b6a:	c202                	sw	zero,4(sp)
     b6c:	c402                	sw	zero,8(sp)
     b6e:	295d                	jal	1024 <RCC_APB2PeriphClockCmd>
     b70:	4785                	li	a5,1
     b72:	40011537          	lui	a0,0x40011
     b76:	807c                	sh	a5,0(sp)
     b78:	858a                	mv	a1,sp
     b7a:	04800793          	li	a5,72
     b7e:	40050513          	addi	a0,a0,1024 # 40011400 <__global_pointer$+0x20010bc0>
     b82:	c43e                	sw	a5,8(sp)
     b84:	2cf1                	jal	e60 <GPIO_Init>
     b86:	0131                	addi	sp,sp,12
     b88:	d22ff06f          	j	aa <__riscv_restore_0>

00000b8c <Touch_Button_Timer_Init>:
     b8c:	d14ff2ef          	jal	t0,a0 <__riscv_save_0>
     b90:	1131                	addi	sp,sp,-20
     b92:	4585                	li	a1,1
     b94:	4505                	li	a0,1
     b96:	c402                	sw	zero,8(sp)
     b98:	c602                	sw	zero,12(sp)
     b9a:	00011823          	sh	zero,16(sp)
     b9e:	c002                	sw	zero,0(sp)
     ba0:	c202                	sw	zero,4(sp)
     ba2:	2145                	jal	1042 <RCC_APB1PeriphClockCmd>
     ba4:	02f00793          	li	a5,47
     ba8:	c43e                	sw	a5,8(sp)
     baa:	002c                	addi	a1,sp,8
     bac:	3e700793          	li	a5,999
     bb0:	40000537          	lui	a0,0x40000
     bb4:	c63e                	sw	a5,12(sp)
     bb6:	216d                	jal	1060 <TIM_TimeBaseInit>
     bb8:	4605                	li	a2,1
     bba:	4585                	li	a1,1
     bbc:	40000537          	lui	a0,0x40000
     bc0:	2319                	jal	10c6 <TIM_ITConfig>
     bc2:	22600793          	li	a5,550
     bc6:	807c                	sh	a5,0(sp)
     bc8:	850a                	mv	a0,sp
     bca:	4785                	li	a5,1
     bcc:	c23e                	sw	a5,4(sp)
     bce:	00010123          	sb	zero,2(sp)
     bd2:	2e35                	jal	f0e <NVIC_Init>
     bd4:	4585                	li	a1,1
     bd6:	40000537          	lui	a0,0x40000
     bda:	29d1                	jal	10ae <TIM_Cmd>
     bdc:	0151                	addi	sp,sp,20
     bde:	cccff06f          	j	aa <__riscv_restore_0>

00000be2 <Touch_Button_EXTI_Config>:
     be2:	cbeff2ef          	jal	t0,a0 <__riscv_save_0>
     be6:	1121                	addi	sp,sp,-24
     be8:	4585                	li	a1,1
     bea:	4505                	li	a0,1
     bec:	c402                	sw	zero,8(sp)
     bee:	c602                	sw	zero,12(sp)
     bf0:	c802                	sw	zero,16(sp)
     bf2:	ca02                	sw	zero,20(sp)
     bf4:	c002                	sw	zero,0(sp)
     bf6:	c202                	sw	zero,4(sp)
     bf8:	2135                	jal	1024 <RCC_APB2PeriphClockCmd>
     bfa:	4581                	li	a1,0
     bfc:	450d                	li	a0,3
     bfe:	24e5                	jal	ee6 <GPIO_EXTILineConfig>
     c00:	4405                	li	s0,1
     c02:	47c1                	li	a5,16
     c04:	0028                	addi	a0,sp,8
     c06:	c422                	sw	s0,8(sp)
     c08:	c83e                	sw	a5,16(sp)
     c0a:	ca22                	sw	s0,20(sp)
     c0c:	c602                	sw	zero,12(sp)
     c0e:	22c1                	jal	dce <EXTI_Init>
     c10:	11400793          	li	a5,276
     c14:	850a                	mv	a0,sp
     c16:	807c                	sh	a5,0(sp)
     c18:	8140                	sb	s0,2(sp)
     c1a:	c222                	sw	s0,4(sp)
     c1c:	2ccd                	jal	f0e <NVIC_Init>
     c1e:	0161                	addi	sp,sp,24
     c20:	c8aff06f          	j	aa <__riscv_restore_0>

00000c24 <Touch_Button_Init>:
     c24:	c7cff2ef          	jal	t0,a0 <__riscv_save_0>
     c28:	97818793          	addi	a5,gp,-1672 # 200001b8 <touch_button>
     c2c:	00079823          	sh	zero,16(a5)
     c30:	0007a023          	sw	zero,0(a5)
     c34:	0007a223          	sw	zero,4(a5)
     c38:	0007a423          	sw	zero,8(a5)
     c3c:	0007a623          	sw	zero,12(a5)
     c40:	00078923          	sb	zero,18(a5)
     c44:	3f21                	jal	b5c <Touch_Button_GPIO_Config>
     c46:	3f71                	jal	be2 <Touch_Button_EXTI_Config>
     c48:	3791                	jal	b8c <Touch_Button_Timer_Init>
     c4a:	c60ff06f          	j	aa <__riscv_restore_0>

00000c4e <Touch_Button_Update>:
     c4e:	9741a683          	lw	a3,-1676(gp) # 200001b4 <system_tick_ms>
     c52:	97818793          	addi	a5,gp,-1672 # 200001b8 <touch_button>
     c56:	4790                	lw	a2,8(a5)
     c58:	438c                	lw	a1,0(a5)
     c5a:	4505                	li	a0,1
     c5c:	40c68633          	sub	a2,a3,a2
     c60:	97818713          	addi	a4,gp,-1672 # 200001b8 <touch_button>
     c64:	02a58663          	beq	a1,a0,c90 <Touch_Button_Update+0x42>
     c68:	c589                	beqz	a1,c72 <Touch_Button_Update+0x24>
     c6a:	478d                	li	a5,3
     c6c:	04f58063          	beq	a1,a5,cac <Touch_Button_Update+0x5e>
     c70:	8082                	ret
     c72:	3b98                	lbu	a4,17(a5)
     c74:	c729                	beqz	a4,cbe <Touch_Button_Update+0x70>
     c76:	2bb8                	lbu	a4,18(a5)
     c78:	e339                	bnez	a4,cbe <Touch_Button_Update+0x70>
     c7a:	47d8                	lw	a4,12(a5)
     c7c:	8e99                	sub	a3,a3,a4
     c7e:	7cf00713          	li	a4,1999
     c82:	02d77e63          	bgeu	a4,a3,cbe <Touch_Button_Update+0x70>
     c86:	470d                	li	a4,3
     c88:	000788a3          	sb	zero,17(a5)
     c8c:	c3d8                	sw	a4,4(a5)
     c8e:	8082                	ret
     c90:	3e700713          	li	a4,999
     c94:	02c77563          	bgeu	a4,a2,cbe <Touch_Button_Update+0x70>
     c98:	4709                	li	a4,2
     c9a:	c398                	sw	a4,0(a5)
     c9c:	c3d8                	sw	a4,4(a5)
     c9e:	2bb8                	lbu	a4,18(a5)
     ca0:	bb8c                	sb	a1,17(a5)
     ca2:	c7d4                	sw	a3,12(a5)
     ca4:	00173713          	seqz	a4,a4
     ca8:	abb8                	sb	a4,18(a5)
     caa:	8082                	ret
     cac:	3e700793          	li	a5,999
     cb0:	00c7e563          	bltu	a5,a2,cba <Touch_Button_Update+0x6c>
     cb4:	c348                	sw	a0,4(a4)
     cb6:	bb08                	sb	a0,17(a4)
     cb8:	c754                	sw	a3,12(a4)
     cba:	00072023          	sw	zero,0(a4) # 40021000 <__global_pointer$+0x200207c0>
     cbe:	8082                	ret

00000cc0 <Touch_Button_Get_Event>:
     cc0:	97818793          	addi	a5,gp,-1672 # 200001b8 <touch_button>
     cc4:	43c8                	lw	a0,4(a5)
     cc6:	0007a223          	sw	zero,4(a5)
     cca:	8082                	ret

00000ccc <Touch_Button_IRQ_Handler>:
     ccc:	bd4ff2ef          	jal	t0,a0 <__riscv_save_0>
     cd0:	4505                	li	a0,1
     cd2:	229d                	jal	e38 <EXTI_GetITStatus>
     cd4:	c505                	beqz	a0,cfc <Touch_Button_IRQ_Handler+0x30>
     cd6:	40011537          	lui	a0,0x40011
     cda:	4585                	li	a1,1
     cdc:	40050513          	addi	a0,a0,1024 # 40011400 <__global_pointer$+0x20010bc0>
     ce0:	9741a403          	lw	s0,-1676(gp) # 200001b4 <system_tick_ms>
     ce4:	2ae5                	jal	edc <GPIO_ReadInputDataBit>
     ce6:	97818793          	addi	a5,gp,-1672 # 200001b8 <touch_button>
     cea:	4398                	lw	a4,0(a5)
     cec:	c911                	beqz	a0,d00 <Touch_Button_IRQ_Handler+0x34>
     cee:	e709                	bnez	a4,cf8 <Touch_Button_IRQ_Handler+0x2c>
     cf0:	4705                	li	a4,1
     cf2:	c398                	sw	a4,0(a5)
     cf4:	c780                	sw	s0,8(a5)
     cf6:	ab98                	sb	a4,16(a5)
     cf8:	4505                	li	a0,1
     cfa:	2ab1                	jal	e56 <EXTI_ClearITPendingBit>
     cfc:	baeff06f          	j	aa <__riscv_restore_0>
     d00:	177d                	addi	a4,a4,-1
     d02:	4685                	li	a3,1
     d04:	fee6eae3          	bltu	a3,a4,cf8 <Touch_Button_IRQ_Handler+0x2c>
     d08:	470d                	li	a4,3
     d0a:	c398                	sw	a4,0(a5)
     d0c:	00078823          	sb	zero,16(a5)
     d10:	b7e5                	j	cf8 <Touch_Button_IRQ_Handler+0x2c>

00000d12 <ADC1_IRQHandler>:
     d12:	a001                	j	d12 <ADC1_IRQHandler>

00000d14 <handle_reset>:
     d14:	20000197          	auipc	gp,0x20000
     d18:	b2c18193          	addi	gp,gp,-1236 # 20000840 <__global_pointer$>
     d1c:	fc018113          	addi	sp,gp,-64 # 20000800 <_eusrstack>
     d20:	0a000513          	li	a0,160
     d24:	1ffff597          	auipc	a1,0x1ffff
     d28:	2dc58593          	addi	a1,a1,732 # 20000000 <_highcode_vma_end>
     d2c:	1ffff617          	auipc	a2,0x1ffff
     d30:	2d460613          	addi	a2,a2,724 # 20000000 <_highcode_vma_end>
     d34:	00c5fa63          	bgeu	a1,a2,d48 <handle_reset+0x34>
     d38:	00052283          	lw	t0,0(a0)
     d3c:	0055a023          	sw	t0,0(a1)
     d40:	0511                	addi	a0,a0,4
     d42:	0591                	addi	a1,a1,4
     d44:	fec5eae3          	bltu	a1,a2,d38 <handle_reset+0x24>
     d48:	00001517          	auipc	a0,0x1
     d4c:	38c50513          	addi	a0,a0,908 # 20d4 <_data_lma>
     d50:	1ffff597          	auipc	a1,0x1ffff
     d54:	2b058593          	addi	a1,a1,688 # 20000000 <_highcode_vma_end>
     d58:	1ffff617          	auipc	a2,0x1ffff
     d5c:	2e860613          	addi	a2,a2,744 # 20000040 <_edata>
     d60:	00c5fa63          	bgeu	a1,a2,d74 <handle_reset+0x60>
     d64:	00052283          	lw	t0,0(a0)
     d68:	0055a023          	sw	t0,0(a1)
     d6c:	0511                	addi	a0,a0,4
     d6e:	0591                	addi	a1,a1,4
     d70:	fec5eae3          	bltu	a1,a2,d64 <handle_reset+0x50>
     d74:	1ffff517          	auipc	a0,0x1ffff
     d78:	2cc50513          	addi	a0,a0,716 # 20000040 <_edata>
     d7c:	99418593          	addi	a1,gp,-1644 # 200001d4 <_ebss>
     d80:	00b57763          	bgeu	a0,a1,d8e <handle_reset+0x7a>
     d84:	00052023          	sw	zero,0(a0)
     d88:	0511                	addi	a0,a0,4
     d8a:	feb56de3          	bltu	a0,a1,d84 <handle_reset+0x70>
     d8e:	000022b7          	lui	t0,0x2
     d92:	88028293          	addi	t0,t0,-1920 # 1880 <print+0x338>
     d96:	30029073          	csrw	mstatus,t0
     d9a:	428d                	li	t0,3
     d9c:	80429073          	csrw	0x804,t0
     da0:	fffff297          	auipc	t0,0xfffff
     da4:	26028293          	addi	t0,t0,608 # 0 <_sinit>
     da8:	0032e293          	ori	t0,t0,3
     dac:	30529073          	csrw	mtvec,t0
     db0:	c0dff0ef          	jal	ra,9bc <SystemInit>
     db4:	fffff297          	auipc	t0,0xfffff
     db8:	62628293          	addi	t0,t0,1574 # 3da <main>
     dbc:	34129073          	csrw	mepc,t0
     dc0:	30200073          	mret

00000dc4 <DBGMCU_GetCHIPID>:
     dc4:	1ffff7b7          	lui	a5,0x1ffff
     dc8:	7c47a503          	lw	a0,1988(a5) # 1ffff7c4 <_data_lma+0x1fffd6f0>
     dcc:	8082                	ret

00000dce <EXTI_Init>:
     dce:	4158                	lw	a4,4(a0)
     dd0:	00052303          	lw	t1,0(a0)
     dd4:	454c                	lw	a1,12(a0)
     dd6:	40010637          	lui	a2,0x40010
     dda:	40060793          	addi	a5,a2,1024 # 40010400 <__global_pointer$+0x2000fbc0>
     dde:	973e                	add	a4,a4,a5
     de0:	fff34693          	not	a3,t1
     de4:	c5b1                	beqz	a1,e30 <EXTI_Init+0x62>
     de6:	40062583          	lw	a1,1024(a2)
     dea:	8df5                	and	a1,a1,a3
     dec:	40b62023          	sw	a1,1024(a2)
     df0:	43d0                	lw	a2,4(a5)
     df2:	8ef1                	and	a3,a3,a2
     df4:	c3d4                	sw	a3,4(a5)
     df6:	4314                	lw	a3,0(a4)
     df8:	0066e6b3          	or	a3,a3,t1
     dfc:	c314                	sw	a3,0(a4)
     dfe:	4118                	lw	a4,0(a0)
     e00:	4790                	lw	a2,8(a5)
     e02:	fff74693          	not	a3,a4
     e06:	8e75                	and	a2,a2,a3
     e08:	c790                	sw	a2,8(a5)
     e0a:	47d0                	lw	a2,12(a5)
     e0c:	8ef1                	and	a3,a3,a2
     e0e:	c7d4                	sw	a3,12(a5)
     e10:	4514                	lw	a3,8(a0)
     e12:	4641                	li	a2,16
     e14:	00c69963          	bne	a3,a2,e26 <EXTI_Init+0x58>
     e18:	4794                	lw	a3,8(a5)
     e1a:	8ed9                	or	a3,a3,a4
     e1c:	c794                	sw	a3,8(a5)
     e1e:	47d4                	lw	a3,12(a5)
     e20:	8f55                	or	a4,a4,a3
     e22:	c7d8                	sw	a4,12(a5)
     e24:	8082                	ret
     e26:	97b6                	add	a5,a5,a3
     e28:	4394                	lw	a3,0(a5)
     e2a:	8f55                	or	a4,a4,a3
     e2c:	c398                	sw	a4,0(a5)
     e2e:	8082                	ret
     e30:	431c                	lw	a5,0(a4)
     e32:	8ff5                	and	a5,a5,a3
     e34:	c31c                	sw	a5,0(a4)
     e36:	8082                	ret

00000e38 <EXTI_GetITStatus>:
     e38:	400107b7          	lui	a5,0x40010
     e3c:	40078713          	addi	a4,a5,1024 # 40010400 <__global_pointer$+0x2000fbc0>
     e40:	4007a783          	lw	a5,1024(a5)
     e44:	4b58                	lw	a4,20(a4)
     e46:	8f69                	and	a4,a4,a0
     e48:	c709                	beqz	a4,e52 <EXTI_GetITStatus+0x1a>
     e4a:	8d7d                	and	a0,a0,a5
     e4c:	00a03533          	snez	a0,a0
     e50:	8082                	ret
     e52:	4501                	li	a0,0
     e54:	8082                	ret

00000e56 <EXTI_ClearITPendingBit>:
     e56:	400107b7          	lui	a5,0x40010
     e5a:	40a7aa23          	sw	a0,1044(a5) # 40010414 <__global_pointer$+0x2000fbd4>
     e5e:	8082                	ret

00000e60 <GPIO_Init>:
     e60:	4594                	lw	a3,8(a1)
     e62:	0106f793          	andi	a5,a3,16
     e66:	00f6f293          	andi	t0,a3,15
     e6a:	c781                	beqz	a5,e72 <GPIO_Init+0x12>
     e6c:	41dc                	lw	a5,4(a1)
     e6e:	00f2e2b3          	or	t0,t0,a5
     e72:	0005d383          	lhu	t2,0(a1)
     e76:	0ff3f793          	andi	a5,t2,255
     e7a:	c3a5                	beqz	a5,eda <GPIO_Init+0x7a>
     e7c:	00052303          	lw	t1,0(a0)
     e80:	1161                	addi	sp,sp,-8
     e82:	c222                	sw	s0,4(sp)
     e84:	c026                	sw	s1,0(sp)
     e86:	4781                	li	a5,0
     e88:	02800413          	li	s0,40
     e8c:	04800493          	li	s1,72
     e90:	4705                	li	a4,1
     e92:	00f71633          	sll	a2,a4,a5
     e96:	00c3f733          	and	a4,t2,a2
     e9a:	02e61263          	bne	a2,a4,ebe <GPIO_Init+0x5e>
     e9e:	00279593          	slli	a1,a5,0x2
     ea2:	473d                	li	a4,15
     ea4:	00b71733          	sll	a4,a4,a1
     ea8:	fff74713          	not	a4,a4
     eac:	00677333          	and	t1,a4,t1
     eb0:	00b295b3          	sll	a1,t0,a1
     eb4:	0065e333          	or	t1,a1,t1
     eb8:	00869d63          	bne	a3,s0,ed2 <GPIO_Init+0x72>
     ebc:	c950                	sw	a2,20(a0)
     ebe:	0785                	addi	a5,a5,1
     ec0:	4721                	li	a4,8
     ec2:	fce797e3          	bne	a5,a4,e90 <GPIO_Init+0x30>
     ec6:	4412                	lw	s0,4(sp)
     ec8:	00652023          	sw	t1,0(a0)
     ecc:	4482                	lw	s1,0(sp)
     ece:	0121                	addi	sp,sp,8
     ed0:	8082                	ret
     ed2:	fe9696e3          	bne	a3,s1,ebe <GPIO_Init+0x5e>
     ed6:	c910                	sw	a2,16(a0)
     ed8:	b7dd                	j	ebe <GPIO_Init+0x5e>
     eda:	8082                	ret

00000edc <GPIO_ReadInputDataBit>:
     edc:	4508                	lw	a0,8(a0)
     ede:	8d6d                	and	a0,a0,a1
     ee0:	00a03533          	snez	a0,a0
     ee4:	8082                	ret

00000ee6 <GPIO_EXTILineConfig>:
     ee6:	40010737          	lui	a4,0x40010
     eea:	4714                	lw	a3,8(a4)
     eec:	0586                	slli	a1,a1,0x1
     eee:	478d                	li	a5,3
     ef0:	00b797b3          	sll	a5,a5,a1
     ef4:	fff7c793          	not	a5,a5
     ef8:	8ff5                	and	a5,a5,a3
     efa:	c71c                	sw	a5,8(a4)
     efc:	471c                	lw	a5,8(a4)
     efe:	00b515b3          	sll	a1,a0,a1
     f02:	8ddd                	or	a1,a1,a5
     f04:	c70c                	sw	a1,8(a4)
     f06:	8082                	ret

00000f08 <NVIC_PriorityGroupConfig>:
     f08:	98a1a623          	sw	a0,-1652(gp) # 200001cc <NVIC_Priority_Group>
     f0c:	8082                	ret

00000f0e <NVIC_Init>:
     f0e:	98c1a683          	lw	a3,-1652(gp) # 200001cc <NVIC_Priority_Group>
     f12:	4785                	li	a5,1
     f14:	2118                	lbu	a4,0(a0)
     f16:	02f69063          	bne	a3,a5,f36 <NVIC_Init+0x28>
     f1a:	311c                	lbu	a5,1(a0)
     f1c:	02d79c63          	bne	a5,a3,f54 <NVIC_Init+0x46>
     f20:	213c                	lbu	a5,2(a0)
     f22:	079a                	slli	a5,a5,0x6
     f24:	f807e793          	ori	a5,a5,-128
     f28:	e000e6b7          	lui	a3,0xe000e
     f2c:	0ff7f793          	andi	a5,a5,255
     f30:	96ba                	add	a3,a3,a4
     f32:	40f68023          	sb	a5,1024(a3) # e000e400 <__global_pointer$+0xc000dbc0>
     f36:	4685                	li	a3,1
     f38:	00575793          	srli	a5,a4,0x5
     f3c:	00e69733          	sll	a4,a3,a4
     f40:	4154                	lw	a3,4(a0)
     f42:	ce89                	beqz	a3,f5c <NVIC_Init+0x4e>
     f44:	04078793          	addi	a5,a5,64
     f48:	078a                	slli	a5,a5,0x2
     f4a:	e000e6b7          	lui	a3,0xe000e
     f4e:	97b6                	add	a5,a5,a3
     f50:	c398                	sw	a4,0(a5)
     f52:	8082                	ret
     f54:	f3ed                	bnez	a5,f36 <NVIC_Init+0x28>
     f56:	213c                	lbu	a5,2(a0)
     f58:	079a                	slli	a5,a5,0x6
     f5a:	b7f9                	j	f28 <NVIC_Init+0x1a>
     f5c:	06078793          	addi	a5,a5,96
     f60:	e000e6b7          	lui	a3,0xe000e
     f64:	078a                	slli	a5,a5,0x2
     f66:	97b6                	add	a5,a5,a3
     f68:	c398                	sw	a4,0(a5)
     f6a:	0000100f          	fence.i
     f6e:	8082                	ret

00000f70 <RCC_AdjustHSICalibrationValue>:
     f70:	40021737          	lui	a4,0x40021
     f74:	431c                	lw	a5,0(a4)
     f76:	050e                	slli	a0,a0,0x3
     f78:	f077f793          	andi	a5,a5,-249
     f7c:	8d5d                	or	a0,a0,a5
     f7e:	c308                	sw	a0,0(a4)
     f80:	8082                	ret

00000f82 <RCC_GetClocksFreq>:
     f82:	91eff2ef          	jal	t0,a0 <__riscv_save_0>
     f86:	40021737          	lui	a4,0x40021
     f8a:	435c                	lw	a5,4(a4)
     f8c:	4691                	li	a3,4
     f8e:	842a                	mv	s0,a0
     f90:	8bb1                	andi	a5,a5,12
     f92:	00d78563          	beq	a5,a3,f9c <RCC_GetClocksFreq+0x1a>
     f96:	46a1                	li	a3,8
     f98:	08d78063          	beq	a5,a3,1018 <RCC_GetClocksFreq+0x96>
     f9c:	016e37b7          	lui	a5,0x16e3
     fa0:	60078793          	addi	a5,a5,1536 # 16e3600 <_data_lma+0x16e152c>
     fa4:	c01c                	sw	a5,0(s0)
     fa6:	400216b7          	lui	a3,0x40021
     faa:	42dc                	lw	a5,4(a3)
     fac:	8391                	srli	a5,a5,0x4
     fae:	00f7f713          	andi	a4,a5,15
     fb2:	200007b7          	lui	a5,0x20000
     fb6:	02c78793          	addi	a5,a5,44 # 2000002c <APBAHBPrescTable>
     fba:	97ba                	add	a5,a5,a4
     fbc:	238c                	lbu	a1,0(a5)
     fbe:	42dc                	lw	a5,4(a3)
     fc0:	4018                	lw	a4,0(s0)
     fc2:	0ff5f593          	andi	a1,a1,255
     fc6:	0807f793          	andi	a5,a5,128
     fca:	00b75533          	srl	a0,a4,a1
     fce:	e781                	bnez	a5,fd6 <RCC_GetClocksFreq+0x54>
     fd0:	853a                	mv	a0,a4
     fd2:	902ff0ef          	jal	ra,d4 <__udivsi3>
     fd6:	c048                	sw	a0,4(s0)
     fd8:	c408                	sw	a0,8(s0)
     fda:	c448                	sw	a0,12(s0)
     fdc:	400217b7          	lui	a5,0x40021
     fe0:	43dc                	lw	a5,4(a5)
     fe2:	468d                	li	a3,3
     fe4:	83ad                	srli	a5,a5,0xb
     fe6:	8bfd                	andi	a5,a5,31
     fe8:	0037d713          	srli	a4,a5,0x3
     fec:	078a                	slli	a5,a5,0x2
     fee:	8bf1                	andi	a5,a5,28
     ff0:	8fd9                	or	a5,a5,a4
     ff2:	0137f613          	andi	a2,a5,19
     ff6:	0037f713          	andi	a4,a5,3
     ffa:	00c6f463          	bgeu	a3,a2,1002 <RCC_GetClocksFreq+0x80>
     ffe:	ff478713          	addi	a4,a5,-12 # 40020ff4 <__global_pointer$+0x200207b4>
    1002:	200007b7          	lui	a5,0x20000
    1006:	01878793          	addi	a5,a5,24 # 20000018 <ADCPrescTable>
    100a:	97ba                	add	a5,a5,a4
    100c:	238c                	lbu	a1,0(a5)
    100e:	8c6ff0ef          	jal	ra,d4 <__udivsi3>
    1012:	c808                	sw	a0,16(s0)
    1014:	896ff06f          	j	aa <__riscv_restore_0>
    1018:	435c                	lw	a5,4(a4)
    101a:	02dc77b7          	lui	a5,0x2dc7
    101e:	c0078793          	addi	a5,a5,-1024 # 2dc6c00 <_data_lma+0x2dc4b2c>
    1022:	b749                	j	fa4 <RCC_GetClocksFreq+0x22>

00001024 <RCC_APB2PeriphClockCmd>:
    1024:	c599                	beqz	a1,1032 <RCC_APB2PeriphClockCmd+0xe>
    1026:	40021737          	lui	a4,0x40021
    102a:	4f1c                	lw	a5,24(a4)
    102c:	8d5d                	or	a0,a0,a5
    102e:	cf08                	sw	a0,24(a4)
    1030:	8082                	ret
    1032:	400217b7          	lui	a5,0x40021
    1036:	4f98                	lw	a4,24(a5)
    1038:	fff54513          	not	a0,a0
    103c:	8d79                	and	a0,a0,a4
    103e:	cf88                	sw	a0,24(a5)
    1040:	8082                	ret

00001042 <RCC_APB1PeriphClockCmd>:
    1042:	c599                	beqz	a1,1050 <RCC_APB1PeriphClockCmd+0xe>
    1044:	40021737          	lui	a4,0x40021
    1048:	4f5c                	lw	a5,28(a4)
    104a:	8d5d                	or	a0,a0,a5
    104c:	cf48                	sw	a0,28(a4)
    104e:	8082                	ret
    1050:	400217b7          	lui	a5,0x40021
    1054:	4fd8                	lw	a4,28(a5)
    1056:	fff54513          	not	a0,a0
    105a:	8d79                	and	a0,a0,a4
    105c:	cfc8                	sw	a0,28(a5)
    105e:	8082                	ret

00001060 <TIM_TimeBaseInit>:
    1060:	211e                	lhu	a5,0(a0)
    1062:	40013737          	lui	a4,0x40013
    1066:	c0070713          	addi	a4,a4,-1024 # 40012c00 <__global_pointer$+0x200123c0>
    106a:	07c2                	slli	a5,a5,0x10
    106c:	83c1                	srli	a5,a5,0x10
    106e:	00e50663          	beq	a0,a4,107a <TIM_TimeBaseInit+0x1a>
    1072:	40000737          	lui	a4,0x40000
    1076:	00e51663          	bne	a0,a4,1082 <TIM_TimeBaseInit+0x22>
    107a:	21ba                	lhu	a4,2(a1)
    107c:	f8f7f793          	andi	a5,a5,-113
    1080:	8fd9                	or	a5,a5,a4
    1082:	21fa                	lhu	a4,6(a1)
    1084:	cff7f793          	andi	a5,a5,-769
    1088:	07c2                	slli	a5,a5,0x10
    108a:	83c1                	srli	a5,a5,0x10
    108c:	8fd9                	or	a5,a5,a4
    108e:	a11e                	sh	a5,0(a0)
    1090:	21de                	lhu	a5,4(a1)
    1092:	b55e                	sh	a5,44(a0)
    1094:	219e                	lhu	a5,0(a1)
    1096:	b51e                	sh	a5,40(a0)
    1098:	400137b7          	lui	a5,0x40013
    109c:	c0078793          	addi	a5,a5,-1024 # 40012c00 <__global_pointer$+0x200123c0>
    10a0:	00f51463          	bne	a0,a5,10a8 <TIM_TimeBaseInit+0x48>
    10a4:	259c                	lbu	a5,8(a1)
    10a6:	b91e                	sh	a5,48(a0)
    10a8:	4785                	li	a5,1
    10aa:	a95e                	sh	a5,20(a0)
    10ac:	8082                	ret

000010ae <TIM_Cmd>:
    10ae:	211e                	lhu	a5,0(a0)
    10b0:	c589                	beqz	a1,10ba <TIM_Cmd+0xc>
    10b2:	0017e793          	ori	a5,a5,1
    10b6:	a11e                	sh	a5,0(a0)
    10b8:	8082                	ret
    10ba:	07c2                	slli	a5,a5,0x10
    10bc:	83c1                	srli	a5,a5,0x10
    10be:	9bf9                	andi	a5,a5,-2
    10c0:	07c2                	slli	a5,a5,0x10
    10c2:	83c1                	srli	a5,a5,0x10
    10c4:	bfcd                	j	10b6 <TIM_Cmd+0x8>

000010c6 <TIM_ITConfig>:
    10c6:	255e                	lhu	a5,12(a0)
    10c8:	c601                	beqz	a2,10d0 <TIM_ITConfig+0xa>
    10ca:	8ddd                	or	a1,a1,a5
    10cc:	a54e                	sh	a1,12(a0)
    10ce:	8082                	ret
    10d0:	fff5c593          	not	a1,a1
    10d4:	8dfd                	and	a1,a1,a5
    10d6:	bfdd                	j	10cc <TIM_ITConfig+0x6>

000010d8 <TIM_SetCompare1>:
    10d8:	d94c                	sw	a1,52(a0)
    10da:	8082                	ret

000010dc <TIM_GetITStatus>:
    10dc:	291e                	lhu	a5,16(a0)
    10de:	254a                	lhu	a0,12(a0)
    10e0:	8fed                	and	a5,a5,a1
    10e2:	0542                	slli	a0,a0,0x10
    10e4:	8141                	srli	a0,a0,0x10
    10e6:	c789                	beqz	a5,10f0 <TIM_GetITStatus+0x14>
    10e8:	8d6d                	and	a0,a0,a1
    10ea:	00a03533          	snez	a0,a0
    10ee:	8082                	ret
    10f0:	4501                	li	a0,0
    10f2:	8082                	ret

000010f4 <TIM_ClearITPendingBit>:
    10f4:	fff5c593          	not	a1,a1
    10f8:	05c2                	slli	a1,a1,0x10
    10fa:	81c1                	srli	a1,a1,0x10
    10fc:	a90e                	sh	a1,16(a0)
    10fe:	8082                	ret

00001100 <USART_Init>:
    1100:	fa1fe2ef          	jal	t0,a0 <__riscv_save_0>
    1104:	2916                	lhu	a3,16(a0)
    1106:	77f5                	lui	a5,0xffffd
    1108:	17fd                	addi	a5,a5,-1
    110a:	8ff5                	and	a5,a5,a3
    110c:	21f6                	lhu	a3,6(a1)
    110e:	25da                	lhu	a4,12(a1)
    1110:	1121                	addi	sp,sp,-24
    1112:	8fd5                	or	a5,a5,a3
    1114:	a91e                	sh	a5,16(a0)
    1116:	2556                	lhu	a3,12(a0)
    1118:	77fd                	lui	a5,0xfffff
    111a:	9f378793          	addi	a5,a5,-1549 # ffffe9f3 <__global_pointer$+0xdfffe1b3>
    111e:	8ff5                	and	a5,a5,a3
    1120:	21d6                	lhu	a3,4(a1)
    1122:	842a                	mv	s0,a0
    1124:	c02e                	sw	a1,0(sp)
    1126:	8fd5                	or	a5,a5,a3
    1128:	2596                	lhu	a3,8(a1)
    112a:	8fd5                	or	a5,a5,a3
    112c:	25b6                	lhu	a3,10(a1)
    112e:	8fd5                	or	a5,a5,a3
    1130:	a55e                	sh	a5,12(a0)
    1132:	295e                	lhu	a5,20(a0)
    1134:	07c2                	slli	a5,a5,0x10
    1136:	83c1                	srli	a5,a5,0x10
    1138:	cff7f793          	andi	a5,a5,-769
    113c:	8f5d                	or	a4,a4,a5
    113e:	a95a                	sh	a4,20(a0)
    1140:	0048                	addi	a0,sp,4
    1142:	3581                	jal	f82 <RCC_GetClocksFreq>
    1144:	400147b7          	lui	a5,0x40014
    1148:	80078793          	addi	a5,a5,-2048 # 40013800 <__global_pointer$+0x20012fc0>
    114c:	4582                	lw	a1,0(sp)
    114e:	06f41263          	bne	s0,a5,11b2 <USART_Init+0xb2>
    1152:	47c2                	lw	a5,16(sp)
    1154:	245a                	lhu	a4,12(s0)
    1156:	00179513          	slli	a0,a5,0x1
    115a:	953e                	add	a0,a0,a5
    115c:	0742                	slli	a4,a4,0x10
    115e:	050e                	slli	a0,a0,0x3
    1160:	8741                	srai	a4,a4,0x10
    1162:	953e                	add	a0,a0,a5
    1164:	418c                	lw	a1,0(a1)
    1166:	04075863          	bgez	a4,11b6 <USART_Init+0xb6>
    116a:	0586                	slli	a1,a1,0x1
    116c:	f69fe0ef          	jal	ra,d4 <__udivsi3>
    1170:	06400593          	li	a1,100
    1174:	c02a                	sw	a0,0(sp)
    1176:	f5ffe0ef          	jal	ra,d4 <__udivsi3>
    117a:	4782                	lw	a5,0(sp)
    117c:	00451493          	slli	s1,a0,0x4
    1180:	06400593          	li	a1,100
    1184:	853e                	mv	a0,a5
    1186:	f7bfe0ef          	jal	ra,100 <__umodsi3>
    118a:	245e                	lhu	a5,12(s0)
    118c:	07c2                	slli	a5,a5,0x10
    118e:	87c1                	srai	a5,a5,0x10
    1190:	0207d563          	bgez	a5,11ba <USART_Init+0xba>
    1194:	050e                	slli	a0,a0,0x3
    1196:	06400593          	li	a1,100
    119a:	03250513          	addi	a0,a0,50
    119e:	f37fe0ef          	jal	ra,d4 <__udivsi3>
    11a2:	891d                	andi	a0,a0,7
    11a4:	8cc9                	or	s1,s1,a0
    11a6:	04c2                	slli	s1,s1,0x10
    11a8:	80c1                	srli	s1,s1,0x10
    11aa:	a406                	sh	s1,8(s0)
    11ac:	0161                	addi	sp,sp,24
    11ae:	efdfe06f          	j	aa <__riscv_restore_0>
    11b2:	47b2                	lw	a5,12(sp)
    11b4:	b745                	j	1154 <USART_Init+0x54>
    11b6:	058a                	slli	a1,a1,0x2
    11b8:	bf55                	j	116c <USART_Init+0x6c>
    11ba:	0512                	slli	a0,a0,0x4
    11bc:	06400593          	li	a1,100
    11c0:	03250513          	addi	a0,a0,50
    11c4:	f11fe0ef          	jal	ra,d4 <__udivsi3>
    11c8:	893d                	andi	a0,a0,15
    11ca:	bfe9                	j	11a4 <USART_Init+0xa4>

000011cc <USART_Cmd>:
    11cc:	c591                	beqz	a1,11d8 <USART_Cmd+0xc>
    11ce:	255e                	lhu	a5,12(a0)
    11d0:	6709                	lui	a4,0x2
    11d2:	8fd9                	or	a5,a5,a4
    11d4:	a55e                	sh	a5,12(a0)
    11d6:	8082                	ret
    11d8:	255a                	lhu	a4,12(a0)
    11da:	77f9                	lui	a5,0xffffe
    11dc:	17fd                	addi	a5,a5,-1
    11de:	8ff9                	and	a5,a5,a4
    11e0:	bfd5                	j	11d4 <USART_Cmd+0x8>

000011e2 <USART_SendData>:
    11e2:	1ff5f593          	andi	a1,a1,511
    11e6:	a14e                	sh	a1,4(a0)
    11e8:	8082                	ret

000011ea <USART_GetFlagStatus>:
    11ea:	210a                	lhu	a0,0(a0)
    11ec:	8d6d                	and	a0,a0,a1
    11ee:	00a03533          	snez	a0,a0
    11f2:	8082                	ret

000011f4 <Delay_Init>:
    11f4:	eadfe2ef          	jal	t0,a0 <__riscv_save_0>
    11f8:	200007b7          	lui	a5,0x20000
    11fc:	0147a503          	lw	a0,20(a5) # 20000014 <SystemCoreClock>
    1200:	007a15b7          	lui	a1,0x7a1
    1204:	20058593          	addi	a1,a1,512 # 7a1200 <_data_lma+0x79f12c>
    1208:	ecdfe0ef          	jal	ra,d4 <__udivsi3>
    120c:	0ff57513          	andi	a0,a0,255
    1210:	98a18923          	sb	a0,-1646(gp) # 200001d2 <p_us>
    1214:	00551793          	slli	a5,a0,0x5
    1218:	8f89                	sub	a5,a5,a0
    121a:	078a                	slli	a5,a5,0x2
    121c:	953e                	add	a0,a0,a5
    121e:	050e                	slli	a0,a0,0x3
    1220:	98a19823          	sh	a0,-1648(gp) # 200001d0 <p_ms>
    1224:	e87fe06f          	j	aa <__riscv_restore_0>

00001228 <Delay_Ms>:
    1228:	e79fe2ef          	jal	t0,a0 <__riscv_save_0>
    122c:	e000f437          	lui	s0,0xe000f
    1230:	405c                	lw	a5,4(s0)
    1232:	85aa                	mv	a1,a0
    1234:	9bf9                	andi	a5,a5,-2
    1236:	c05c                	sw	a5,4(s0)
    1238:	9901d503          	lhu	a0,-1648(gp) # 200001d0 <p_ms>
    123c:	e79fe0ef          	jal	ra,b4 <__mulsi3>
    1240:	c808                	sw	a0,16(s0)
    1242:	00042423          	sw	zero,8(s0) # e000f008 <__global_pointer$+0xc000e7c8>
    1246:	401c                	lw	a5,0(s0)
    1248:	0017e793          	ori	a5,a5,1
    124c:	c01c                	sw	a5,0(s0)
    124e:	e000f7b7          	lui	a5,0xe000f
    1252:	43d8                	lw	a4,4(a5)
    1254:	8b05                	andi	a4,a4,1
    1256:	df75                	beqz	a4,1252 <Delay_Ms+0x2a>
    1258:	4398                	lw	a4,0(a5)
    125a:	9b79                	andi	a4,a4,-2
    125c:	c398                	sw	a4,0(a5)
    125e:	e4dfe06f          	j	aa <__riscv_restore_0>

00001262 <USART_Printf_Init>:
    1262:	e3ffe2ef          	jal	t0,a0 <__riscv_save_0>
    1266:	842a                	mv	s0,a0
    1268:	6511                	lui	a0,0x4
    126a:	1111                	addi	sp,sp,-28
    126c:	4585                	li	a1,1
    126e:	02050513          	addi	a0,a0,32 # 4020 <_data_lma+0x1f4c>
    1272:	3b4d                	jal	1024 <RCC_APB2PeriphClockCmd>
    1274:	02000793          	li	a5,32
    1278:	807c                	sh	a5,0(sp)
    127a:	40011537          	lui	a0,0x40011
    127e:	478d                	li	a5,3
    1280:	c23e                	sw	a5,4(sp)
    1282:	858a                	mv	a1,sp
    1284:	47e1                	li	a5,24
    1286:	40050513          	addi	a0,a0,1024 # 40011400 <__global_pointer$+0x20010bc0>
    128a:	c43e                	sw	a5,8(sp)
    128c:	bd5ff0ef          	jal	ra,e60 <GPIO_Init>
    1290:	c622                	sw	s0,12(sp)
    1292:	40014437          	lui	s0,0x40014
    1296:	000807b7          	lui	a5,0x80
    129a:	006c                	addi	a1,sp,12
    129c:	80040513          	addi	a0,s0,-2048 # 40013800 <__global_pointer$+0x20012fc0>
    12a0:	ca3e                	sw	a5,20(sp)
    12a2:	c802                	sw	zero,16(sp)
    12a4:	00011c23          	sh	zero,24(sp)
    12a8:	3da1                	jal	1100 <USART_Init>
    12aa:	4585                	li	a1,1
    12ac:	80040513          	addi	a0,s0,-2048
    12b0:	3f31                	jal	11cc <USART_Cmd>
    12b2:	0171                	addi	sp,sp,28
    12b4:	df7fe06f          	j	aa <__riscv_restore_0>

000012b8 <_write>:
    12b8:	de9fe2ef          	jal	t0,a0 <__riscv_save_0>
    12bc:	1171                	addi	sp,sp,-4
    12be:	84ae                	mv	s1,a1
    12c0:	4401                	li	s0,0
    12c2:	02c45d63          	bge	s0,a2,12fc <_write+0x44>
    12c6:	400147b7          	lui	a5,0x40014
    12ca:	80078713          	addi	a4,a5,-2048 # 40013800 <__global_pointer$+0x20012fc0>
    12ce:	853a                	mv	a0,a4
    12d0:	04000593          	li	a1,64
    12d4:	c032                	sw	a2,0(sp)
    12d6:	3f11                	jal	11ea <USART_GetFlagStatus>
    12d8:	400147b7          	lui	a5,0x40014
    12dc:	80078713          	addi	a4,a5,-2048 # 40013800 <__global_pointer$+0x20012fc0>
    12e0:	4602                	lw	a2,0(sp)
    12e2:	d575                	beqz	a0,12ce <_write+0x16>
    12e4:	00848733          	add	a4,s1,s0
    12e8:	00070583          	lb	a1,0(a4) # 2000 <font+0x434>
    12ec:	80078513          	addi	a0,a5,-2048
    12f0:	0405                	addi	s0,s0,1
    12f2:	05c2                	slli	a1,a1,0x10
    12f4:	81c1                	srli	a1,a1,0x10
    12f6:	35f5                	jal	11e2 <USART_SendData>
    12f8:	4602                	lw	a2,0(sp)
    12fa:	b7e1                	j	12c2 <_write+0xa>
    12fc:	8532                	mv	a0,a2
    12fe:	0111                	addi	sp,sp,4
    1300:	dabfe06f          	j	aa <__riscv_restore_0>

00001304 <printchar>:
    1304:	1141                	addi	sp,sp,-16
    1306:	c606                	sw	ra,12(sp)
    1308:	c02e                	sw	a1,0(sp)
    130a:	cd0d                	beqz	a0,1344 <printchar+0x40>
    130c:	4118                	lw	a4,0(a0)
    130e:	87aa                	mv	a5,a0
    1310:	c305                	beqz	a4,1330 <printchar+0x2c>
    1312:	4158                	lw	a4,4(a0)
    1314:	557d                	li	a0,-1
    1316:	cb11                	beqz	a4,132a <printchar+0x26>
    1318:	4685                	li	a3,1
    131a:	00d71b63          	bne	a4,a3,1330 <printchar+0x2c>
    131e:	4798                	lw	a4,8(a5)
    1320:	00070023          	sb	zero,0(a4)
    1324:	0007a223          	sw	zero,4(a5)
    1328:	4505                	li	a0,1
    132a:	40b2                	lw	ra,12(sp)
    132c:	0141                	addi	sp,sp,16
    132e:	8082                	ret
    1330:	4798                	lw	a4,8(a5)
    1332:	4682                	lw	a3,0(sp)
    1334:	a314                	sb	a3,0(a4)
    1336:	4798                	lw	a4,8(a5)
    1338:	0705                	addi	a4,a4,1
    133a:	c798                	sw	a4,8(a5)
    133c:	43d8                	lw	a4,4(a5)
    133e:	177d                	addi	a4,a4,-1
    1340:	c3d8                	sw	a4,4(a5)
    1342:	b7dd                	j	1328 <printchar+0x24>
    1344:	4605                	li	a2,1
    1346:	858a                	mv	a1,sp
    1348:	3f85                	jal	12b8 <_write>
    134a:	bff9                	j	1328 <printchar+0x24>

0000134c <prints>:
    134c:	1101                	addi	sp,sp,-32
    134e:	cc22                	sw	s0,24(sp)
    1350:	c22e                	sw	a1,4(sp)
    1352:	ce06                	sw	ra,28(sp)
    1354:	ca26                	sw	s1,20(sp)
    1356:	842a                	mv	s0,a0
    1358:	4781                	li	a5,0
    135a:	02000593          	li	a1,32
    135e:	02064563          	bltz	a2,1388 <prints+0x3c>
    1362:	4592                	lw	a1,4(sp)
    1364:	95be                	add	a1,a1,a5
    1366:	00058583          	lb	a1,0(a1)
    136a:	e58d                	bnez	a1,1394 <prints+0x48>
    136c:	02c7d863          	bge	a5,a2,139c <prints+0x50>
    1370:	02e7d463          	bge	a5,a4,1398 <prints+0x4c>
    1374:	8e19                	sub	a2,a2,a4
    1376:	02000513          	li	a0,32
    137a:	0026f593          	andi	a1,a3,2
    137e:	c02a                	sw	a0,0(sp)
    1380:	c589                	beqz	a1,138a <prints+0x3e>
    1382:	e701                	bnez	a4,138a <prints+0x3e>
    1384:	03000593          	li	a1,48
    1388:	c02e                	sw	a1,0(sp)
    138a:	8a85                	andi	a3,a3,1
    138c:	4481                	li	s1,0
    138e:	ea95                	bnez	a3,13c2 <prints+0x76>
    1390:	84b2                	mv	s1,a2
    1392:	a00d                	j	13b4 <prints+0x68>
    1394:	0785                	addi	a5,a5,1
    1396:	b7f1                	j	1362 <prints+0x16>
    1398:	8e1d                	sub	a2,a2,a5
    139a:	bff1                	j	1376 <prints+0x2a>
    139c:	4601                	li	a2,0
    139e:	bfe1                	j	1376 <prints+0x2a>
    13a0:	4582                	lw	a1,0(sp)
    13a2:	8522                	mv	a0,s0
    13a4:	c83a                	sw	a4,16(sp)
    13a6:	c632                	sw	a2,12(sp)
    13a8:	c43e                	sw	a5,8(sp)
    13aa:	3fa9                	jal	1304 <printchar>
    13ac:	47a2                	lw	a5,8(sp)
    13ae:	4632                	lw	a2,12(sp)
    13b0:	4742                	lw	a4,16(sp)
    13b2:	14fd                	addi	s1,s1,-1
    13b4:	fe9046e3          	bgtz	s1,13a0 <prints+0x54>
    13b8:	84b2                	mv	s1,a2
    13ba:	00065363          	bgez	a2,13c0 <prints+0x74>
    13be:	4481                	li	s1,0
    13c0:	8e05                	sub	a2,a2,s1
    13c2:	02e7c763          	blt	a5,a4,13f0 <prints+0xa4>
    13c6:	87a6                	mv	a5,s1
    13c8:	4692                	lw	a3,4(sp)
    13ca:	40978733          	sub	a4,a5,s1
    13ce:	9736                	add	a4,a4,a3
    13d0:	00070583          	lb	a1,0(a4)
    13d4:	ed95                	bnez	a1,1410 <prints+0xc4>
    13d6:	84b2                	mv	s1,a2
    13d8:	04904463          	bgtz	s1,1420 <prints+0xd4>
    13dc:	00065363          	bgez	a2,13e2 <prints+0x96>
    13e0:	4601                	li	a2,0
    13e2:	40f2                	lw	ra,28(sp)
    13e4:	4462                	lw	s0,24(sp)
    13e6:	44d2                	lw	s1,20(sp)
    13e8:	00f60533          	add	a0,a2,a5
    13ec:	6105                	addi	sp,sp,32
    13ee:	8082                	ret
    13f0:	8f1d                	sub	a4,a4,a5
    13f2:	87ba                	mv	a5,a4
    13f4:	03000593          	li	a1,48
    13f8:	8522                	mv	a0,s0
    13fa:	c832                	sw	a2,16(sp)
    13fc:	c63e                	sw	a5,12(sp)
    13fe:	c43a                	sw	a4,8(sp)
    1400:	3711                	jal	1304 <printchar>
    1402:	47b2                	lw	a5,12(sp)
    1404:	4722                	lw	a4,8(sp)
    1406:	4642                	lw	a2,16(sp)
    1408:	17fd                	addi	a5,a5,-1
    140a:	f7ed                	bnez	a5,13f4 <prints+0xa8>
    140c:	94ba                	add	s1,s1,a4
    140e:	bf65                	j	13c6 <prints+0x7a>
    1410:	8522                	mv	a0,s0
    1412:	c632                	sw	a2,12(sp)
    1414:	c43e                	sw	a5,8(sp)
    1416:	35fd                	jal	1304 <printchar>
    1418:	47a2                	lw	a5,8(sp)
    141a:	4632                	lw	a2,12(sp)
    141c:	0785                	addi	a5,a5,1
    141e:	b76d                	j	13c8 <prints+0x7c>
    1420:	4582                	lw	a1,0(sp)
    1422:	8522                	mv	a0,s0
    1424:	c432                	sw	a2,8(sp)
    1426:	c23e                	sw	a5,4(sp)
    1428:	3df1                	jal	1304 <printchar>
    142a:	14fd                	addi	s1,s1,-1
    142c:	4622                	lw	a2,8(sp)
    142e:	4792                	lw	a5,4(sp)
    1430:	b765                	j	13d8 <prints+0x8c>

00001432 <printInt>:
    1432:	7139                	addi	sp,sp,-64
    1434:	de06                	sw	ra,60(sp)
    1436:	dc22                	sw	s0,56(sp)
    1438:	da26                	sw	s1,52(sp)
    143a:	c23e                	sw	a5,4(sp)
    143c:	8332                	mv	t1,a2
    143e:	863a                	mv	a2,a4
    1440:	ed89                	bnez	a1,145a <printInt+0x28>
    1442:	4692                	lw	a3,4(sp)
    1444:	03000793          	li	a5,48
    1448:	4701                	li	a4,0
    144a:	086c                	addi	a1,sp,28
    144c:	86fc                	sh	a5,28(sp)
    144e:	3dfd                	jal	134c <prints>
    1450:	50f2                	lw	ra,60(sp)
    1452:	5462                	lw	s0,56(sp)
    1454:	54d2                	lw	s1,52(sp)
    1456:	6121                	addi	sp,sp,64
    1458:	8082                	ret
    145a:	84aa                	mv	s1,a0
    145c:	8436                	mv	s0,a3
    145e:	87ae                	mv	a5,a1
    1460:	ca91                	beqz	a3,1474 <printInt+0x42>
    1462:	4729                	li	a4,10
    1464:	4401                	li	s0,0
    1466:	00e31763          	bne	t1,a4,1474 <printInt+0x42>
    146a:	0005d563          	bgez	a1,1474 <printInt+0x42>
    146e:	40b007b3          	neg	a5,a1
    1472:	4405                	li	s0,1
    1474:	4686                	lw	a3,64(sp)
    1476:	020109a3          	sb	zero,51(sp)
    147a:	03310713          	addi	a4,sp,51
    147e:	fc668693          	addi	a3,a3,-58 # 40020fc6 <__global_pointer$+0x20020786>
    1482:	c436                	sw	a3,8(sp)
    1484:	859a                	mv	a1,t1
    1486:	853e                	mv	a0,a5
    1488:	ca32                	sw	a2,20(sp)
    148a:	c83a                	sw	a4,16(sp)
    148c:	c61a                	sw	t1,12(sp)
    148e:	c03e                	sw	a5,0(sp)
    1490:	c71fe0ef          	jal	ra,100 <__umodsi3>
    1494:	46a5                	li	a3,9
    1496:	4782                	lw	a5,0(sp)
    1498:	4332                	lw	t1,12(sp)
    149a:	4742                	lw	a4,16(sp)
    149c:	4652                	lw	a2,20(sp)
    149e:	00a6d463          	bge	a3,a0,14a6 <printInt+0x74>
    14a2:	46a2                	lw	a3,8(sp)
    14a4:	9536                	add	a0,a0,a3
    14a6:	03050513          	addi	a0,a0,48
    14aa:	fff70693          	addi	a3,a4,-1
    14ae:	fea70fa3          	sb	a0,-1(a4)
    14b2:	859a                	mv	a1,t1
    14b4:	853e                	mv	a0,a5
    14b6:	cc32                	sw	a2,24(sp)
    14b8:	ca3a                	sw	a4,20(sp)
    14ba:	c81a                	sw	t1,16(sp)
    14bc:	c63e                	sw	a5,12(sp)
    14be:	c036                	sw	a3,0(sp)
    14c0:	c15fe0ef          	jal	ra,d4 <__udivsi3>
    14c4:	47b2                	lw	a5,12(sp)
    14c6:	4342                	lw	t1,16(sp)
    14c8:	4752                	lw	a4,20(sp)
    14ca:	4662                	lw	a2,24(sp)
    14cc:	0467f963          	bgeu	a5,t1,151e <printInt+0xec>
    14d0:	cc01                	beqz	s0,14e8 <printInt+0xb6>
    14d2:	ca29                	beqz	a2,1524 <printInt+0xf2>
    14d4:	4792                	lw	a5,4(sp)
    14d6:	8b89                	andi	a5,a5,2
    14d8:	c7b1                	beqz	a5,1524 <printInt+0xf2>
    14da:	02d00593          	li	a1,45
    14de:	8526                	mv	a0,s1
    14e0:	c432                	sw	a2,8(sp)
    14e2:	350d                	jal	1304 <printchar>
    14e4:	4622                	lw	a2,8(sp)
    14e6:	167d                	addi	a2,a2,-1
    14e8:	4792                	lw	a5,4(sp)
    14ea:	8b91                	andi	a5,a5,4
    14ec:	c395                	beqz	a5,1510 <printInt+0xde>
    14ee:	4706                	lw	a4,64(sp)
    14f0:	06100793          	li	a5,97
    14f4:	c432                	sw	a2,8(sp)
    14f6:	03000593          	li	a1,48
    14fa:	8526                	mv	a0,s1
    14fc:	02f71e63          	bne	a4,a5,1538 <printInt+0x106>
    1500:	3511                	jal	1304 <printchar>
    1502:	07800593          	li	a1,120
    1506:	8526                	mv	a0,s1
    1508:	3bf5                	jal	1304 <printchar>
    150a:	4622                	lw	a2,8(sp)
    150c:	0409                	addi	s0,s0,2
    150e:	1679                	addi	a2,a2,-2
    1510:	4716                	lw	a4,68(sp)
    1512:	4692                	lw	a3,4(sp)
    1514:	4582                	lw	a1,0(sp)
    1516:	8526                	mv	a0,s1
    1518:	3d15                	jal	134c <prints>
    151a:	9522                	add	a0,a0,s0
    151c:	bf15                	j	1450 <printInt+0x1e>
    151e:	87aa                	mv	a5,a0
    1520:	4702                	lw	a4,0(sp)
    1522:	b78d                	j	1484 <printInt+0x52>
    1524:	4682                	lw	a3,0(sp)
    1526:	02d00793          	li	a5,45
    152a:	4401                	li	s0,0
    152c:	fef68fa3          	sb	a5,-1(a3)
    1530:	ffe70793          	addi	a5,a4,-2
    1534:	c03e                	sw	a5,0(sp)
    1536:	bf4d                	j	14e8 <printInt+0xb6>
    1538:	33f1                	jal	1304 <printchar>
    153a:	05800593          	li	a1,88
    153e:	b7e1                	j	1506 <printInt+0xd4>

00001540 <printLongLongInt>:
    1540:	4501                	li	a0,0
    1542:	8082                	ret

00001544 <printDouble>:
    1544:	4501                	li	a0,0
    1546:	8082                	ret

00001548 <print>:
    1548:	fd810113          	addi	sp,sp,-40
    154c:	d022                	sw	s0,32(sp)
    154e:	ce26                	sw	s1,28(sp)
    1550:	d206                	sw	ra,36(sp)
    1552:	c42a                	sw	a0,8(sp)
    1554:	82ae                	mv	t0,a1
    1556:	8432                	mv	s0,a2
    1558:	c602                	sw	zero,12(sp)
    155a:	4481                	li	s1,0
    155c:	00028583          	lb	a1,0(t0)
    1560:	ed89                	bnez	a1,157a <print+0x32>
    1562:	47a2                	lw	a5,8(sp)
    1564:	c781                	beqz	a5,156c <print+0x24>
    1566:	4581                	li	a1,0
    1568:	853e                	mv	a0,a5
    156a:	3b69                	jal	1304 <printchar>
    156c:	5092                	lw	ra,36(sp)
    156e:	5402                	lw	s0,32(sp)
    1570:	8526                	mv	a0,s1
    1572:	44f2                	lw	s1,28(sp)
    1574:	02810113          	addi	sp,sp,40
    1578:	8082                	ret
    157a:	02500793          	li	a5,37
    157e:	00f58863          	beq	a1,a5,158e <print+0x46>
    1582:	4522                	lw	a0,8(sp)
    1584:	c816                	sw	t0,16(sp)
    1586:	0485                	addi	s1,s1,1
    1588:	3bb5                	jal	1304 <printchar>
    158a:	42c2                	lw	t0,16(sp)
    158c:	a839                	j	15aa <print+0x62>
    158e:	00128783          	lb	a5,1(t0)
    1592:	00128713          	addi	a4,t0,1
    1596:	00b79c63          	bne	a5,a1,15ae <print+0x66>
    159a:	4522                	lw	a0,8(sp)
    159c:	02500593          	li	a1,37
    15a0:	c83a                	sw	a4,16(sp)
    15a2:	338d                	jal	1304 <printchar>
    15a4:	4742                	lw	a4,16(sp)
    15a6:	0485                	addi	s1,s1,1
    15a8:	82ba                	mv	t0,a4
    15aa:	0285                	addi	t0,t0,1
    15ac:	bf45                	j	155c <print+0x14>
    15ae:	dbd5                	beqz	a5,1562 <print+0x1a>
    15b0:	02b00693          	li	a3,43
    15b4:	04d78963          	beq	a5,a3,1606 <print+0xbe>
    15b8:	00f6c863          	blt	a3,a5,15c8 <print+0x80>
    15bc:	02300693          	li	a3,35
    15c0:	04d78663          	beq	a5,a3,160c <print+0xc4>
    15c4:	4781                	li	a5,0
    15c6:	a005                	j	15e6 <print+0x9e>
    15c8:	02d00693          	li	a3,45
    15cc:	00d78a63          	beq	a5,a3,15e0 <print+0x98>
    15d0:	03000693          	li	a3,48
    15d4:	fed798e3          	bne	a5,a3,15c4 <print+0x7c>
    15d8:	00228713          	addi	a4,t0,2
    15dc:	4789                	li	a5,2
    15de:	a021                	j	15e6 <print+0x9e>
    15e0:	00228713          	addi	a4,t0,2
    15e4:	4785                	li	a5,1
    15e6:	00070683          	lb	a3,0(a4)
    15ea:	02b00613          	li	a2,43
    15ee:	04c68363          	beq	a3,a2,1634 <print+0xec>
    15f2:	02d64163          	blt	a2,a3,1614 <print+0xcc>
    15f6:	02300613          	li	a2,35
    15fa:	02c68b63          	beq	a3,a2,1630 <print+0xe8>
    15fe:	82ba                	mv	t0,a4
    1600:	4501                	li	a0,0
    1602:	46a5                	li	a3,9
    1604:	a081                	j	1644 <print+0xfc>
    1606:	00228713          	addi	a4,t0,2
    160a:	bf6d                	j	15c4 <print+0x7c>
    160c:	00228713          	addi	a4,t0,2
    1610:	4791                	li	a5,4
    1612:	bfd1                	j	15e6 <print+0x9e>
    1614:	02d00613          	li	a2,45
    1618:	00c68963          	beq	a3,a2,162a <print+0xe2>
    161c:	03000613          	li	a2,48
    1620:	fcc69fe3          	bne	a3,a2,15fe <print+0xb6>
    1624:	0027e793          	ori	a5,a5,2
    1628:	a031                	j	1634 <print+0xec>
    162a:	0705                	addi	a4,a4,1
    162c:	4785                	li	a5,1
    162e:	bfc1                	j	15fe <print+0xb6>
    1630:	0047e793          	ori	a5,a5,4
    1634:	0705                	addi	a4,a4,1
    1636:	b7e1                	j	15fe <print+0xb6>
    1638:	00251613          	slli	a2,a0,0x2
    163c:	9532                	add	a0,a0,a2
    163e:	0506                	slli	a0,a0,0x1
    1640:	953a                	add	a0,a0,a4
    1642:	0285                	addi	t0,t0,1
    1644:	00028603          	lb	a2,0(t0)
    1648:	fd060713          	addi	a4,a2,-48
    164c:	0ff77593          	andi	a1,a4,255
    1650:	feb6f4e3          	bgeu	a3,a1,1638 <print+0xf0>
    1654:	02e00713          	li	a4,46
    1658:	4699                	li	a3,6
    165a:	00e61e63          	bne	a2,a4,1676 <print+0x12e>
    165e:	0285                	addi	t0,t0,1
    1660:	4681                	li	a3,0
    1662:	45a5                	li	a1,9
    1664:	00028603          	lb	a2,0(t0)
    1668:	fd060613          	addi	a2,a2,-48
    166c:	0ff67713          	andi	a4,a2,255
    1670:	02e5f563          	bgeu	a1,a4,169a <print+0x152>
    1674:	c636                	sw	a3,12(sp)
    1676:	00028703          	lb	a4,0(t0)
    167a:	06a00613          	li	a2,106
    167e:	0ac70d63          	beq	a4,a2,1738 <print+0x1f0>
    1682:	02e64363          	blt	a2,a4,16a8 <print+0x160>
    1686:	04c00613          	li	a2,76
    168a:	0ac70763          	beq	a4,a2,1738 <print+0x1f0>
    168e:	06800613          	li	a2,104
    1692:	08c70c63          	beq	a4,a2,172a <print+0x1e2>
    1696:	4581                	li	a1,0
    1698:	a82d                	j	16d2 <print+0x18a>
    169a:	00269713          	slli	a4,a3,0x2
    169e:	96ba                	add	a3,a3,a4
    16a0:	0686                	slli	a3,a3,0x1
    16a2:	96b2                	add	a3,a3,a2
    16a4:	0285                	addi	t0,t0,1
    16a6:	bf7d                	j	1664 <print+0x11c>
    16a8:	07400613          	li	a2,116
    16ac:	08c70663          	beq	a4,a2,1738 <print+0x1f0>
    16b0:	07a00613          	li	a2,122
    16b4:	08c70263          	beq	a4,a2,1738 <print+0x1f0>
    16b8:	06c00613          	li	a2,108
    16bc:	4581                	li	a1,0
    16be:	00c71a63          	bne	a4,a2,16d2 <print+0x18a>
    16c2:	00128603          	lb	a2,1(t0)
    16c6:	458d                	li	a1,3
    16c8:	00e61463          	bne	a2,a4,16d0 <print+0x188>
    16cc:	0285                	addi	t0,t0,1
    16ce:	4591                	li	a1,4
    16d0:	0285                	addi	t0,t0,1
    16d2:	00028603          	lb	a2,0(t0)
    16d6:	06000393          	li	t2,96
    16da:	06100713          	li	a4,97
    16de:	00c3c463          	blt	t2,a2,16e6 <print+0x19e>
    16e2:	04100713          	li	a4,65
    16e6:	06700393          	li	t2,103
    16ea:	06c3c463          	blt	t2,a2,1752 <print+0x20a>
    16ee:	06500393          	li	t2,101
    16f2:	18765563          	bge	a2,t2,187c <print+0x334>
    16f6:	04700393          	li	t2,71
    16fa:	04c3c163          	blt	t2,a2,173c <print+0x1f4>
    16fe:	04500593          	li	a1,69
    1702:	16b65d63          	bge	a2,a1,187c <print+0x334>
    1706:	04300713          	li	a4,67
    170a:	eae610e3          	bne	a2,a4,15aa <print+0x62>
    170e:	4018                	lw	a4,0(s0)
    1710:	00440393          	addi	t2,s0,4
    1714:	ca16                	sw	t0,20(sp)
    1716:	00e10c23          	sb	a4,24(sp)
    171a:	c81e                	sw	t2,16(sp)
    171c:	00010ca3          	sb	zero,25(sp)
    1720:	4701                	li	a4,0
    1722:	86be                	mv	a3,a5
    1724:	862a                	mv	a2,a0
    1726:	082c                	addi	a1,sp,24
    1728:	a849                	j	17ba <print+0x272>
    172a:	00128603          	lb	a2,1(t0)
    172e:	4581                	li	a1,0
    1730:	fae611e3          	bne	a2,a4,16d2 <print+0x18a>
    1734:	0289                	addi	t0,t0,2
    1736:	bf71                	j	16d2 <print+0x18a>
    1738:	0285                	addi	t0,t0,1
    173a:	bfb1                	j	1696 <print+0x14e>
    173c:	06300693          	li	a3,99
    1740:	fcd607e3          	beq	a2,a3,170e <print+0x1c6>
    1744:	06c6cf63          	blt	a3,a2,17c2 <print+0x27a>
    1748:	05800693          	li	a3,88
    174c:	02d60363          	beq	a2,a3,1772 <print+0x22a>
    1750:	bda9                	j	15aa <print+0x62>
    1752:	07300693          	li	a3,115
    1756:	04d60463          	beq	a2,a3,179e <print+0x256>
    175a:	02c6cb63          	blt	a3,a2,1790 <print+0x248>
    175e:	06f00693          	li	a3,111
    1762:	0ed60463          	beq	a2,a3,184a <print+0x302>
    1766:	07000693          	li	a3,112
    176a:	0047e793          	ori	a5,a5,4
    176e:	e2d61ee3          	bne	a2,a3,15aa <print+0x62>
    1772:	4691                	li	a3,4
    1774:	0cd59163          	bne	a1,a3,1836 <print+0x2ee>
    1778:	00840393          	addi	t2,s0,8
    177c:	400c                	lw	a1,0(s0)
    177e:	4050                	lw	a2,4(s0)
    1780:	ca16                	sw	t0,20(sp)
    1782:	c23a                	sw	a4,4(sp)
    1784:	c03e                	sw	a5,0(sp)
    1786:	c81e                	sw	t2,16(sp)
    1788:	87aa                	mv	a5,a0
    178a:	4701                	li	a4,0
    178c:	46c1                	li	a3,16
    178e:	a881                	j	17de <print+0x296>
    1790:	07500693          	li	a3,117
    1794:	06d60a63          	beq	a2,a3,1808 <print+0x2c0>
    1798:	07800693          	li	a3,120
    179c:	bf45                	j	174c <print+0x204>
    179e:	4018                	lw	a4,0(s0)
    17a0:	000026b7          	lui	a3,0x2
    17a4:	00440393          	addi	t2,s0,4
    17a8:	0cc68593          	addi	a1,a3,204 # 20cc <font+0x500>
    17ac:	c311                	beqz	a4,17b0 <print+0x268>
    17ae:	85ba                	mv	a1,a4
    17b0:	4732                	lw	a4,12(sp)
    17b2:	ca16                	sw	t0,20(sp)
    17b4:	c81e                	sw	t2,16(sp)
    17b6:	86be                	mv	a3,a5
    17b8:	862a                	mv	a2,a0
    17ba:	4522                	lw	a0,8(sp)
    17bc:	b91ff0ef          	jal	ra,134c <prints>
    17c0:	a00d                	j	17e2 <print+0x29a>
    17c2:	4691                	li	a3,4
    17c4:	02d59463          	bne	a1,a3,17ec <print+0x2a4>
    17c8:	00840393          	addi	t2,s0,8
    17cc:	400c                	lw	a1,0(s0)
    17ce:	4050                	lw	a2,4(s0)
    17d0:	ca16                	sw	t0,20(sp)
    17d2:	c23a                	sw	a4,4(sp)
    17d4:	c03e                	sw	a5,0(sp)
    17d6:	c81e                	sw	t2,16(sp)
    17d8:	87aa                	mv	a5,a0
    17da:	4705                	li	a4,1
    17dc:	46a9                	li	a3,10
    17de:	4522                	lw	a0,8(sp)
    17e0:	3385                	jal	1540 <printLongLongInt>
    17e2:	43c2                	lw	t2,16(sp)
    17e4:	94aa                	add	s1,s1,a0
    17e6:	841e                	mv	s0,t2
    17e8:	42d2                	lw	t0,20(sp)
    17ea:	b3c1                	j	15aa <print+0x62>
    17ec:	46b2                	lw	a3,12(sp)
    17ee:	400c                	lw	a1,0(s0)
    17f0:	c816                	sw	t0,16(sp)
    17f2:	c236                	sw	a3,4(sp)
    17f4:	c03a                	sw	a4,0(sp)
    17f6:	0411                	addi	s0,s0,4
    17f8:	872a                	mv	a4,a0
    17fa:	4685                	li	a3,1
    17fc:	4629                	li	a2,10
    17fe:	4522                	lw	a0,8(sp)
    1800:	c33ff0ef          	jal	ra,1432 <printInt>
    1804:	94aa                	add	s1,s1,a0
    1806:	b351                	j	158a <print+0x42>
    1808:	4691                	li	a3,4
    180a:	00d59d63          	bne	a1,a3,1824 <print+0x2dc>
    180e:	00840393          	addi	t2,s0,8
    1812:	400c                	lw	a1,0(s0)
    1814:	4050                	lw	a2,4(s0)
    1816:	ca16                	sw	t0,20(sp)
    1818:	c23a                	sw	a4,4(sp)
    181a:	c03e                	sw	a5,0(sp)
    181c:	c81e                	sw	t2,16(sp)
    181e:	87aa                	mv	a5,a0
    1820:	4701                	li	a4,0
    1822:	bf6d                	j	17dc <print+0x294>
    1824:	46b2                	lw	a3,12(sp)
    1826:	400c                	lw	a1,0(s0)
    1828:	c816                	sw	t0,16(sp)
    182a:	c236                	sw	a3,4(sp)
    182c:	c03a                	sw	a4,0(sp)
    182e:	0411                	addi	s0,s0,4
    1830:	872a                	mv	a4,a0
    1832:	4681                	li	a3,0
    1834:	b7e1                	j	17fc <print+0x2b4>
    1836:	46b2                	lw	a3,12(sp)
    1838:	c816                	sw	t0,16(sp)
    183a:	400c                	lw	a1,0(s0)
    183c:	4641                	li	a2,16
    183e:	c236                	sw	a3,4(sp)
    1840:	c03a                	sw	a4,0(sp)
    1842:	0411                	addi	s0,s0,4
    1844:	872a                	mv	a4,a0
    1846:	4681                	li	a3,0
    1848:	bf5d                	j	17fe <print+0x2b6>
    184a:	4691                	li	a3,4
    184c:	00d59e63          	bne	a1,a3,1868 <print+0x320>
    1850:	00840393          	addi	t2,s0,8
    1854:	400c                	lw	a1,0(s0)
    1856:	4050                	lw	a2,4(s0)
    1858:	ca16                	sw	t0,20(sp)
    185a:	c23a                	sw	a4,4(sp)
    185c:	c03e                	sw	a5,0(sp)
    185e:	c81e                	sw	t2,16(sp)
    1860:	87aa                	mv	a5,a0
    1862:	4701                	li	a4,0
    1864:	46a1                	li	a3,8
    1866:	bfa5                	j	17de <print+0x296>
    1868:	46b2                	lw	a3,12(sp)
    186a:	400c                	lw	a1,0(s0)
    186c:	c816                	sw	t0,16(sp)
    186e:	c236                	sw	a3,4(sp)
    1870:	c03a                	sw	a4,0(sp)
    1872:	0411                	addi	s0,s0,4
    1874:	872a                	mv	a4,a0
    1876:	4681                	li	a3,0
    1878:	4621                	li	a2,8
    187a:	b751                	j	17fe <print+0x2b6>
    187c:	400c                	lw	a1,0(s0)
    187e:	00840613          	addi	a2,s0,8
    1882:	4040                	lw	s0,4(s0)
    1884:	c23a                	sw	a4,4(sp)
    1886:	872a                	mv	a4,a0
    1888:	4522                	lw	a0,8(sp)
    188a:	c832                	sw	a2,16(sp)
    188c:	c03e                	sw	a5,0(sp)
    188e:	8622                	mv	a2,s0
    1890:	87b6                	mv	a5,a3
    1892:	46a9                	li	a3,10
    1894:	ca16                	sw	t0,20(sp)
    1896:	cafff0ef          	jal	ra,1544 <printDouble>
    189a:	94aa                	add	s1,s1,a0
    189c:	4442                	lw	s0,16(sp)
    189e:	b7a9                	j	17e8 <print+0x2a0>

000018a0 <printf>:
    18a0:	fdc10113          	addi	sp,sp,-36
    18a4:	c82e                	sw	a1,16(sp)
    18a6:	ca32                	sw	a2,20(sp)
    18a8:	85aa                	mv	a1,a0
    18aa:	0810                	addi	a2,sp,16
    18ac:	4501                	li	a0,0
    18ae:	c606                	sw	ra,12(sp)
    18b0:	cc36                	sw	a3,24(sp)
    18b2:	ce3a                	sw	a4,28(sp)
    18b4:	d03e                	sw	a5,32(sp)
    18b6:	c032                	sw	a2,0(sp)
    18b8:	c91ff0ef          	jal	ra,1548 <print>
    18bc:	40b2                	lw	ra,12(sp)
    18be:	02410113          	addi	sp,sp,36
    18c2:	8082                	ret

000018c4 <puts>:
    18c4:	1141                	addi	sp,sp,-16
    18c6:	c422                	sw	s0,8(sp)
    18c8:	c226                	sw	s1,4(sp)
    18ca:	c606                	sw	ra,12(sp)
    18cc:	211c                	lbu	a5,0(a0)
    18ce:	84aa                	mv	s1,a0
    18d0:	4401                	li	s0,0
    18d2:	81dc                	sb	a5,3(sp)
    18d4:	00310783          	lb	a5,3(sp)
    18d8:	0405                	addi	s0,s0,1
    18da:	ef99                	bnez	a5,18f8 <puts+0x34>
    18dc:	47a9                	li	a5,10
    18de:	00310593          	addi	a1,sp,3
    18e2:	4605                	li	a2,1
    18e4:	4501                	li	a0,0
    18e6:	81dc                	sb	a5,3(sp)
    18e8:	9d1ff0ef          	jal	ra,12b8 <_write>
    18ec:	8522                	mv	a0,s0
    18ee:	40b2                	lw	ra,12(sp)
    18f0:	4422                	lw	s0,8(sp)
    18f2:	4492                	lw	s1,4(sp)
    18f4:	0141                	addi	sp,sp,16
    18f6:	8082                	ret
    18f8:	4605                	li	a2,1
    18fa:	00310593          	addi	a1,sp,3
    18fe:	4501                	li	a0,0
    1900:	9b9ff0ef          	jal	ra,12b8 <_write>
    1904:	008487b3          	add	a5,s1,s0
    1908:	239c                	lbu	a5,0(a5)
    190a:	81dc                	sb	a5,3(sp)
    190c:	b7e1                	j	18d4 <puts+0x10>

0000190e <memcpy>:
    190e:	00a5c7b3          	xor	a5,a1,a0
    1912:	8b8d                	andi	a5,a5,3
    1914:	00c50733          	add	a4,a0,a2
    1918:	e781                	bnez	a5,1920 <memcpy+0x12>
    191a:	478d                	li	a5,3
    191c:	02c7e763          	bltu	a5,a2,194a <memcpy+0x3c>
    1920:	87aa                	mv	a5,a0
    1922:	0ae57e63          	bgeu	a0,a4,19de <memcpy+0xd0>
    1926:	2194                	lbu	a3,0(a1)
    1928:	0785                	addi	a5,a5,1
    192a:	0585                	addi	a1,a1,1
    192c:	fed78fa3          	sb	a3,-1(a5)
    1930:	fee7ebe3          	bltu	a5,a4,1926 <memcpy+0x18>
    1934:	8082                	ret
    1936:	2194                	lbu	a3,0(a1)
    1938:	0785                	addi	a5,a5,1
    193a:	0585                	addi	a1,a1,1
    193c:	fed78fa3          	sb	a3,-1(a5)
    1940:	fee7ebe3          	bltu	a5,a4,1936 <memcpy+0x28>
    1944:	4402                	lw	s0,0(sp)
    1946:	0111                	addi	sp,sp,4
    1948:	8082                	ret
    194a:	00357693          	andi	a3,a0,3
    194e:	87aa                	mv	a5,a0
    1950:	ca89                	beqz	a3,1962 <memcpy+0x54>
    1952:	2194                	lbu	a3,0(a1)
    1954:	0785                	addi	a5,a5,1
    1956:	0585                	addi	a1,a1,1
    1958:	fed78fa3          	sb	a3,-1(a5)
    195c:	0037f693          	andi	a3,a5,3
    1960:	bfc5                	j	1950 <memcpy+0x42>
    1962:	ffc77693          	andi	a3,a4,-4
    1966:	fe068613          	addi	a2,a3,-32
    196a:	06c7f563          	bgeu	a5,a2,19d4 <memcpy+0xc6>
    196e:	1171                	addi	sp,sp,-4
    1970:	c022                	sw	s0,0(sp)
    1972:	49c0                	lw	s0,20(a1)
    1974:	0005a303          	lw	t1,0(a1)
    1978:	0085a383          	lw	t2,8(a1)
    197c:	cbc0                	sw	s0,20(a5)
    197e:	4d80                	lw	s0,24(a1)
    1980:	0067a023          	sw	t1,0(a5)
    1984:	0045a303          	lw	t1,4(a1)
    1988:	cf80                	sw	s0,24(a5)
    198a:	4dc0                	lw	s0,28(a1)
    198c:	0067a223          	sw	t1,4(a5)
    1990:	00c5a283          	lw	t0,12(a1)
    1994:	0105a303          	lw	t1,16(a1)
    1998:	02458593          	addi	a1,a1,36
    199c:	cfc0                	sw	s0,28(a5)
    199e:	ffc5a403          	lw	s0,-4(a1)
    19a2:	0077a423          	sw	t2,8(a5)
    19a6:	0057a623          	sw	t0,12(a5)
    19aa:	0067a823          	sw	t1,16(a5)
    19ae:	02478793          	addi	a5,a5,36
    19b2:	fe87ae23          	sw	s0,-4(a5)
    19b6:	fac7eee3          	bltu	a5,a2,1972 <memcpy+0x64>
    19ba:	f8d7f3e3          	bgeu	a5,a3,1940 <memcpy+0x32>
    19be:	4190                	lw	a2,0(a1)
    19c0:	0791                	addi	a5,a5,4
    19c2:	0591                	addi	a1,a1,4
    19c4:	fec7ae23          	sw	a2,-4(a5)
    19c8:	bfcd                	j	19ba <memcpy+0xac>
    19ca:	4190                	lw	a2,0(a1)
    19cc:	0791                	addi	a5,a5,4
    19ce:	0591                	addi	a1,a1,4
    19d0:	fec7ae23          	sw	a2,-4(a5)
    19d4:	fed7ebe3          	bltu	a5,a3,19ca <memcpy+0xbc>
    19d8:	f4e7e7e3          	bltu	a5,a4,1926 <memcpy+0x18>
    19dc:	8082                	ret
    19de:	8082                	ret
    19e0:	1609                	addi	a2,a2,-30
    19e2:	2009                	jal	19e4 <memcpy+0xd6>
    19e4:	1b21                	addi	s6,s6,-24
    19e6:	15171913          	0x15171913
    19ea:	2b1e                	lhu	a5,16(a4)
    19ec:	0504                	addi	s1,sp,640
    19ee:	0e02                	c.slli64	t3
    19f0:	1e08140b          	0x1e08140b
    19f4:	1d22                	slli	s10,s10,0x28
    19f6:	1e18                	addi	a4,sp,816
    19f8:	2b241a1b          	0x2b241a1b
    19fc:	0606                	slli	a2,a2,0x1
    19fe:	0f02                	c.slli64	t5
    1a00:	00004843          	fmadd.s	fa6,ft0,ft0,ft0,rmm
    1a04:	003a                	c.slli	zero,0xe
    1a06:	0000                	unimp
    1a08:	4441                	li	s0,16
    1a0a:	6f4d2043          	fmadd.q	ft0,fs10,fs4,fa3,rdn
    1a0e:	696e                	flw	fs2,216(sp)
    1a10:	6f74                	flw	fa3,92(a4)
    1a12:	0072                	c.slli	zero,0x1c

00001a14 <CSWTCH.2>:
    1a14:	07ff 07e0 ffe0 f81f 6944 7073 616c 2079     ........Display 
    1a24:	664f 0066 4843 3233 3056 3330 4120 4344     Off.CH32V003 ADC
    1a34:	0000 0000 6f4d 696e 6f74 2072 3176 302e     ....Monitor v1.0
    1a44:	0000 0000 6f54 6375 3a68 5420 7275 206e     ....Touch: Turn 
    1a54:	6e4f 0000 6f48 646c 203a 6f54 6767 656c     On..Hold: Toggle
    1a64:	0000 0000 4441 2043 6e69 7469 6169 696c     ....ADC initiali
    1a74:	657a 0d64 0000 0000 5750 204d 6e69 7469     zed.....PWM init
    1a84:	6169 696c 657a 0d64 0000 0000 6f54 6375     ialized.....Touc
    1a94:	2068 7562 7474 6e6f 6920 696e 6974 6c61     h button initial
    1aa4:	7a69 6465 000d 0000 6944 7073 616c 2079     ized....Display 
    1ab4:	6f63 746e 6f72 206c 6e69 7469 6169 696c     control initiali
    1ac4:	657a 0d64 0000 0000 4441 2043 6964 7073     zed.....ADC disp
    1ad4:	616c 2079 6e69 7469 6169 696c 657a 0d64     lay initialized.
    1ae4:	0000 0000 7953 7473 6d65 6920 696e 6974     ....System initi
    1af4:	6c61 7a69 7461 6f69 206e 6f63 706d 656c     alization comple
    1b04:	6574 000d 0a0d 3d3d 203d 4843 3233 3056     te....=== CH32V0
    1b14:	3330 4120 4344 4d20 6e6f 7469 726f 3d20     03 ADC Monitor =
    1b24:	3d3d 000d 7953 7473 6d65 6c43 3a6b 2520     ==..SystemClk: %
    1b34:	2064 7a48 0a0d 0000 6843 7069 4449 203a     d Hz....ChipID: 
    1b44:	3025 7838 0a0d 0000 6f54 6375 3a68 5320     %08x....Touch: S
    1b54:	6f68 7472 7020 6572 7373 2d20 7420 7275     hort press - tur
    1b64:	696e 676e 6f20 206e 6964 7073 616c 0d79     ning on display.
    1b74:	0000 0000 6f54 6375 3a68 4c20 6e6f 2067     ....Touch: Long 
    1b84:	7270 7365 2073 202d 6f74 6767 696c 676e     press - toggling
    1b94:	6420 7369 6c70 7961 6d20 646f 0d65 0000      display mode...
    1ba4:	6f54 6375 3a68 5420 6d69 6f65 7475 2d20     Touch: Timeout -
    1bb4:	7420 7275 696e 676e 6f20 6666 6420 7369      turning off dis
    1bc4:	6c70 7961 000d 0000                         play....

00001bcc <font>:
    1bcc:	0000 0000 3e00 4f5b 3e5b 6b3e 6b4f 1c3e     .....>[O[>>kOk>.
    1bdc:	7c3e 1c3e 3c18 3c7e 1c18 7d57 1c57 5e1c     >|>..<~<..W}W..^
    1bec:	5e7f 001c 3c18 0018 e7ff e7c3 00ff 2418     .^...<.........$
    1bfc:	0018 e7ff e7db 30ff 3a48 0e06 2926 2979     .......0H:..&)y)
    1c0c:	4026 057f 0705 7f40 2505 5a3f e73c 5a3c     &@....@..%?Z<.<Z
    1c1c:	3e7f 1c1c 0808 1c1c 7f3e 2214 227f 5f14     .>......>.."."._
    1c2c:	005f 5f5f 0906 017f 007f 8966 6a95 6060     _.__......f..j``
    1c3c:	6060 9460 ffa2 94a2 0408 047e 1008 7e20     ```.......~... ~
    1c4c:	1020 0808 1c2a 0808 2a1c 0808 101e 1010      ...*....*......
    1c5c:	0c10 0c1e 0c1e 3830 383e 0630 3e0e 060e     ......08>80..>..
    1c6c:	0000 0000 0000 5f00 0000 0700 0700 1400     ......._........
    1c7c:	147f 147f 2a24 2a7f 2312 0813 6264 4936     ....$*.*.#..db6I
    1c8c:	2056 0050 0708 0003 1c00 4122 0000 2241     V P......."A..A"
    1c9c:	001c 1c2a 1c7f 082a 3e08 0808 8000 3070     ..*...*..>....p0
    1cac:	0800 0808 0808 0000 6060 2000 0810 0204     ........``. ....
    1cbc:	513e 4549 003e 7f42 0040 4972 4949 2146     >QIE>.B.@.rIIIF!
    1ccc:	4941 334d 1418 7f12 2710 4545 3945 4a3c     AIM3.....'EEE9<J
    1cdc:	4949 4131 1121 0709 4936 4949 4636 4949     II1A!...6III6FII
    1cec:	1e29 0000 0014 0000 3440 0000 0800 2214     ).......@4....."
    1cfc:	1441 1414 1414 4100 1422 0208 5901 0609     A......A"....Y..
    1d0c:	413e 595d 7c4e 1112 7c12 497f 4949 3e36     >A]YN|...|.III6>
    1d1c:	4141 2241 417f 4141 7f3e 4949 4149 097f     AAA".AAA>.IIIA..
    1d2c:	0909 3e01 4141 7351 087f 0808 007f 7f41     ...>AAQs......A.
    1d3c:	0041 4020 3f41 7f01 1408 4122 407f 4040     A. @A?...."A.@@@
    1d4c:	7f40 1c02 7f02 047f 1008 3e7f 4141 3e41     @..........>AAA>
    1d5c:	097f 0909 3e06 5141 5e21 097f 2919 2646     .....>AQ!^...)F&
    1d6c:	4949 3249 0103 017f 3f03 4040 3f40 201f     III2.....?@@@?. 
    1d7c:	2040 3f1f 3840 3f40 1463 1408 0363 7804     @ .?@8@?c...c..x
    1d8c:	0304 5961 4d49 0043 417f 4141 0402 1008     ..aYIMC..AAA....
    1d9c:	0020 4141 7f41 0204 0201 4004 4040 4040      .AAA......@@@@@
    1dac:	0300 0807 2000 5454 4078 287f 4444 3838     ..... TTx@.(DD88
    1dbc:	4444 2844 4438 2844 387f 5454 1854 0800     DDD(8DD(.8TTT...
    1dcc:	097e 1802 a4a4 789c 087f 0404 0078 7d44     ~......x....x.D}
    1ddc:	0040 4020 3d40 7f00 2810 0044 4100 407f     @. @@=...(D..A.@
    1dec:	7c00 7804 7804 087c 0404 3878 4444 3844     .|.x.x|...x8DDD8
    1dfc:	18fc 2424 1818 2424 fc18 087c 0404 4808     ..$$..$$..|....H
    1e0c:	5454 2454 0404 443f 3c24 4040 7c20 201c     TTT$..?D$<@@ |. 
    1e1c:	2040 3c1c 3040 3c40 2844 2810 4c44 9090     @ .<@0@<D(.(DL..
    1e2c:	7c90 6444 4c54 0044 3608 0041 0000 0077     .|DdTLD..6A...w.
    1e3c:	0000 3641 0008 0102 0402 3c02 2326 3c26     ..A6.......<&#&<
    1e4c:	a11e 61a1 3a12 4040 7a20 5438 5554 2159     ...a.:@@ z8TTUY!
    1e5c:	5555 4179 5422 7854 2142 5455 4078 5420     UUyA"TTxB!UTx@ T
    1e6c:	7955 0c40 521e 1272 5539 5555 3959 5454     <EMAIL>.9UUUY9TT
    1e7c:	5954 5539 5454 0058 4500 417c 0200 7d45     TY9UTTX..E|A..E}
    1e8c:	0042 4501 407c 127d 1211 f07d 2528 f028     B..E|@}...}.(%(.
    1e9c:	547c 4555 2000 5454 547c 0a7c 7f09 3249     |TUE. TT|T|...I2
    1eac:	4949 3249 443a 4444 323a 484a 3048 413a     III2:DDD:2JHH0:A
    1ebc:	2141 3a7a 4042 7820 9d00 a0a0 3d7d 4242     A!z:B@ x....}=BB
    1ecc:	3d42 403d 4040 3c3d ff24 2424 7e48 4349     B==@@@=<$.$$H~IC
    1edc:	2b66 fc2f 2b2f 09ff f629 c020 7e88 0309     f+/./+..). ..~..
    1eec:	5420 7954 0041 4400 417d 4830 4a48 3832      TTyA..D}A0HHJ28
    1efc:	4040 7a22 7a00 0a0a 7d72 190d 7d31 2926     @@"z.z..r}..1}&)
    1f0c:	2f29 2628 2929 2629 4830 404d 3820 0808     )/(&)))&0HM@ 8..
    1f1c:	0808 0808 0808 2f38 c810 baac 102f 3428     ......8/..../.(4
    1f2c:	00fa 7b00 0000 1408 142a 2222 2a14 0814     ...{....*."".*..
    1f3c:	0055 0055 aa55 aa55 aa55 55ff 55ff 00ff     U.U.U.U.U..U.U..
    1f4c:	0000 00ff 1010 ff10 1400 1414 00ff 1010     ................
    1f5c:	00ff 10ff f010 f010 1414 fc14 1400 f714     ................
    1f6c:	ff00 0000 00ff 14ff f414 fc04 1414 1017     ................
    1f7c:	101f 1f10 1f10 1414 1f14 1000 1010 00f0     ................
    1f8c:	0000 1f00 1010 1010 101f 1010 f010 0010     ................
    1f9c:	0000 10ff 1010 1010 1010 1010 10ff 0000     ................
    1fac:	ff00 0014 ff00 ff00 0000 101f 0017 fc00     ................
    1fbc:	f404 1414 1017 1417 f414 f404 0000 00ff     ................
    1fcc:	14f7 1414 1414 1414 00f7 14f7 1414 1417     ................
    1fdc:	1010 101f 141f 1414 14f4 1010 10f0 00f0     ................
    1fec:	1f00 1f10 0000 1f00 0014 0000 14fc 0000     ................
    1ffc:	10f0 10f0 ff10 ff10 1414 ff14 1014 1010     ................
    200c:	001f 0000 f000 ff10 ffff ffff f0f0 f0f0     ................
    201c:	fff0 ffff 0000 0000 ff00 0fff 0f0f 0f0f     ................
    202c:	4438 3844 fc44 4a4a 344a 027e 0602 0206     8DD8D.JJJ4~.....
    203c:	027e 027e 5563 4149 3863 4444 043c 7e40     ~.~.cUIAc8DD<.@~
    204c:	1e20 0620 7e02 0202 a599 a5e7 1c99 492a      . ..~........*I
    205c:	1c2a 724c 7201 304c 4d4a 304d 4830 4878     *.Lr.rL0JMM00HxH
    206c:	bc30 5a62 3d46 493e 4949 7e00 0101 7e01     0.bZF=>III.~...~
    207c:	2a2a 2a2a 442a 5f44 4444 5140 444a 4040     *****DD_DD@QJD@@
    208c:	4a44 4051 0000 01ff e003 ff80 0000 0808     DJQ@............
    209c:	6b6b 3608 3612 3624 0600 0909 0006 1800     kk.6.6$6........
    20ac:	0018 0000 1010 3000 ff40 0101 1f00 0101     .......0@.......
    20bc:	001e 1d19 1217 3c00 3c3c 003c 0000 0000     .......<<<<.....
    20cc:	6e28 6c75 296c 0000                         (null)..
