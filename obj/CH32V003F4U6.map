Archive member included to satisfy reference by file (symbol)

/home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
                              ./User/main.o (printf)
/home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(save-restore.o)
                              ./User/adc_config.o (__riscv_save_2)
/home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(muldi3.o)
                              ./User/adc_config.o (__mulsi3)
/home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(div.o)
                              ./User/adc_display.o (__divsi3)
/home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libg_nano.a(lib_a-memcpy.o)
                              ./User/st7735.o (memcpy)
/home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libg_nano.a(lib_a-memset.o)
                              ./User/adc_config.o (memset)

Discarded input sections

 .text          0x0000000000000000        0x0 ./User/adc_config.o
 .data          0x0000000000000000        0x0 ./User/adc_config.o
 .bss           0x0000000000000000        0x0 ./User/adc_config.o
 .text.ADC_Convert_To_Voltage
                0x0000000000000000       0x24 ./User/adc_config.o
 .text.ADC_Start_Conversion
                0x0000000000000000       0x34 ./User/adc_config.o
 .text.ADC_Is_Conversion_Done
                0x0000000000000000       0x26 ./User/adc_config.o
 .text          0x0000000000000000        0x0 ./User/adc_display.o
 .data          0x0000000000000000        0x0 ./User/adc_display.o
 .bss           0x0000000000000000        0x0 ./User/adc_display.o
 .text.ADC_Display_Set_Config
                0x0000000000000000       0x10 ./User/adc_display.o
 .text.ADC_Display_Get_Channel_Color
                0x0000000000000000       0x1e ./User/adc_display.o
 .text.ADC_Display_Format_Voltage
                0x0000000000000000       0x6c ./User/adc_display.o
 .text.ADC_Display_Format_Percentage
                0x0000000000000000       0x36 ./User/adc_display.o
 .text          0x0000000000000000        0x0 ./User/ch32v00x_it.o
 .data          0x0000000000000000        0x0 ./User/ch32v00x_it.o
 .bss           0x0000000000000000        0x0 ./User/ch32v00x_it.o
 .text          0x0000000000000000        0x0 ./User/display_control.o
 .data          0x0000000000000000        0x0 ./User/display_control.o
 .bss           0x0000000000000000        0x0 ./User/display_control.o
 .text.Display_Control_Set_Content_Update_Flag
                0x0000000000000000        0xc ./User/display_control.o
 .text.Display_Control_Get_State
                0x0000000000000000        0xa ./User/display_control.o
 .text          0x0000000000000000        0x0 ./User/main.o
 .data          0x0000000000000000        0x0 ./User/main.o
 .bss           0x0000000000000000        0x0 ./User/main.o
 .text          0x0000000000000000        0x0 ./User/pwm_config.o
 .data          0x0000000000000000        0x0 ./User/pwm_config.o
 .bss           0x0000000000000000        0x0 ./User/pwm_config.o
 .text.PWM_Set_Brightness_Percent
                0x0000000000000000       0x44 ./User/pwm_config.o
 .text.PWM_Fade_To_Brightness
                0x0000000000000000       0x22 ./User/pwm_config.o
 .text          0x0000000000000000        0x0 ./User/st7735.o
 .data          0x0000000000000000        0x0 ./User/st7735.o
 .bss           0x0000000000000000        0x0 ./User/st7735.o
 .text._tft_draw_fast_v_line
                0x0000000000000000       0x94 ./User/st7735.o
 .text._tft_draw_fast_h_line
                0x0000000000000000       0x92 ./User/st7735.o
 .text.tft_draw_pixel
                0x0000000000000000       0x4c ./User/st7735.o
 .text.tft_draw_bitmap
                0x0000000000000000       0x82 ./User/st7735.o
 .text.tft_draw_rect
                0x0000000000000000       0x98 ./User/st7735.o
 .text.tft_draw_line
                0x0000000000000000      0x178 ./User/st7735.o
 .text          0x0000000000000000        0x0 ./User/system_ch32v00x.o
 .data          0x0000000000000000        0x0 ./User/system_ch32v00x.o
 .bss           0x0000000000000000        0x0 ./User/system_ch32v00x.o
 .text          0x0000000000000000        0x0 ./User/touch_button.o
 .data          0x0000000000000000        0x0 ./User/touch_button.o
 .bss           0x0000000000000000        0x0 ./User/touch_button.o
 .text.Touch_Button_Is_Screen_On
                0x0000000000000000        0xa ./User/touch_button.o
 .text.Touch_Button_Reset_Timeout
                0x0000000000000000       0x12 ./User/touch_button.o
 .text          0x0000000000000000        0x0 ./Startup/startup_ch32v00x.o
 .data          0x0000000000000000        0x0 ./Startup/startup_ch32v00x.o
 .bss           0x0000000000000000        0x0 ./Startup/startup_ch32v00x.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_adc.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_adc.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_DeInit
                0x0000000000000000       0x3a ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_StructInit
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_DMACmd
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_ITConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_GetSoftwareStartConvStatus
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_DiscModeChannelCountConfig
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_DiscModeCmd
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_ExternalTrigConvCmd
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_AutoInjectedConvCmd
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_InjectedDiscModeCmd
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_ExternalTrigInjectedConvConfig
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_ExternalTrigInjectedConvCmd
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_SoftwareStartInjectedConvCmd
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_GetSoftwareStartInjectedConvCmdStatus
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_InjectedChannelConfig
                0x0000000000000000       0x7a ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_InjectedSequencerLengthConfig
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_SetInjectedOffset
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_GetInjectedConversionValue
                0x0000000000000000       0x1c ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_AnalogWatchdogCmd
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_AnalogWatchdogThresholdsConfig
                0x0000000000000000        0x6 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_AnalogWatchdogSingleChannelConfig
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_GetITStatus
                0x0000000000000000       0x1c ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_ClearITPendingBit
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_Calibration_Vol
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_ExternalTrig_DLY
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_adc.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_dbgmcu.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_dbgmcu.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_dbgmcu.o
 .text.DBGMCU_GetREVID
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_dbgmcu.o
 .text.DBGMCU_GetDEVID
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_dbgmcu.o
 .text.__get_DEBUG_CR
                0x0000000000000000        0x6 ./Peripheral/src/ch32v00x_dbgmcu.o
 .text.__set_DEBUG_CR
                0x0000000000000000        0x6 ./Peripheral/src/ch32v00x_dbgmcu.o
 .text.DBGMCU_Config
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_dbgmcu.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_dma.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_dma.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_DeInit
                0x0000000000000000       0x92 ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_Init
                0x0000000000000000       0x38 ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_StructInit
                0x0000000000000000       0x2e ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_Cmd  0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_ITConfig
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_SetCurrDataCounter
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_GetCurrDataCounter
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_GetFlagStatus
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_ClearFlag
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_GetITStatus
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_ClearITPendingBit
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_dma.o
 .debug_info    0x0000000000000000      0xd17 ./Peripheral/src/ch32v00x_dma.o
 .debug_abbrev  0x0000000000000000      0x302 ./Peripheral/src/ch32v00x_dma.o
 .debug_loc     0x0000000000000000      0x14a ./Peripheral/src/ch32v00x_dma.o
 .debug_aranges
                0x0000000000000000       0x60 ./Peripheral/src/ch32v00x_dma.o
 .debug_ranges  0x0000000000000000       0x50 ./Peripheral/src/ch32v00x_dma.o
 .debug_line    0x0000000000000000      0x843 ./Peripheral/src/ch32v00x_dma.o
 .debug_str     0x0000000000000000      0x803 ./Peripheral/src/ch32v00x_dma.o
 .comment       0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_dma.o
 .debug_frame   0x0000000000000000       0xcc ./Peripheral/src/ch32v00x_dma.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_exti.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_exti.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_exti.o
 .text.EXTI_DeInit
                0x0000000000000000       0x22 ./Peripheral/src/ch32v00x_exti.o
 .text.EXTI_StructInit
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_exti.o
 .text.EXTI_GenerateSWInterrupt
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_exti.o
 .text.EXTI_GetFlagStatus
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_exti.o
 .text.EXTI_ClearFlag
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_exti.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_flash.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_flash.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_flash.o
 .text.ROM_ERASE
                0x0000000000000000       0x60 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_SetLatency
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_Unlock
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_Lock
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_GetUserOptionByte
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_GetWriteProtectionOptionByte
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_GetReadOutProtectionStatus
                0x0000000000000000        0xc ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ITConfig
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_GetFlagStatus
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ClearFlag
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_GetStatus
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_GetBank1Status
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_WaitForLastOperation
                0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ErasePage
                0x0000000000000000       0x4c ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_EraseAllPages
                0x0000000000000000       0x48 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_EraseOptionBytes
                0x0000000000000000       0x9a ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ProgramWord
                0x0000000000000000       0x6e ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ProgramHalfWord
                0x0000000000000000       0x48 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ProgramOptionByteData
                0x0000000000000000       0x66 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_EnableWriteProtection
                0x0000000000000000       0x9e ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ReadOutProtection
                0x0000000000000000       0xae ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_UserOptionByteConfig
                0x0000000000000000       0x7a ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_WaitForLastBank1Operation
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_Unlock_Fast
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_Lock_Fast
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_BufReset
                0x0000000000000000       0x28 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_BufLoad
                0x0000000000000000       0x36 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ErasePage_Fast
                0x0000000000000000       0x36 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ProgramPage_Fast
                0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_flash.o
 .text.SystemReset_StartMode
                0x0000000000000000       0x56 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ROM_ERASE
                0x0000000000000000      0x140 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ROM_WRITE
                0x0000000000000000       0xf6 ./Peripheral/src/ch32v00x_flash.o
 .debug_info    0x0000000000000000     0x1481 ./Peripheral/src/ch32v00x_flash.o
 .debug_abbrev  0x0000000000000000      0x3fa ./Peripheral/src/ch32v00x_flash.o
 .debug_loc     0x0000000000000000      0xad0 ./Peripheral/src/ch32v00x_flash.o
 .debug_aranges
                0x0000000000000000      0x110 ./Peripheral/src/ch32v00x_flash.o
 .debug_ranges  0x0000000000000000      0x100 ./Peripheral/src/ch32v00x_flash.o
 .debug_line    0x0000000000000000     0x1cd5 ./Peripheral/src/ch32v00x_flash.o
 .debug_str     0x0000000000000000      0xac5 ./Peripheral/src/ch32v00x_flash.o
 .comment       0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_flash.o
 .debug_frame   0x0000000000000000      0x324 ./Peripheral/src/ch32v00x_flash.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_gpio.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_gpio.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_DeInit
                0x0000000000000000       0x68 ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_AFIODeInit
                0x0000000000000000       0x28 ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_StructInit
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_ReadInputData
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_ReadOutputDataBit
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_ReadOutputData
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_SetBits
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_ResetBits
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_WriteBit
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_Write
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_PinLockConfig
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_PinRemapConfig
                0x0000000000000000       0xc4 ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_IPD_Unused
                0x0000000000000000       0xa4 ./Peripheral/src/ch32v00x_gpio.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_i2c.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_i2c.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_DeInit
                0x0000000000000000       0x3a ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_Init
                0x0000000000000000      0x100 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_StructInit
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_Cmd  0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_DMACmd
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_DMALastTransferCmd
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_GenerateSTART
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_GenerateSTOP
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_AcknowledgeConfig
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_OwnAddress2Config
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_DualAddressCmd
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_GeneralCallCmd
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_ITConfig
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_SendData
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_ReceiveData
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_Send7bitAddress
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_ReadRegister
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_SoftwareResetCmd
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_NACKPositionConfig
                0x0000000000000000       0x22 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_TransmitPEC
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_PECPositionConfig
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_CalculatePEC
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_GetPEC
                0x0000000000000000        0x6 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_ARPCmd
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_StretchClockCmd
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_FastModeDutyCycleConfig
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_CheckEvent
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_GetLastEvent
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_GetFlagStatus
                0x0000000000000000       0x32 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_ClearFlag
                0x0000000000000000        0xc ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_GetITStatus
                0x0000000000000000       0x20 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_ClearITPendingBit
                0x0000000000000000        0xc ./Peripheral/src/ch32v00x_i2c.o
 .debug_info    0x0000000000000000     0x1417 ./Peripheral/src/ch32v00x_i2c.o
 .debug_abbrev  0x0000000000000000      0x3a7 ./Peripheral/src/ch32v00x_i2c.o
 .debug_loc     0x0000000000000000      0x720 ./Peripheral/src/ch32v00x_i2c.o
 .debug_aranges
                0x0000000000000000      0x108 ./Peripheral/src/ch32v00x_i2c.o
 .debug_ranges  0x0000000000000000       0xf8 ./Peripheral/src/ch32v00x_i2c.o
 .debug_line    0x0000000000000000     0x112a ./Peripheral/src/ch32v00x_i2c.o
 .debug_str     0x0000000000000000      0xac2 ./Peripheral/src/ch32v00x_i2c.o
 .comment       0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_i2c.o
 .debug_frame   0x0000000000000000      0x254 ./Peripheral/src/ch32v00x_i2c.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_iwdg.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_iwdg.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_iwdg.o
 .text.IWDG_WriteAccessCmd
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_iwdg.o
 .text.IWDG_SetPrescaler
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_iwdg.o
 .text.IWDG_SetReload
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_iwdg.o
 .text.IWDG_ReloadCounter
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_iwdg.o
 .text.IWDG_Enable
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_iwdg.o
 .text.IWDG_GetFlagStatus
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_iwdg.o
 .debug_info    0x0000000000000000      0xb46 ./Peripheral/src/ch32v00x_iwdg.o
 .debug_abbrev  0x0000000000000000      0x282 ./Peripheral/src/ch32v00x_iwdg.o
 .debug_loc     0x0000000000000000       0x68 ./Peripheral/src/ch32v00x_iwdg.o
 .debug_aranges
                0x0000000000000000       0x48 ./Peripheral/src/ch32v00x_iwdg.o
 .debug_ranges  0x0000000000000000       0x38 ./Peripheral/src/ch32v00x_iwdg.o
 .debug_line    0x0000000000000000      0x49b ./Peripheral/src/ch32v00x_iwdg.o
 .debug_str     0x0000000000000000      0x709 ./Peripheral/src/ch32v00x_iwdg.o
 .comment       0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_iwdg.o
 .debug_frame   0x0000000000000000       0x70 ./Peripheral/src/ch32v00x_iwdg.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_misc.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_misc.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_misc.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_opa.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_opa.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_opa.o
 .text.OPA_DeInit
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_opa.o
 .text.OPA_Init
                0x0000000000000000       0x22 ./Peripheral/src/ch32v00x_opa.o
 .text.OPA_StructInit
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_opa.o
 .text.OPA_Cmd  0x0000000000000000       0x2c ./Peripheral/src/ch32v00x_opa.o
 .debug_info    0x0000000000000000      0xaa5 ./Peripheral/src/ch32v00x_opa.o
 .debug_abbrev  0x0000000000000000      0x230 ./Peripheral/src/ch32v00x_opa.o
 .debug_loc     0x0000000000000000       0x35 ./Peripheral/src/ch32v00x_opa.o
 .debug_aranges
                0x0000000000000000       0x38 ./Peripheral/src/ch32v00x_opa.o
 .debug_ranges  0x0000000000000000       0x28 ./Peripheral/src/ch32v00x_opa.o
 .debug_line    0x0000000000000000      0x491 ./Peripheral/src/ch32v00x_opa.o
 .debug_str     0x0000000000000000      0x67d ./Peripheral/src/ch32v00x_opa.o
 .comment       0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_opa.o
 .debug_frame   0x0000000000000000       0x50 ./Peripheral/src/ch32v00x_opa.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_pwr.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_pwr.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_pwr.o
 .text.PWR_DeInit
                0x0000000000000000       0x2c ./Peripheral/src/ch32v00x_pwr.o
 .text.PWR_PVDCmd
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_pwr.o
 .text.PWR_PVDLevelConfig
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_pwr.o
 .text.PWR_AutoWakeUpCmd
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_pwr.o
 .text.PWR_AWU_SetPrescaler
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_pwr.o
 .text.PWR_AWU_SetWindowValue
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_pwr.o
 .text.PWR_EnterSTANDBYMode
                0x0000000000000000       0xfc ./Peripheral/src/ch32v00x_pwr.o
 .text.PWR_GetFlagStatus
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_pwr.o
 .debug_info    0x0000000000000000      0xf00 ./Peripheral/src/ch32v00x_pwr.o
 .debug_abbrev  0x0000000000000000      0x359 ./Peripheral/src/ch32v00x_pwr.o
 .debug_loc     0x0000000000000000      0x19a ./Peripheral/src/ch32v00x_pwr.o
 .debug_aranges
                0x0000000000000000       0x58 ./Peripheral/src/ch32v00x_pwr.o
 .debug_ranges  0x0000000000000000       0x90 ./Peripheral/src/ch32v00x_pwr.o
 .debug_line    0x0000000000000000      0x7ef ./Peripheral/src/ch32v00x_pwr.o
 .debug_str     0x0000000000000000      0x80f ./Peripheral/src/ch32v00x_pwr.o
 .comment       0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_pwr.o
 .debug_frame   0x0000000000000000       0xb8 ./Peripheral/src/ch32v00x_pwr.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_rcc.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_rcc.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_HSEConfig
                0x0000000000000000       0x3c ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_DeInit
                0x0000000000000000       0x58 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_HSICmd
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_PLLConfig
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_PLLCmd
                0x0000000000000000       0x24 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_SYSCLKConfig
                0x0000000000000000       0x4e ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_GetSYSCLKSource
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_HCLKConfig
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_ITConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_ADCCLKConfig
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_LSICmd
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_AHBPeriphClockCmd
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_APB1PeriphClockCmd
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_APB2PeriphResetCmd
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_APB1PeriphResetCmd
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_ClockSecuritySystemCmd
                0x0000000000000000       0x24 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_MCOConfig
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_GetFlagStatus
                0x0000000000000000       0x20 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_WaitForHSEStartUp
                0x0000000000000000       0x42 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_ClearFlag
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_GetITStatus
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_ClearITPendingBit
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_rcc.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_spi.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_spi.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_I2S_DeInit
                0x0000000000000000       0x32 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_Init
                0x0000000000000000       0x32 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_StructInit
                0x0000000000000000       0x22 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_Cmd  0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_I2S_ITConfig
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_I2S_DMACmd
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_I2S_SendData
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_I2S_ReceiveData
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_NSSInternalSoftwareConfig
                0x0000000000000000       0x24 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_SSOutputCmd
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_DataSizeConfig
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_TransmitCRC
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_CalculateCRC
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_GetCRC
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_GetCRCPolynomial
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_BiDirectionalLineConfig
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_I2S_GetFlagStatus
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_I2S_ClearFlag
                0x0000000000000000        0xc ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_I2S_GetITStatus
                0x0000000000000000       0x28 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_I2S_ClearITPendingBit
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_spi.o
 .debug_info    0x0000000000000000     0x1068 ./Peripheral/src/ch32v00x_spi.o
 .debug_abbrev  0x0000000000000000      0x303 ./Peripheral/src/ch32v00x_spi.o
 .debug_loc     0x0000000000000000      0x429 ./Peripheral/src/ch32v00x_spi.o
 .debug_aranges
                0x0000000000000000       0xb8 ./Peripheral/src/ch32v00x_spi.o
 .debug_ranges  0x0000000000000000       0xa8 ./Peripheral/src/ch32v00x_spi.o
 .debug_line    0x0000000000000000      0xa7d ./Peripheral/src/ch32v00x_spi.o
 .debug_str     0x0000000000000000      0x949 ./Peripheral/src/ch32v00x_spi.o
 .comment       0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_spi.o
 .debug_frame   0x0000000000000000      0x15c ./Peripheral/src/ch32v00x_spi.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_tim.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_tim.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_tim.o
 .text.TI1_Config
                0x0000000000000000       0x5a ./Peripheral/src/ch32v00x_tim.o
 .text.TI2_Config
                0x0000000000000000       0x70 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_DeInit
                0x0000000000000000       0x5c ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC2Init
                0x0000000000000000       0x96 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC3Init
                0x0000000000000000       0x94 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC4Init
                0x0000000000000000       0x6e ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_BDTRConfig
                0x0000000000000000       0x20 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_TimeBaseStructInit
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OCStructInit
                0x0000000000000000       0x22 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ICStructInit
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_BDTRStructInit
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ITConfig
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_GenerateEvent
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_DMAConfig
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_DMACmd
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_InternalClockConfig
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ITRxExternalClockConfig
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_TIxExternalClockConfig
                0x0000000000000000       0x48 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ETRConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ETRClockMode1Config
                0x0000000000000000       0x2a ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ETRClockMode2Config
                0x0000000000000000       0x22 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_PrescalerConfig
                0x0000000000000000        0x6 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_CounterModeConfig
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SelectInputTrigger
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_EncoderInterfaceConfig
                0x0000000000000000       0x3c ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ForcedOC1Config
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ForcedOC2Config
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ForcedOC3Config
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ForcedOC4Config
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SelectCOM
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SelectCCDMA
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_CCPreloadControl
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC2PreloadConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC3PreloadConfig
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC4PreloadConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC1FastConfig
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC2FastConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC3FastConfig
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC4FastConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ClearOC1Ref
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ClearOC2Ref
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ClearOC3Ref
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ClearOC4Ref
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC1PolarityConfig
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC1NPolarityConfig
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC2PolarityConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC2NPolarityConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC3PolarityConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC3NPolarityConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC4PolarityConfig
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_CCxCmd
                0x0000000000000000       0x20 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_CCxNCmd
                0x0000000000000000       0x20 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SelectOCxM
                0x0000000000000000       0x4c ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_UpdateDisableConfig
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_UpdateRequestConfig
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SelectHallSensor
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SelectOnePulseMode
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SelectOutputTrigger
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SelectSlaveMode
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SelectMasterSlaveMode
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SetCounter
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SetAutoreload
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SetCompare2
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SetCompare3
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SetCompare4
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SetIC1Prescaler
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SetIC2Prescaler
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_PWMIConfig
                0x0000000000000000       0xa4 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SetIC3Prescaler
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SetIC4Prescaler
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ICInit
                0x0000000000000000      0x148 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SetClockDivision
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_GetCapture1
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_GetCapture2
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_GetCapture3
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_GetCapture4
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_GetCounter
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_GetPrescaler
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_GetFlagStatus
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ClearFlag
                0x0000000000000000        0xc ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_GetITStatus
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ClearITPendingBit
                0x0000000000000000        0xc ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_IndicateCaptureLevelCmd
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_tim.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_usart.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_usart.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_DeInit
                0x0000000000000000       0x36 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_StructInit
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_ClockInit
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_usart.o
 .text.USART_ClockStructInit
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_ITConfig
                0x0000000000000000       0x36 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_DMACmd
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_SetAddress
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_WakeUpConfig
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_ReceiverWakeUpCmd
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_LINBreakDetectLengthConfig
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_LINCmd
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_ReceiveData
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_SendBreak
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_usart.o
 .text.USART_SetGuardTime
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_SetPrescaler
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_SmartCardCmd
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_usart.o
 .text.USART_SmartCardNACKCmd
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_HalfDuplexCmd
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_OverSampling8Cmd
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_OneBitMethodCmd
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_usart.o
 .text.USART_IrDAConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_IrDACmd
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_ClearFlag
                0x0000000000000000        0xc ./Peripheral/src/ch32v00x_usart.o
 .text.USART_GetITStatus
                0x0000000000000000       0x3c ./Peripheral/src/ch32v00x_usart.o
 .text.USART_ClearITPendingBit
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_usart.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_wwdg.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_wwdg.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_wwdg.o
 .text.WWDG_DeInit
                0x0000000000000000       0x2e ./Peripheral/src/ch32v00x_wwdg.o
 .text.WWDG_SetPrescaler
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_wwdg.o
 .text.WWDG_SetWindowValue
                0x0000000000000000       0x26 ./Peripheral/src/ch32v00x_wwdg.o
 .text.WWDG_EnableIT
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_wwdg.o
 .text.WWDG_SetCounter
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_wwdg.o
 .text.WWDG_Enable
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_wwdg.o
 .text.WWDG_GetFlagStatus
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_wwdg.o
 .text.WWDG_ClearFlag
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_wwdg.o
 .debug_info    0x0000000000000000      0xb27 ./Peripheral/src/ch32v00x_wwdg.o
 .debug_abbrev  0x0000000000000000      0x29d ./Peripheral/src/ch32v00x_wwdg.o
 .debug_loc     0x0000000000000000       0xae ./Peripheral/src/ch32v00x_wwdg.o
 .debug_aranges
                0x0000000000000000       0x58 ./Peripheral/src/ch32v00x_wwdg.o
 .debug_ranges  0x0000000000000000       0x48 ./Peripheral/src/ch32v00x_wwdg.o
 .debug_line    0x0000000000000000      0x53f ./Peripheral/src/ch32v00x_wwdg.o
 .debug_str     0x0000000000000000      0x6c2 ./Peripheral/src/ch32v00x_wwdg.o
 .comment       0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_wwdg.o
 .debug_frame   0x0000000000000000       0xa4 ./Peripheral/src/ch32v00x_wwdg.o
 .text          0x0000000000000000        0x0 ./Debug/debug.o
 .data          0x0000000000000000        0x0 ./Debug/debug.o
 .bss           0x0000000000000000        0x0 ./Debug/debug.o
 .text.Delay_Us
                0x0000000000000000       0x4a ./Debug/debug.o
 .text.SDI_Printf_Enable
                0x0000000000000000       0x2a ./Debug/debug.o
 .text._sbrk    0x0000000000000000       0x2e ./Debug/debug.o
 .data.curbrk.4091
                0x0000000000000000        0x4 ./Debug/debug.o
 .text          0x0000000000000000        0x0 ./Core/core_riscv.o
 .data          0x0000000000000000        0x0 ./Core/core_riscv.o
 .bss           0x0000000000000000        0x0 ./Core/core_riscv.o
 .text.__get_MSTATUS
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__set_MSTATUS
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__get_MISA
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__set_MISA
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__get_MTVEC
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__set_MTVEC
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__get_MSCRATCH
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__set_MSCRATCH
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__get_MEPC
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__set_MEPC
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__get_MCAUSE
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__set_MCAUSE
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__get_MVENDORID
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__get_MARCHID
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__get_MIMPID
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__get_MHARTID
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__get_SP
                0x0000000000000000        0x4 ./Core/core_riscv.o
 .highcode      0x0000000000000000        0xa ./Core/core_riscv.o
 .debug_info    0x0000000000000000      0x349 ./Core/core_riscv.o
 .debug_abbrev  0x0000000000000000      0x10d ./Core/core_riscv.o
 .debug_aranges
                0x0000000000000000       0xa8 ./Core/core_riscv.o
 .debug_ranges  0x0000000000000000       0x98 ./Core/core_riscv.o
 .debug_line    0x0000000000000000      0x488 ./Core/core_riscv.o
 .debug_str     0x0000000000000000      0x294 ./Core/core_riscv.o
 .comment       0x0000000000000000       0x34 ./Core/core_riscv.o
 .debug_frame   0x0000000000000000      0x130 ./Core/core_riscv.o
 .text          0x0000000000000000        0x0 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
 .data          0x0000000000000000        0x0 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
 .text.sprintf  0x0000000000000000       0x2c /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
 .text.putchar  0x0000000000000000       0x1c /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
 .data          0x0000000000000000        0x0 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(save-restore.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(save-restore.o)
 .eh_frame      0x0000000000000000       0x68 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(save-restore.o)
 .data          0x0000000000000000        0x0 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(muldi3.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(muldi3.o)
 .data          0x0000000000000000        0x0 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(div.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(div.o)
 .text          0x0000000000000000        0x0 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libg_nano.a(lib_a-memcpy.o)
 .data          0x0000000000000000        0x0 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libg_nano.a(lib_a-memcpy.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libg_nano.a(lib_a-memcpy.o)
 .data          0x0000000000000000        0x0 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libg_nano.a(lib_a-memset.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libg_nano.a(lib_a-memset.o)

Memory Configuration

Name             Origin             Length             Attributes
FLASH            0x0000000000000000 0x0000000000004000 xr
RAM              0x0000000020000000 0x0000000000000800 xrw
*default*        0x0000000000000000 0xffffffffffffffff

Linker script and memory map

LOAD ./User/adc_config.o
LOAD ./User/adc_display.o
LOAD ./User/ch32v00x_it.o
LOAD ./User/display_control.o
LOAD ./User/main.o
LOAD ./User/pwm_config.o
LOAD ./User/st7735.o
LOAD ./User/system_ch32v00x.o
LOAD ./User/touch_button.o
LOAD ./Startup/startup_ch32v00x.o
LOAD ./Peripheral/src/ch32v00x_adc.o
LOAD ./Peripheral/src/ch32v00x_dbgmcu.o
LOAD ./Peripheral/src/ch32v00x_dma.o
LOAD ./Peripheral/src/ch32v00x_exti.o
LOAD ./Peripheral/src/ch32v00x_flash.o
LOAD ./Peripheral/src/ch32v00x_gpio.o
LOAD ./Peripheral/src/ch32v00x_i2c.o
LOAD ./Peripheral/src/ch32v00x_iwdg.o
LOAD ./Peripheral/src/ch32v00x_misc.o
LOAD ./Peripheral/src/ch32v00x_opa.o
LOAD ./Peripheral/src/ch32v00x_pwr.o
LOAD ./Peripheral/src/ch32v00x_rcc.o
LOAD ./Peripheral/src/ch32v00x_spi.o
LOAD ./Peripheral/src/ch32v00x_tim.o
LOAD ./Peripheral/src/ch32v00x_usart.o
LOAD ./Peripheral/src/ch32v00x_wwdg.o
LOAD ./Debug/debug.o
LOAD ./Core/core_riscv.o
LOAD /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a
LOAD /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a
LOAD /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libg_nano.a
LOAD /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libc_nano.a
LOAD /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a
START GROUP
LOAD /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a
LOAD /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libc_nano.a
LOAD /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libnosys.a
END GROUP
START GROUP
LOAD /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a
LOAD /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libc_nano.a
LOAD /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libnosys.a
END GROUP
                0x0000000000000100                __stack_size = 0x100
                [!provide]                        PROVIDE (_stack_size = __stack_size)

.init           0x0000000000000000       0xa0
                0x0000000000000000                _sinit = .
                0x0000000000000000                . = ALIGN (0x4)
 *(SORT_NONE(.init))
 .init          0x0000000000000000       0x9e ./Startup/startup_ch32v00x.o
                0x0000000000000000                _start
                0x00000000000000a0                . = ALIGN (0x4)
 *fill*         0x000000000000009e        0x2 
                0x00000000000000a0                _einit = .

.highcodelalign
                0x00000000000000a0        0x0
                0x00000000000000a0                . = ALIGN (0x4)
                0x00000000000000a0                PROVIDE (_highcode_lma = .)

.highcode       0x0000000020000000        0x0 load address 0x00000000000000a0
                0x0000000020000000                . = ALIGN (0x4)
                0x0000000020000000                PROVIDE (_highcode_vma_start = .)
 *(.highcode)
 *(.highcode.*)
                0x0000000020000000                . = ALIGN (0x4)
                0x0000000020000000                PROVIDE (_highcode_vma_end = .)

.text           0x00000000000000a0     0x26c8
                0x00000000000000a0                . = ALIGN (0x4)
 *(.text)
 .text          0x00000000000000a0       0x14 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(save-restore.o)
                0x00000000000000a0                __riscv_save_1
                0x00000000000000a0                __riscv_save_2
                0x00000000000000a0                __riscv_save_0
                0x00000000000000aa                __riscv_restore_2
                0x00000000000000aa                __riscv_restore_0
                0x00000000000000aa                __riscv_restore_1
 .text          0x00000000000000b4       0x16 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(muldi3.o)
                0x00000000000000b4                __mulsi3
 *fill*         0x00000000000000ca        0x2 
 .text          0x00000000000000cc       0x7e /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(div.o)
                0x00000000000000cc                __divsi3
                0x00000000000000d4                __udivsi3
                0x0000000000000100                __umodsi3
                0x0000000000000124                __modsi3
 .text          0x000000000000014a       0xa8 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libg_nano.a(lib_a-memset.o)
                0x000000000000014a                memset
 *(.text.*)
 .text.ADC_GPIO_Config
                0x00000000000001f2       0x30 ./User/adc_config.o
                0x00000000000001f2                ADC_GPIO_Config
 .text.ADC_Config_Init
                0x0000000000000222       0x80 ./User/adc_config.o
                0x0000000000000222                ADC_Config_Init
 .text.ADC_Read_Channel
                0x00000000000002a2       0x46 ./User/adc_config.o
                0x00000000000002a2                ADC_Read_Channel
 .text.ADC_Read_All_Channels
                0x00000000000002e8       0x5a ./User/adc_config.o
                0x00000000000002e8                ADC_Read_All_Channels
 .text.ADC_Display_Draw_Header
                0x0000000000000342       0x28 ./User/adc_display.o
                0x0000000000000342                ADC_Display_Draw_Header
 .text.ADC_Display_Draw_Channel_Labels
                0x000000000000036a       0x5c ./User/adc_display.o
                0x000000000000036a                ADC_Display_Draw_Channel_Labels
 .text.ADC_Display_Init
                0x00000000000003c6       0x26 ./User/adc_display.o
                0x00000000000003c6                ADC_Display_Init
 .text.ADC_Display_Draw_Channel_Data
                0x00000000000003ec       0xf6 ./User/adc_display.o
                0x00000000000003ec                ADC_Display_Draw_Channel_Data
 .text.ADC_Display_Draw_All_Channels
                0x00000000000004e2       0x1c ./User/adc_display.o
                0x00000000000004e2                ADC_Display_Draw_All_Channels
 .text.ADC_Display_Clear_Values_Area
                0x00000000000004fe       0x1a ./User/adc_display.o
                0x00000000000004fe                ADC_Display_Clear_Values_Area
 .text.ADC_Display_Should_Update
                0x0000000000000518       0x20 ./User/adc_display.o
                0x0000000000000518                ADC_Display_Should_Update
 .text.ADC_Display_Update
                0x0000000000000538       0x36 ./User/adc_display.o
                0x0000000000000538                ADC_Display_Update
 .text.NMI_Handler
                0x000000000000056e        0x2 ./User/ch32v00x_it.o
                0x000000000000056e                NMI_Handler
 .text.HardFault_Handler
                0x0000000000000570       0x10 ./User/ch32v00x_it.o
                0x0000000000000570                HardFault_Handler
 .text.EXTI7_0_IRQHandler
                0x0000000000000580        0x8 ./User/ch32v00x_it.o
                0x0000000000000580                EXTI7_0_IRQHandler
 .text.SysTick_Handler
                0x0000000000000588        0xe ./User/ch32v00x_it.o
                0x0000000000000588                SysTick_Handler
 .text.Display_Control_Turn_On
                0x0000000000000596       0x16 ./User/display_control.o
                0x0000000000000596                Display_Control_Turn_On
 .text.Display_Control_Turn_Off
                0x00000000000005ac       0x1a ./User/display_control.o
                0x00000000000005ac                Display_Control_Turn_Off
 .text.Display_Control_Toggle
                0x00000000000005c6       0x18 ./User/display_control.o
                0x00000000000005c6                Display_Control_Toggle
 .text.Display_Control_Is_On
                0x00000000000005de        0xc ./User/display_control.o
                0x00000000000005de                Display_Control_Is_On
 .text.Display_Control_Clear_Screen
                0x00000000000005ea       0x18 ./User/display_control.o
                0x00000000000005ea                Display_Control_Clear_Screen
 .text.Display_Control_Show_Startup_Message
                0x0000000000000602       0x74 ./User/display_control.o
                0x0000000000000602                Display_Control_Show_Startup_Message
 .text.Display_Control_Init
                0x0000000000000676       0x26 ./User/display_control.o
                0x0000000000000676                Display_Control_Init
 .text.Display_Control_Show_Off_Message
                0x000000000000069c       0x2a ./User/display_control.o
                0x000000000000069c                Display_Control_Show_Off_Message
 .text.Display_Control_Update
                0x00000000000006c6       0x40 ./User/display_control.o
                0x00000000000006c6                Display_Control_Update
 .text.System_Init
                0x0000000000000706       0x62 ./User/main.o
                0x0000000000000706                System_Init
 .text.startup.main
                0x0000000000000768       0xd2 ./User/main.o
                0x0000000000000768                main
 .text.PWM_GPIO_Config
                0x000000000000083a       0x36 ./User/pwm_config.o
                0x000000000000083a                PWM_GPIO_Config
 .text.PWM_Timer_Config
                0x0000000000000870       0x9a ./User/pwm_config.o
                0x0000000000000870                PWM_Timer_Config
 .text.PWM_Set_Brightness
                0x000000000000090a       0x2c ./User/pwm_config.o
                0x000000000000090a                PWM_Set_Brightness
 .text.PWM_Config_Init
                0x0000000000000936       0x22 ./User/pwm_config.o
                0x0000000000000936                PWM_Config_Init
 .text.PWM_Update_Fade
                0x0000000000000958       0x52 ./User/pwm_config.o
                0x0000000000000958                PWM_Update_Fade
 .text.PWM_Turn_On
                0x00000000000009aa       0x10 ./User/pwm_config.o
                0x00000000000009aa                PWM_Turn_On
 .text.PWM_Turn_Off
                0x00000000000009ba        0xe ./User/pwm_config.o
                0x00000000000009ba                PWM_Turn_Off
 .text.PWM_Get_Brightness
                0x00000000000009c8        0x6 ./User/pwm_config.o
                0x00000000000009c8                PWM_Get_Brightness
 .text.SPI_send_DMA
                0x00000000000009ce       0x3e ./User/st7735.o
 .text.SPI_send
                0x0000000000000a0c       0x12 ./User/st7735.o
 .text.write_command_8
                0x0000000000000a1e       0x16 ./User/st7735.o
 .text.write_data_16
                0x0000000000000a34       0x20 ./User/st7735.o
 .text.tft_set_window
                0x0000000000000a54       0x3c ./User/st7735.o
 .text.tft_init
                0x0000000000000a90      0x190 ./User/st7735.o
                0x0000000000000a90                tft_init
 .text.tft_set_cursor
                0x0000000000000c20        0xe ./User/st7735.o
                0x0000000000000c20                tft_set_cursor
 .text.tft_set_color
                0x0000000000000c2e        0xa ./User/st7735.o
                0x0000000000000c2e                tft_set_color
 .text.tft_set_background_color
                0x0000000000000c38        0x6 ./User/st7735.o
                0x0000000000000c38                tft_set_background_color
 .text.tft_print_char
                0x0000000000000c3e       0xe4 ./User/st7735.o
                0x0000000000000c3e                tft_print_char
 .text.tft_print
                0x0000000000000d22       0x20 ./User/st7735.o
                0x0000000000000d22                tft_print
 .text.tft_print_number
                0x0000000000000d42       0xaa ./User/st7735.o
                0x0000000000000d42                tft_print_number
 .text.tft_fill_rect
                0x0000000000000dec       0x8e ./User/st7735.o
                0x0000000000000dec                tft_fill_rect
 .text.SystemInit
                0x0000000000000e7a      0x134 ./User/system_ch32v00x.o
                0x0000000000000e7a                SystemInit
 .text.SystemCoreClockUpdate
                0x0000000000000fae       0x6c ./User/system_ch32v00x.o
                0x0000000000000fae                SystemCoreClockUpdate
 .text.Touch_Button_GPIO_Config
                0x000000000000101a       0x32 ./User/touch_button.o
                0x000000000000101a                Touch_Button_GPIO_Config
 .text.Touch_Button_EXTI_Config
                0x000000000000104c       0x46 ./User/touch_button.o
                0x000000000000104c                Touch_Button_EXTI_Config
 .text.Touch_Button_Init
                0x0000000000001092       0x30 ./User/touch_button.o
                0x0000000000001092                Touch_Button_Init
 .text.Touch_Button_Update
                0x00000000000010c2       0x72 ./User/touch_button.o
                0x00000000000010c2                Touch_Button_Update
 .text.Touch_Button_Get_Event
                0x0000000000001134        0xc ./User/touch_button.o
                0x0000000000001134                Touch_Button_Get_Event
 .text.Touch_Button_Get_Time_Ms
                0x0000000000001140        0x6 ./User/touch_button.o
                0x0000000000001140                Touch_Button_Get_Time_Ms
 .text.Touch_Button_IRQ_Handler
                0x0000000000001146       0x4c ./User/touch_button.o
                0x0000000000001146                Touch_Button_IRQ_Handler
 .text.vector_handler
                0x0000000000001192        0x2 ./Startup/startup_ch32v00x.o
                0x0000000000001192                TIM1_CC_IRQHandler
                0x0000000000001192                PVD_IRQHandler
                0x0000000000001192                SPI1_IRQHandler
                0x0000000000001192                AWU_IRQHandler
                0x0000000000001192                DMA1_Channel4_IRQHandler
                0x0000000000001192                ADC1_IRQHandler
                0x0000000000001192                DMA1_Channel7_IRQHandler
                0x0000000000001192                I2C1_EV_IRQHandler
                0x0000000000001192                DMA1_Channel6_IRQHandler
                0x0000000000001192                RCC_IRQHandler
                0x0000000000001192                TIM1_TRG_COM_IRQHandler
                0x0000000000001192                DMA1_Channel1_IRQHandler
                0x0000000000001192                DMA1_Channel5_IRQHandler
                0x0000000000001192                DMA1_Channel3_IRQHandler
                0x0000000000001192                TIM1_UP_IRQHandler
                0x0000000000001192                WWDG_IRQHandler
                0x0000000000001192                TIM2_IRQHandler
                0x0000000000001192                SW_Handler
                0x0000000000001192                TIM1_BRK_IRQHandler
                0x0000000000001192                DMA1_Channel2_IRQHandler
                0x0000000000001192                FLASH_IRQHandler
                0x0000000000001192                USART1_IRQHandler
                0x0000000000001192                I2C1_ER_IRQHandler
 .text.handle_reset
                0x0000000000001194       0xb0 ./Startup/startup_ch32v00x.o
                0x0000000000001194                handle_reset
 .text.ADC_Init
                0x0000000000001244       0x4e ./Peripheral/src/ch32v00x_adc.o
                0x0000000000001244                ADC_Init
 .text.ADC_Cmd  0x0000000000001292       0x10 ./Peripheral/src/ch32v00x_adc.o
                0x0000000000001292                ADC_Cmd
 .text.ADC_ResetCalibration
                0x00000000000012a2        0xa ./Peripheral/src/ch32v00x_adc.o
                0x00000000000012a2                ADC_ResetCalibration
 .text.ADC_GetResetCalibrationStatus
                0x00000000000012ac        0x8 ./Peripheral/src/ch32v00x_adc.o
                0x00000000000012ac                ADC_GetResetCalibrationStatus
 .text.ADC_StartCalibration
                0x00000000000012b4        0xa ./Peripheral/src/ch32v00x_adc.o
                0x00000000000012b4                ADC_StartCalibration
 .text.ADC_GetCalibrationStatus
                0x00000000000012be        0x8 ./Peripheral/src/ch32v00x_adc.o
                0x00000000000012be                ADC_GetCalibrationStatus
 .text.ADC_SoftwareStartConvCmd
                0x00000000000012c6       0x18 ./Peripheral/src/ch32v00x_adc.o
                0x00000000000012c6                ADC_SoftwareStartConvCmd
 .text.ADC_RegularChannelConfig
                0x00000000000012de       0xba ./Peripheral/src/ch32v00x_adc.o
                0x00000000000012de                ADC_RegularChannelConfig
 .text.ADC_GetConversionValue
                0x0000000000001398        0x8 ./Peripheral/src/ch32v00x_adc.o
                0x0000000000001398                ADC_GetConversionValue
 .text.ADC_GetFlagStatus
                0x00000000000013a0        0xa ./Peripheral/src/ch32v00x_adc.o
                0x00000000000013a0                ADC_GetFlagStatus
 .text.ADC_ClearFlag
                0x00000000000013aa        0x8 ./Peripheral/src/ch32v00x_adc.o
                0x00000000000013aa                ADC_ClearFlag
 .text.DBGMCU_GetCHIPID
                0x00000000000013b2        0xa ./Peripheral/src/ch32v00x_dbgmcu.o
                0x00000000000013b2                DBGMCU_GetCHIPID
 .text.EXTI_Init
                0x00000000000013bc       0x6a ./Peripheral/src/ch32v00x_exti.o
                0x00000000000013bc                EXTI_Init
 .text.EXTI_GetITStatus
                0x0000000000001426       0x1e ./Peripheral/src/ch32v00x_exti.o
                0x0000000000001426                EXTI_GetITStatus
 .text.EXTI_ClearITPendingBit
                0x0000000000001444        0xa ./Peripheral/src/ch32v00x_exti.o
                0x0000000000001444                EXTI_ClearITPendingBit
 .text.GPIO_Init
                0x000000000000144e       0x7c ./Peripheral/src/ch32v00x_gpio.o
                0x000000000000144e                GPIO_Init
 .text.GPIO_ReadInputDataBit
                0x00000000000014ca        0xa ./Peripheral/src/ch32v00x_gpio.o
                0x00000000000014ca                GPIO_ReadInputDataBit
 .text.GPIO_EXTILineConfig
                0x00000000000014d4       0x22 ./Peripheral/src/ch32v00x_gpio.o
                0x00000000000014d4                GPIO_EXTILineConfig
 .text.NVIC_PriorityGroupConfig
                0x00000000000014f6        0x6 ./Peripheral/src/ch32v00x_misc.o
                0x00000000000014f6                NVIC_PriorityGroupConfig
 .text.NVIC_Init
                0x00000000000014fc       0x62 ./Peripheral/src/ch32v00x_misc.o
                0x00000000000014fc                NVIC_Init
 .text.RCC_AdjustHSICalibrationValue
                0x000000000000155e       0x12 ./Peripheral/src/ch32v00x_rcc.o
                0x000000000000155e                RCC_AdjustHSICalibrationValue
 .text.RCC_GetClocksFreq
                0x0000000000001570       0xa2 ./Peripheral/src/ch32v00x_rcc.o
                0x0000000000001570                RCC_GetClocksFreq
 .text.RCC_APB2PeriphClockCmd
                0x0000000000001612       0x1e ./Peripheral/src/ch32v00x_rcc.o
                0x0000000000001612                RCC_APB2PeriphClockCmd
 .text.TIM_TimeBaseInit
                0x0000000000001630       0x4e ./Peripheral/src/ch32v00x_tim.o
                0x0000000000001630                TIM_TimeBaseInit
 .text.TIM_OC1Init
                0x000000000000167e       0x6a ./Peripheral/src/ch32v00x_tim.o
                0x000000000000167e                TIM_OC1Init
 .text.TIM_Cmd  0x00000000000016e8       0x18 ./Peripheral/src/ch32v00x_tim.o
                0x00000000000016e8                TIM_Cmd
 .text.TIM_CtrlPWMOutputs
                0x0000000000001700       0x16 ./Peripheral/src/ch32v00x_tim.o
                0x0000000000001700                TIM_CtrlPWMOutputs
 .text.TIM_ARRPreloadConfig
                0x0000000000001716       0x1a ./Peripheral/src/ch32v00x_tim.o
                0x0000000000001716                TIM_ARRPreloadConfig
 .text.TIM_OC1PreloadConfig
                0x0000000000001730        0xe ./Peripheral/src/ch32v00x_tim.o
                0x0000000000001730                TIM_OC1PreloadConfig
 .text.TIM_SetCompare1
                0x000000000000173e        0x4 ./Peripheral/src/ch32v00x_tim.o
                0x000000000000173e                TIM_SetCompare1
 .text.USART_Init
                0x0000000000001742       0xcc ./Peripheral/src/ch32v00x_usart.o
                0x0000000000001742                USART_Init
 .text.USART_Cmd
                0x000000000000180e       0x16 ./Peripheral/src/ch32v00x_usart.o
                0x000000000000180e                USART_Cmd
 .text.USART_SendData
                0x0000000000001824        0x8 ./Peripheral/src/ch32v00x_usart.o
                0x0000000000001824                USART_SendData
 .text.USART_GetFlagStatus
                0x000000000000182c        0xa ./Peripheral/src/ch32v00x_usart.o
                0x000000000000182c                USART_GetFlagStatus
 .text.Delay_Init
                0x0000000000001836       0x34 ./Debug/debug.o
                0x0000000000001836                Delay_Init
 .text.Delay_Ms
                0x000000000000186a       0x3a ./Debug/debug.o
                0x000000000000186a                Delay_Ms
 .text.USART_Printf_Init
                0x00000000000018a4       0x58 ./Debug/debug.o
                0x00000000000018a4                USART_Printf_Init
 .text._write   0x00000000000018fc       0x4c ./Debug/debug.o
                0x00000000000018fc                _write
 .text.printchar
                0x0000000000001948       0x48 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
                0x0000000000001948                printchar
 .text.prints   0x0000000000001990       0xe6 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
                0x0000000000001990                prints
 .text.printInt
                0x0000000000001a76      0x118 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
                0x0000000000001a76                printInt
 .text.printLongLongInt
                0x0000000000001b8e        0x4 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
                0x0000000000001b8e                printLongLongInt
 .text.printDouble
                0x0000000000001b92        0x4 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
                0x0000000000001b92                printDouble
 .text.print    0x0000000000001b96      0x360 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
                0x0000000000001b96                print
 .text.printf   0x0000000000001ef6       0x24 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
                0x0000000000001ef6                printf
 .text.snprintf
                0x0000000000001f1a       0x2a /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
                0x0000000000001f1a                snprintf
 .text.puts     0x0000000000001f44       0x4a /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
                0x0000000000001f44                puts
 .text.memcpy   0x0000000000001f8e       0xd2 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libg_nano.a(lib_a-memcpy.o)
                0x0000000000001f8e                memcpy
 *(.rodata)
 *fill*         0x0000000000002060        0x0 
 .rodata        0x0000000000002060       0x20 ./User/st7735.o
 *(.rodata*)
 .rodata.ADC_Display_Draw_Channel_Data.str1.4
                0x0000000000002080       0x11 ./User/adc_display.o
 *fill*         0x0000000000002091        0x3 
 .rodata.ADC_Display_Draw_Channel_Labels.str1.4
                0x0000000000002094        0x6 ./User/adc_display.o
 *fill*         0x000000000000209a        0x2 
 .rodata.ADC_Display_Draw_Header.str1.4
                0x000000000000209c        0xc ./User/adc_display.o
 .rodata.CSWTCH.2
                0x00000000000020a8        0x8 ./User/adc_display.o
 .rodata.Display_Control_Show_Off_Message.str1.4
                0x00000000000020b0        0xc ./User/display_control.o
 .rodata.Display_Control_Show_Startup_Message.str1.4
                0x00000000000020bc       0x3d ./User/display_control.o
 *fill*         0x00000000000020f9        0x3 
 .rodata.System_Init.str1.4
                0x00000000000020fc       0xa0 ./User/main.o
 .rodata.main.str1.4
                0x000000000000219c       0xc2 ./User/main.o
 *fill*         0x000000000000225e        0x2 
 .rodata.font   0x0000000000002260      0x500 ./User/st7735.o
 .rodata.print.str1.4
                0x0000000000002760        0x8 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
                                          0x7 (size before relaxing)
 *(.gnu.linkonce.t.*)
                0x0000000000002768                . = ALIGN (0x4)

.rela.dyn       0x0000000000002768        0x0
 .rela.text.startup.main
                0x0000000000002768        0x0 ./User/adc_config.o
 .rela.text.Touch_Button_Init
                0x0000000000002768        0x0 ./User/adc_config.o
 .rela.init     0x0000000000002768        0x0 ./User/adc_config.o
 .rela.text.handle_reset
                0x0000000000002768        0x0 ./User/adc_config.o
 .rela.text.prints
                0x0000000000002768        0x0 ./User/adc_config.o
 .rela.text.printInt
                0x0000000000002768        0x0 ./User/adc_config.o
 .rela.text.print
                0x0000000000002768        0x0 ./User/adc_config.o
 .rela.text.printf
                0x0000000000002768        0x0 ./User/adc_config.o
 .rela.text.snprintf
                0x0000000000002768        0x0 ./User/adc_config.o

.fini           0x0000000000002768        0x0
 *(SORT_NONE(.fini))
                0x0000000000002768                . = ALIGN (0x4)
                [!provide]                        PROVIDE (_etext = .)
                [!provide]                        PROVIDE (_eitcm = .)

.preinit_array  0x0000000000002768        0x0
                [!provide]                        PROVIDE (__preinit_array_start = .)
 *(.preinit_array)
                [!provide]                        PROVIDE (__preinit_array_end = .)

.init_array     0x0000000000002768        0x0
                [!provide]                        PROVIDE (__init_array_start = .)
 *(SORT_BY_INIT_PRIORITY(.init_array.*) SORT_BY_INIT_PRIORITY(.ctors.*))
 *(.init_array EXCLUDE_FILE(*crtend?.o *crtend.o *crtbegin?.o *crtbegin.o) .ctors)
                [!provide]                        PROVIDE (__init_array_end = .)

.fini_array     0x0000000000002768        0x0
                [!provide]                        PROVIDE (__fini_array_start = .)
 *(SORT_BY_INIT_PRIORITY(.fini_array.*) SORT_BY_INIT_PRIORITY(.dtors.*))
 *(.fini_array EXCLUDE_FILE(*crtend?.o *crtend.o *crtbegin?.o *crtbegin.o) .dtors)
                [!provide]                        PROVIDE (__fini_array_end = .)

.ctors
 *crtbegin.o(.ctors)
 *crtbegin?.o(.ctors)
 *(EXCLUDE_FILE(*crtend?.o *crtend.o) .ctors)
 *(SORT_BY_NAME(.ctors.*))
 *(.ctors)

.dtors
 *crtbegin.o(.dtors)
 *crtbegin?.o(.dtors)
 *(EXCLUDE_FILE(*crtend?.o *crtend.o) .dtors)
 *(SORT_BY_NAME(.dtors.*))
 *(.dtors)

.dalign         0x0000000020000000        0x0 load address 0x0000000000002768
                0x0000000020000000                . = ALIGN (0x4)
                0x0000000020000000                PROVIDE (_data_vma = .)

.dlalign        0x0000000000002768        0x0
                0x0000000000002768                . = ALIGN (0x4)
                0x0000000000002768                PROVIDE (_data_lma = .)

.data           0x0000000020000000       0x40 load address 0x0000000000002768
                0x0000000020000000                . = ALIGN (0x4)
 *(.gnu.linkonce.r.*)
 *(.data .data.*)
 .data._color   0x0000000020000000        0x2 ./User/st7735.o
 *fill*         0x0000000020000002        0x2 
 .data.AHBPrescTable
                0x0000000020000004       0x10 ./User/system_ch32v00x.o
                0x0000000020000004                AHBPrescTable
 .data.SystemCoreClock
                0x0000000020000014        0x4 ./User/system_ch32v00x.o
                0x0000000020000014                SystemCoreClock
 .data.ADCPrescTable
                0x0000000020000018       0x14 ./Peripheral/src/ch32v00x_rcc.o
 .data.APBAHBPrescTable
                0x000000002000002c       0x10 ./Peripheral/src/ch32v00x_rcc.o
 *(.gnu.linkonce.d.*)
                0x0000000020000040                . = ALIGN (0x8)
 *fill*         0x000000002000003c        0x4 
                0x0000000020000840                PROVIDE (__global_pointer$ = (. + 0x800))
 *(.sdata .sdata.*)
 *(.sdata2*)
 *(.gnu.linkonce.s.*)
                0x0000000020000040                . = ALIGN (0x8)
 *(.srodata.cst16)
 *(.srodata.cst8)
 *(.srodata.cst4)
 *(.srodata.cst2)
 *(.srodata .srodata.*)
                0x0000000020000040                . = ALIGN (0x4)
                0x0000000020000040                PROVIDE (_edata = .)

.bss            0x0000000020000040      0x1ac load address 0x00000000000027a8
                0x0000000020000040                . = ALIGN (0x4)
                0x0000000020000040                PROVIDE (_sbss = .)
 *(.sbss*)
 *(.gnu.linkonce.sb.*)
 *(.bss*)
 .bss.adc_display_config
                0x0000000020000040        0xc ./User/adc_display.o
                0x0000000020000040                adc_display_config
 .bss.display_control
                0x000000002000004c        0xc ./User/display_control.o
                0x000000002000004c                display_control
 .bss.adc_data  0x0000000020000058       0x14 ./User/main.o
                0x0000000020000058                adc_data
 .bss.last_adc_read_time
                0x000000002000006c        0x4 ./User/main.o
                0x000000002000006c                last_adc_read_time
 .bss.system_initialized
                0x0000000020000070        0x1 ./User/main.o
                0x0000000020000070                system_initialized
 *fill*         0x0000000020000071        0x3 
 .bss.pwm_control
                0x0000000020000074        0x6 ./User/pwm_config.o
                0x0000000020000074                pwm_control
 .bss._bg_color
                0x000000002000007a        0x2 ./User/st7735.o
 .bss._buffer   0x000000002000007c      0x140 ./User/st7735.o
 .bss._cursor_x
                0x00000000200001bc        0x2 ./User/st7735.o
 .bss._cursor_y
                0x00000000200001be        0x2 ./User/st7735.o
 .bss.str.4169  0x00000000200001c0        0xc ./User/st7735.o
 .bss.system_tick_ms
                0x00000000200001cc        0x4 ./User/touch_button.o
                0x00000000200001cc                system_tick_ms
 .bss.touch_button
                0x00000000200001d0       0x14 ./User/touch_button.o
                0x00000000200001d0                touch_button
 .bss.NVIC_Priority_Group
                0x00000000200001e4        0x4 ./Peripheral/src/ch32v00x_misc.o
                0x00000000200001e4                NVIC_Priority_Group
 .bss.p_ms      0x00000000200001e8        0x2 ./Debug/debug.o
 .bss.p_us      0x00000000200001ea        0x1 ./Debug/debug.o
 *(.gnu.linkonce.b.*)
 *(COMMON*)
                0x00000000200001ec                . = ALIGN (0x4)
 *fill*         0x00000000200001eb        0x1 
                0x00000000200001ec                PROVIDE (_ebss = .)
                0x00000000200001ec                PROVIDE (_end = _ebss)
                [!provide]                        PROVIDE (end = .)

.stack          0x0000000020000700      0x100
                0x0000000020000700                PROVIDE (_heap_end = .)
                0x0000000020000700                . = ALIGN (0x4)
                [!provide]                        PROVIDE (_susrstack = .)
                0x0000000020000800                . = (. + __stack_size)
 *fill*         0x0000000020000700      0x100 
                0x0000000020000800                PROVIDE (_eusrstack = .)
OUTPUT(CH32V003F4U6.elf elf32-littleriscv)

.debug_info     0x0000000000000000    0x13da4
 .debug_info    0x0000000000000000     0x10bc ./User/adc_config.o
 .debug_info    0x00000000000010bc     0x12fe ./User/adc_display.o
 .debug_info    0x00000000000023ba      0xcf2 ./User/ch32v00x_it.o
 .debug_info    0x00000000000030ac      0xe8f ./User/display_control.o
 .debug_info    0x0000000000003f3b      0xfdb ./User/main.o
 .debug_info    0x0000000000004f16     0x11b1 ./User/pwm_config.o
 .debug_info    0x00000000000060c7     0x196b ./User/st7735.o
 .debug_info    0x0000000000007a32      0xc94 ./User/system_ch32v00x.o
 .debug_info    0x00000000000086c6     0x1071 ./User/touch_button.o
 .debug_info    0x0000000000009737       0x22 ./Startup/startup_ch32v00x.o
 .debug_info    0x0000000000009759     0x1526 ./Peripheral/src/ch32v00x_adc.o
 .debug_info    0x000000000000ac7f      0xa94 ./Peripheral/src/ch32v00x_dbgmcu.o
 .debug_info    0x000000000000b713      0xc05 ./Peripheral/src/ch32v00x_exti.o
 .debug_info    0x000000000000c318     0x1034 ./Peripheral/src/ch32v00x_gpio.o
 .debug_info    0x000000000000d34c      0xdc5 ./Peripheral/src/ch32v00x_misc.o
 .debug_info    0x000000000000e111     0x108c ./Peripheral/src/ch32v00x_rcc.o
 .debug_info    0x000000000000f19d     0x297f ./Peripheral/src/ch32v00x_tim.o
 .debug_info    0x0000000000011b1c     0x1351 ./Peripheral/src/ch32v00x_usart.o
 .debug_info    0x0000000000012e6d      0xf37 ./Debug/debug.o

.debug_abbrev   0x0000000000000000     0x388c
 .debug_abbrev  0x0000000000000000      0x327 ./User/adc_config.o
 .debug_abbrev  0x0000000000000327      0x383 ./User/adc_display.o
 .debug_abbrev  0x00000000000006aa      0x266 ./User/ch32v00x_it.o
 .debug_abbrev  0x0000000000000910      0x274 ./User/display_control.o
 .debug_abbrev  0x0000000000000b84      0x28b ./User/main.o
 .debug_abbrev  0x0000000000000e0f      0x34e ./User/pwm_config.o
 .debug_abbrev  0x000000000000115d      0x46e ./User/st7735.o
 .debug_abbrev  0x00000000000015cb      0x2ee ./User/system_ch32v00x.o
 .debug_abbrev  0x00000000000018b9      0x33c ./User/touch_button.o
 .debug_abbrev  0x0000000000001bf5       0x12 ./Startup/startup_ch32v00x.o
 .debug_abbrev  0x0000000000001c07      0x307 ./Peripheral/src/ch32v00x_adc.o
 .debug_abbrev  0x0000000000001f0e      0x2f7 ./Peripheral/src/ch32v00x_dbgmcu.o
 .debug_abbrev  0x0000000000002205      0x2da ./Peripheral/src/ch32v00x_exti.o
 .debug_abbrev  0x00000000000024df      0x309 ./Peripheral/src/ch32v00x_gpio.o
 .debug_abbrev  0x00000000000027e8      0x2e1 ./Peripheral/src/ch32v00x_misc.o
 .debug_abbrev  0x0000000000002ac9      0x389 ./Peripheral/src/ch32v00x_rcc.o
 .debug_abbrev  0x0000000000002e52      0x408 ./Peripheral/src/ch32v00x_tim.o
 .debug_abbrev  0x000000000000325a      0x323 ./Peripheral/src/ch32v00x_usart.o
 .debug_abbrev  0x000000000000357d      0x30f ./Debug/debug.o

.debug_loc      0x0000000000000000     0x5134
 .debug_loc     0x0000000000000000      0x146 ./User/adc_config.o
 .debug_loc     0x0000000000000146      0x53e ./User/adc_display.o
 .debug_loc     0x0000000000000684       0x48 ./User/main.o
 .debug_loc     0x00000000000006cc      0x178 ./User/pwm_config.o
 .debug_loc     0x0000000000000844      0xdf3 ./User/st7735.o
 .debug_loc     0x0000000000001637       0x55 ./User/system_ch32v00x.o
 .debug_loc     0x000000000000168c       0x9e ./User/touch_button.o
 .debug_loc     0x000000000000172a      0x9fa ./Peripheral/src/ch32v00x_adc.o
 .debug_loc     0x0000000000002124       0x7c ./Peripheral/src/ch32v00x_dbgmcu.o
 .debug_loc     0x00000000000021a0      0x181 ./Peripheral/src/ch32v00x_exti.o
 .debug_loc     0x0000000000002321      0x5e2 ./Peripheral/src/ch32v00x_gpio.o
 .debug_loc     0x0000000000002903       0x47 ./Peripheral/src/ch32v00x_misc.o
 .debug_loc     0x000000000000294a      0x5e3 ./Peripheral/src/ch32v00x_rcc.o
 .debug_loc     0x0000000000002f2d     0x1936 ./Peripheral/src/ch32v00x_tim.o
 .debug_loc     0x0000000000004863      0x735 ./Peripheral/src/ch32v00x_usart.o
 .debug_loc     0x0000000000004f98      0x19c ./Debug/debug.o

.debug_aranges  0x0000000000000000      0xb00
 .debug_aranges
                0x0000000000000000       0x50 ./User/adc_config.o
 .debug_aranges
                0x0000000000000050       0x78 ./User/adc_display.o
 .debug_aranges
                0x00000000000000c8       0x38 ./User/ch32v00x_it.o
 .debug_aranges
                0x0000000000000100       0x70 ./User/display_control.o
 .debug_aranges
                0x0000000000000170       0x28 ./User/main.o
 .debug_aranges
                0x0000000000000198       0x68 ./User/pwm_config.o
 .debug_aranges
                0x0000000000000200       0xb0 ./User/st7735.o
 .debug_aranges
                0x00000000000002b0       0x28 ./User/system_ch32v00x.o
 .debug_aranges
                0x00000000000002d8       0x60 ./User/touch_button.o
 .debug_aranges
                0x0000000000000338       0x30 ./Startup/startup_ch32v00x.o
 .debug_aranges
                0x0000000000000368      0x138 ./Peripheral/src/ch32v00x_adc.o
 .debug_aranges
                0x00000000000004a0       0x48 ./Peripheral/src/ch32v00x_dbgmcu.o
 .debug_aranges
                0x00000000000004e8       0x50 ./Peripheral/src/ch32v00x_exti.o
 .debug_aranges
                0x0000000000000538       0x98 ./Peripheral/src/ch32v00x_gpio.o
 .debug_aranges
                0x00000000000005d0       0x28 ./Peripheral/src/ch32v00x_misc.o
 .debug_aranges
                0x00000000000005f8       0xe0 ./Peripheral/src/ch32v00x_rcc.o
 .debug_aranges
                0x00000000000006d8      0x2d8 ./Peripheral/src/ch32v00x_tim.o
 .debug_aranges
                0x00000000000009b0      0x100 ./Peripheral/src/ch32v00x_usart.o
 .debug_aranges
                0x0000000000000ab0       0x50 ./Debug/debug.o

.debug_ranges   0x0000000000000000      0xc88
 .debug_ranges  0x0000000000000000       0x90 ./User/adc_config.o
 .debug_ranges  0x0000000000000090       0xf0 ./User/adc_display.o
 .debug_ranges  0x0000000000000180       0x28 ./User/ch32v00x_it.o
 .debug_ranges  0x00000000000001a8       0x60 ./User/display_control.o
 .debug_ranges  0x0000000000000208       0x38 ./User/main.o
 .debug_ranges  0x0000000000000240       0x70 ./User/pwm_config.o
 .debug_ranges  0x00000000000002b0      0x158 ./User/st7735.o
 .debug_ranges  0x0000000000000408       0x50 ./User/system_ch32v00x.o
 .debug_ranges  0x0000000000000458       0xa0 ./User/touch_button.o
 .debug_ranges  0x00000000000004f8       0x28 ./Startup/startup_ch32v00x.o
 .debug_ranges  0x0000000000000520      0x128 ./Peripheral/src/ch32v00x_adc.o
 .debug_ranges  0x0000000000000648       0x50 ./Peripheral/src/ch32v00x_dbgmcu.o
 .debug_ranges  0x0000000000000698       0x40 ./Peripheral/src/ch32v00x_exti.o
 .debug_ranges  0x00000000000006d8       0x88 ./Peripheral/src/ch32v00x_gpio.o
 .debug_ranges  0x0000000000000760       0x30 ./Peripheral/src/ch32v00x_misc.o
 .debug_ranges  0x0000000000000790       0xd0 ./Peripheral/src/ch32v00x_rcc.o
 .debug_ranges  0x0000000000000860      0x2f8 ./Peripheral/src/ch32v00x_tim.o
 .debug_ranges  0x0000000000000b58       0xf0 ./Peripheral/src/ch32v00x_usart.o
 .debug_ranges  0x0000000000000c48       0x40 ./Debug/debug.o

.debug_line     0x0000000000000000     0xd93e
 .debug_line    0x0000000000000000      0x808 ./User/adc_config.o
 .debug_line    0x0000000000000808      0x98f ./User/adc_display.o
 .debug_line    0x0000000000001197      0x3af ./User/ch32v00x_it.o
 .debug_line    0x0000000000001546      0x687 ./User/display_control.o
 .debug_line    0x0000000000001bcd      0x618 ./User/main.o
 .debug_line    0x00000000000021e5      0x86e ./User/pwm_config.o
 .debug_line    0x0000000000002a53     0x1493 ./User/st7735.o
 .debug_line    0x0000000000003ee6      0x77c ./User/system_ch32v00x.o
 .debug_line    0x0000000000004662      0x960 ./User/touch_button.o
 .debug_line    0x0000000000004fc2      0x148 ./Startup/startup_ch32v00x.o
 .debug_line    0x000000000000510a     0x14e8 ./Peripheral/src/ch32v00x_adc.o
 .debug_line    0x00000000000065f2      0x423 ./Peripheral/src/ch32v00x_dbgmcu.o
 .debug_line    0x0000000000006a15      0x67c ./Peripheral/src/ch32v00x_exti.o
 .debug_line    0x0000000000007091      0xca3 ./Peripheral/src/ch32v00x_gpio.o
 .debug_line    0x0000000000007d34      0x48a ./Peripheral/src/ch32v00x_misc.o
 .debug_line    0x00000000000081be      0xeaa ./Peripheral/src/ch32v00x_rcc.o
 .debug_line    0x0000000000009068     0x3105 ./Peripheral/src/ch32v00x_tim.o
 .debug_line    0x000000000000c16d      0xfb4 ./Peripheral/src/ch32v00x_usart.o
 .debug_line    0x000000000000d121      0x81d ./Debug/debug.o

.debug_str      0x0000000000000000     0x34c9
 .debug_str     0x0000000000000000      0x9b2 ./User/adc_config.o
                                        0xa4e (size before relaxing)
 .debug_str     0x00000000000009b2      0x41f ./User/adc_display.o
                                        0xa44 (size before relaxing)
 .debug_str     0x0000000000000dd1      0x12d ./User/ch32v00x_it.o
                                        0x811 (size before relaxing)
 .debug_str     0x0000000000000efe      0x27f ./User/display_control.o
                                        0x9d3 (size before relaxing)
 .debug_str     0x000000000000117d      0x10b ./User/main.o
                                        0xb12 (size before relaxing)
 .debug_str     0x0000000000001288      0x349 ./User/pwm_config.o
                                        0xb7b (size before relaxing)
 .debug_str     0x00000000000015d1      0x234 ./User/st7735.o
                                        0x98f (size before relaxing)
 .debug_str     0x0000000000001805       0xcd ./User/system_ch32v00x.o
                                        0x758 (size before relaxing)
 .debug_str     0x00000000000018d2      0x3fa ./User/touch_button.o
                                        0xd0a (size before relaxing)
 .debug_str     0x0000000000001ccc       0x2a ./Startup/startup_ch32v00x.o
                                         0x64 (size before relaxing)
 .debug_str     0x0000000000001cf6      0x3ae ./Peripheral/src/ch32v00x_adc.o
                                        0xb9a (size before relaxing)
 .debug_str     0x00000000000020a4       0x7e ./Peripheral/src/ch32v00x_dbgmcu.o
                                        0x656 (size before relaxing)
 .debug_str     0x0000000000002122       0xaf ./Peripheral/src/ch32v00x_exti.o
                                        0x79d (size before relaxing)
 .debug_str     0x00000000000021d1      0x19b ./Peripheral/src/ch32v00x_gpio.o
                                        0x953 (size before relaxing)
 .debug_str     0x000000000000236c       0x9c ./Peripheral/src/ch32v00x_misc.o
                                        0x91b (size before relaxing)
 .debug_str     0x0000000000002408      0x2c7 ./Peripheral/src/ch32v00x_rcc.o
                                        0x9c1 (size before relaxing)
 .debug_str     0x00000000000026cf      0x961 ./Peripheral/src/ch32v00x_tim.o
                                       0x127b (size before relaxing)
 .debug_str     0x0000000000003030      0x409 ./Peripheral/src/ch32v00x_usart.o
                                        0xb10 (size before relaxing)
 .debug_str     0x0000000000003439       0x90 ./Debug/debug.o
                                        0x911 (size before relaxing)

.comment        0x0000000000000000       0x33
 .comment       0x0000000000000000       0x33 ./User/adc_config.o
                                         0x34 (size before relaxing)
 .comment       0x0000000000000033       0x34 ./User/adc_display.o
 .comment       0x0000000000000033       0x34 ./User/ch32v00x_it.o
 .comment       0x0000000000000033       0x34 ./User/display_control.o
 .comment       0x0000000000000033       0x34 ./User/main.o
 .comment       0x0000000000000033       0x34 ./User/pwm_config.o
 .comment       0x0000000000000033       0x34 ./User/st7735.o
 .comment       0x0000000000000033       0x34 ./User/system_ch32v00x.o
 .comment       0x0000000000000033       0x34 ./User/touch_button.o
 .comment       0x0000000000000033       0x34 ./Peripheral/src/ch32v00x_adc.o
 .comment       0x0000000000000033       0x34 ./Peripheral/src/ch32v00x_dbgmcu.o
 .comment       0x0000000000000033       0x34 ./Peripheral/src/ch32v00x_exti.o
 .comment       0x0000000000000033       0x34 ./Peripheral/src/ch32v00x_gpio.o
 .comment       0x0000000000000033       0x34 ./Peripheral/src/ch32v00x_misc.o
 .comment       0x0000000000000033       0x34 ./Peripheral/src/ch32v00x_rcc.o
 .comment       0x0000000000000033       0x34 ./Peripheral/src/ch32v00x_tim.o
 .comment       0x0000000000000033       0x34 ./Peripheral/src/ch32v00x_usart.o
 .comment       0x0000000000000033       0x34 ./Debug/debug.o
 .comment       0x0000000000000033       0x34 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)

.debug_frame    0x0000000000000000     0x18e8
 .debug_frame   0x0000000000000000       0xf4 ./User/adc_config.o
 .debug_frame   0x00000000000000f4      0x180 ./User/adc_display.o
 .debug_frame   0x0000000000000274       0x50 ./User/ch32v00x_it.o
 .debug_frame   0x00000000000002c4      0x12c ./User/display_control.o
 .debug_frame   0x00000000000003f0       0x48 ./User/main.o
 .debug_frame   0x0000000000000438      0x108 ./User/pwm_config.o
 .debug_frame   0x0000000000000540      0x260 ./User/st7735.o
 .debug_frame   0x00000000000007a0       0x58 ./User/system_ch32v00x.o
 .debug_frame   0x00000000000007f8       0xe0 ./User/touch_button.o
 .debug_frame   0x00000000000008d8      0x26c ./Peripheral/src/ch32v00x_adc.o
 .debug_frame   0x0000000000000b44       0x70 ./Peripheral/src/ch32v00x_dbgmcu.o
 .debug_frame   0x0000000000000bb4       0x90 ./Peripheral/src/ch32v00x_exti.o
 .debug_frame   0x0000000000000c44      0x154 ./Peripheral/src/ch32v00x_gpio.o
 .debug_frame   0x0000000000000d98       0x30 ./Peripheral/src/ch32v00x_misc.o
 .debug_frame   0x0000000000000dc8      0x1dc ./Peripheral/src/ch32v00x_rcc.o
 .debug_frame   0x0000000000000fa4      0x624 ./Peripheral/src/ch32v00x_tim.o
 .debug_frame   0x00000000000015c8      0x204 ./Peripheral/src/ch32v00x_usart.o
 .debug_frame   0x00000000000017cc       0xe4 ./Debug/debug.o
 .debug_frame   0x00000000000018b0       0x38 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libg_nano.a(lib_a-memcpy.o)
