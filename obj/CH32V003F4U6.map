Archive member included to satisfy reference by file (symbol)

/opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
                              ./User/main.o (printf)
/opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(save-restore.o)
                              ./User/adc_config.o (__riscv_save_2)
/opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(muldi3.o)
                              ./User/adc_config.o (__mulsi3)
/opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(div.o)
                              ./User/adc_display.o (__divsi3)
/opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libg_nano.a(lib_a-memcpy.o)
                              ./User/st7735.o (memcpy)
/opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libg_nano.a(lib_a-memset.o)
                              ./User/adc_config.o (memset)

Discarded input sections

 .text          0x0000000000000000        0x0 ./User/adc_config.o
 .data          0x0000000000000000        0x0 ./User/adc_config.o
 .bss           0x0000000000000000        0x0 ./User/adc_config.o
 .text.ADC_GPIO_Config
                0x0000000000000000       0x3e ./User/adc_config.o
 .text.ADC_Config_Init
                0x0000000000000000       0xb6 ./User/adc_config.o
 .text.ADC_Read_Channel
                0x0000000000000000       0x64 ./User/adc_config.o
 .text.ADC_Read_All_Channels
                0x0000000000000000       0x82 ./User/adc_config.o
 .text.ADC_Convert_To_Voltage
                0x0000000000000000       0x24 ./User/adc_config.o
 .text.ADC_Start_Conversion
                0x0000000000000000       0x34 ./User/adc_config.o
 .text.ADC_Is_Conversion_Done
                0x0000000000000000       0x26 ./User/adc_config.o
 .debug_info    0x0000000000000000     0x10bc ./User/adc_config.o
 .debug_abbrev  0x0000000000000000      0x327 ./User/adc_config.o
 .debug_loc     0x0000000000000000      0x146 ./User/adc_config.o
 .debug_aranges
                0x0000000000000000       0x50 ./User/adc_config.o
 .debug_ranges  0x0000000000000000       0x90 ./User/adc_config.o
 .debug_line    0x0000000000000000      0x7cc ./User/adc_config.o
 .debug_str     0x0000000000000000      0xa4e ./User/adc_config.o
 .comment       0x0000000000000000       0x34 ./User/adc_config.o
 .debug_frame   0x0000000000000000       0xf4 ./User/adc_config.o
 .text          0x0000000000000000        0x0 ./User/adc_display.o
 .data          0x0000000000000000        0x0 ./User/adc_display.o
 .bss           0x0000000000000000        0x0 ./User/adc_display.o
 .text.ADC_Display_Draw_Channel_Data
                0x0000000000000000      0x13a ./User/adc_display.o
 .text.ADC_Display_Draw_All_Channels
                0x0000000000000000       0x2c ./User/adc_display.o
 .text.ADC_Display_Clear_Values_Area
                0x0000000000000000       0x28 ./User/adc_display.o
 .text.ADC_Display_Set_Config
                0x0000000000000000       0x10 ./User/adc_display.o
 .text.ADC_Display_Should_Update
                0x0000000000000000       0x2e ./User/adc_display.o
 .text.ADC_Display_Update
                0x0000000000000000       0x66 ./User/adc_display.o
 .text.ADC_Display_Get_Channel_Color
                0x0000000000000000       0x1e ./User/adc_display.o
 .text.ADC_Display_Format_Voltage
                0x0000000000000000       0x6c ./User/adc_display.o
 .text.ADC_Display_Format_Percentage
                0x0000000000000000       0x36 ./User/adc_display.o
 .rodata.ADC_Display_Draw_Channel_Data.str1.4
                0x0000000000000000       0x11 ./User/adc_display.o
 .text          0x0000000000000000        0x0 ./User/ch32v00x_it.o
 .data          0x0000000000000000        0x0 ./User/ch32v00x_it.o
 .bss           0x0000000000000000        0x0 ./User/ch32v00x_it.o
 .text          0x0000000000000000        0x0 ./User/display_control.o
 .data          0x0000000000000000        0x0 ./User/display_control.o
 .bss           0x0000000000000000        0x0 ./User/display_control.o
 .text.Display_Control_Set_Content_Update_Flag
                0x0000000000000000        0xc ./User/display_control.o
 .text.Display_Control_Is_On
                0x0000000000000000       0x10 ./User/display_control.o
 .text.Display_Control_Get_State
                0x0000000000000000        0xa ./User/display_control.o
 .text          0x0000000000000000        0x0 ./User/main.o
 .data          0x0000000000000000        0x0 ./User/main.o
 .bss           0x0000000000000000        0x0 ./User/main.o
 .bss.adc_data  0x0000000000000000       0x14 ./User/main.o
 .bss.last_adc_read_time
                0x0000000000000000        0x4 ./User/main.o
 .text          0x0000000000000000        0x0 ./User/pwm_config.o
 .data          0x0000000000000000        0x0 ./User/pwm_config.o
 .bss           0x0000000000000000        0x0 ./User/pwm_config.o
 .text.PWM_GPIO_Config
                0x0000000000000000       0x46 ./User/pwm_config.o
 .text.PWM_Timer_Config
                0x0000000000000000       0xc2 ./User/pwm_config.o
 .text.PWM_Config_Init
                0x0000000000000000       0x40 ./User/pwm_config.o
 .text.PWM_Set_Brightness_Percent
                0x0000000000000000       0x44 ./User/pwm_config.o
 .text.PWM_Fade_To_Brightness
                0x0000000000000000       0x22 ./User/pwm_config.o
 .text          0x0000000000000000        0x0 ./User/st7735.o
 .data          0x0000000000000000        0x0 ./User/st7735.o
 .bss           0x0000000000000000        0x0 ./User/st7735.o
 .text._tft_draw_fast_v_line
                0x0000000000000000       0x94 ./User/st7735.o
 .text._tft_draw_fast_h_line
                0x0000000000000000       0x92 ./User/st7735.o
 .text.tft_draw_pixel
                0x0000000000000000       0x4c ./User/st7735.o
 .text.tft_draw_bitmap
                0x0000000000000000       0x82 ./User/st7735.o
 .text.tft_draw_rect
                0x0000000000000000       0x98 ./User/st7735.o
 .text.tft_draw_line
                0x0000000000000000      0x178 ./User/st7735.o
 .text          0x0000000000000000        0x0 ./User/system_ch32v00x.o
 .data          0x0000000000000000        0x0 ./User/system_ch32v00x.o
 .bss           0x0000000000000000        0x0 ./User/system_ch32v00x.o
 .text          0x0000000000000000        0x0 ./User/touch_button.o
 .data          0x0000000000000000        0x0 ./User/touch_button.o
 .bss           0x0000000000000000        0x0 ./User/touch_button.o
 .text.Touch_Button_Is_Screen_On
                0x0000000000000000        0xa ./User/touch_button.o
 .text.Touch_Button_Reset_Timeout
                0x0000000000000000       0x12 ./User/touch_button.o
 .text.Touch_Button_Get_Time_Ms
                0x0000000000000000        0xa ./User/touch_button.o
 .text          0x0000000000000000        0x0 ./Startup/startup_ch32v00x.o
 .data          0x0000000000000000        0x0 ./Startup/startup_ch32v00x.o
 .bss           0x0000000000000000        0x0 ./Startup/startup_ch32v00x.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_adc.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_adc.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_DeInit
                0x0000000000000000       0x3a ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_Init
                0x0000000000000000       0x4e ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_StructInit
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_Cmd  0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_DMACmd
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_ITConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_ResetCalibration
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_GetResetCalibrationStatus
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_StartCalibration
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_GetCalibrationStatus
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_SoftwareStartConvCmd
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_GetSoftwareStartConvStatus
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_DiscModeChannelCountConfig
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_DiscModeCmd
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_RegularChannelConfig
                0x0000000000000000       0xba ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_ExternalTrigConvCmd
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_GetConversionValue
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_AutoInjectedConvCmd
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_InjectedDiscModeCmd
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_ExternalTrigInjectedConvConfig
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_ExternalTrigInjectedConvCmd
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_SoftwareStartInjectedConvCmd
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_GetSoftwareStartInjectedConvCmdStatus
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_InjectedChannelConfig
                0x0000000000000000       0x7a ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_InjectedSequencerLengthConfig
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_SetInjectedOffset
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_GetInjectedConversionValue
                0x0000000000000000       0x1c ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_AnalogWatchdogCmd
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_AnalogWatchdogThresholdsConfig
                0x0000000000000000        0x6 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_AnalogWatchdogSingleChannelConfig
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_GetFlagStatus
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_ClearFlag
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_GetITStatus
                0x0000000000000000       0x1c ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_ClearITPendingBit
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_Calibration_Vol
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_ExternalTrig_DLY
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_adc.o
 .debug_info    0x0000000000000000     0x1526 ./Peripheral/src/ch32v00x_adc.o
 .debug_abbrev  0x0000000000000000      0x307 ./Peripheral/src/ch32v00x_adc.o
 .debug_loc     0x0000000000000000      0x9fa ./Peripheral/src/ch32v00x_adc.o
 .debug_aranges
                0x0000000000000000      0x138 ./Peripheral/src/ch32v00x_adc.o
 .debug_ranges  0x0000000000000000      0x128 ./Peripheral/src/ch32v00x_adc.o
 .debug_line    0x0000000000000000     0x14ac ./Peripheral/src/ch32v00x_adc.o
 .debug_str     0x0000000000000000      0xb9a ./Peripheral/src/ch32v00x_adc.o
 .comment       0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_adc.o
 .debug_frame   0x0000000000000000      0x26c ./Peripheral/src/ch32v00x_adc.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_dbgmcu.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_dbgmcu.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_dbgmcu.o
 .text.DBGMCU_GetREVID
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_dbgmcu.o
 .text.DBGMCU_GetDEVID
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_dbgmcu.o
 .text.__get_DEBUG_CR
                0x0000000000000000        0x6 ./Peripheral/src/ch32v00x_dbgmcu.o
 .text.__set_DEBUG_CR
                0x0000000000000000        0x6 ./Peripheral/src/ch32v00x_dbgmcu.o
 .text.DBGMCU_Config
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_dbgmcu.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_dma.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_dma.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_DeInit
                0x0000000000000000       0x92 ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_Init
                0x0000000000000000       0x38 ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_StructInit
                0x0000000000000000       0x2e ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_Cmd  0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_ITConfig
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_SetCurrDataCounter
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_GetCurrDataCounter
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_GetFlagStatus
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_ClearFlag
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_GetITStatus
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_ClearITPendingBit
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_dma.o
 .debug_info    0x0000000000000000      0xd17 ./Peripheral/src/ch32v00x_dma.o
 .debug_abbrev  0x0000000000000000      0x302 ./Peripheral/src/ch32v00x_dma.o
 .debug_loc     0x0000000000000000      0x14a ./Peripheral/src/ch32v00x_dma.o
 .debug_aranges
                0x0000000000000000       0x60 ./Peripheral/src/ch32v00x_dma.o
 .debug_ranges  0x0000000000000000       0x50 ./Peripheral/src/ch32v00x_dma.o
 .debug_line    0x0000000000000000      0x807 ./Peripheral/src/ch32v00x_dma.o
 .debug_str     0x0000000000000000      0x803 ./Peripheral/src/ch32v00x_dma.o
 .comment       0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_dma.o
 .debug_frame   0x0000000000000000       0xcc ./Peripheral/src/ch32v00x_dma.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_exti.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_exti.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_exti.o
 .text.EXTI_DeInit
                0x0000000000000000       0x22 ./Peripheral/src/ch32v00x_exti.o
 .text.EXTI_StructInit
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_exti.o
 .text.EXTI_GenerateSWInterrupt
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_exti.o
 .text.EXTI_GetFlagStatus
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_exti.o
 .text.EXTI_ClearFlag
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_exti.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_flash.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_flash.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_flash.o
 .text.ROM_ERASE
                0x0000000000000000       0x60 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_SetLatency
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_Unlock
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_Lock
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_GetUserOptionByte
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_GetWriteProtectionOptionByte
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_GetReadOutProtectionStatus
                0x0000000000000000        0xc ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ITConfig
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_GetFlagStatus
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ClearFlag
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_GetStatus
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_GetBank1Status
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_WaitForLastOperation
                0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ErasePage
                0x0000000000000000       0x4c ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_EraseAllPages
                0x0000000000000000       0x48 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_EraseOptionBytes
                0x0000000000000000       0x9a ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ProgramWord
                0x0000000000000000       0x6e ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ProgramHalfWord
                0x0000000000000000       0x48 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ProgramOptionByteData
                0x0000000000000000       0x66 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_EnableWriteProtection
                0x0000000000000000       0x9e ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ReadOutProtection
                0x0000000000000000       0xae ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_UserOptionByteConfig
                0x0000000000000000       0x7a ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_WaitForLastBank1Operation
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_Unlock_Fast
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_Lock_Fast
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_BufReset
                0x0000000000000000       0x28 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_BufLoad
                0x0000000000000000       0x36 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ErasePage_Fast
                0x0000000000000000       0x36 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ProgramPage_Fast
                0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_flash.o
 .text.SystemReset_StartMode
                0x0000000000000000       0x56 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ROM_ERASE
                0x0000000000000000      0x140 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ROM_WRITE
                0x0000000000000000       0xf6 ./Peripheral/src/ch32v00x_flash.o
 .debug_info    0x0000000000000000     0x1481 ./Peripheral/src/ch32v00x_flash.o
 .debug_abbrev  0x0000000000000000      0x3fa ./Peripheral/src/ch32v00x_flash.o
 .debug_loc     0x0000000000000000      0xad0 ./Peripheral/src/ch32v00x_flash.o
 .debug_aranges
                0x0000000000000000      0x110 ./Peripheral/src/ch32v00x_flash.o
 .debug_ranges  0x0000000000000000      0x100 ./Peripheral/src/ch32v00x_flash.o
 .debug_line    0x0000000000000000     0x1c99 ./Peripheral/src/ch32v00x_flash.o
 .debug_str     0x0000000000000000      0xac5 ./Peripheral/src/ch32v00x_flash.o
 .comment       0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_flash.o
 .debug_frame   0x0000000000000000      0x324 ./Peripheral/src/ch32v00x_flash.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_gpio.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_gpio.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_DeInit
                0x0000000000000000       0x68 ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_AFIODeInit
                0x0000000000000000       0x28 ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_StructInit
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_ReadInputData
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_ReadOutputDataBit
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_ReadOutputData
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_SetBits
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_ResetBits
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_WriteBit
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_Write
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_PinLockConfig
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_PinRemapConfig
                0x0000000000000000       0xc4 ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_IPD_Unused
                0x0000000000000000       0xa4 ./Peripheral/src/ch32v00x_gpio.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_i2c.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_i2c.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_DeInit
                0x0000000000000000       0x3a ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_Init
                0x0000000000000000      0x100 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_StructInit
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_Cmd  0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_DMACmd
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_DMALastTransferCmd
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_GenerateSTART
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_GenerateSTOP
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_AcknowledgeConfig
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_OwnAddress2Config
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_DualAddressCmd
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_GeneralCallCmd
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_ITConfig
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_SendData
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_ReceiveData
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_Send7bitAddress
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_ReadRegister
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_SoftwareResetCmd
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_NACKPositionConfig
                0x0000000000000000       0x22 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_TransmitPEC
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_PECPositionConfig
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_CalculatePEC
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_GetPEC
                0x0000000000000000        0x6 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_ARPCmd
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_StretchClockCmd
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_FastModeDutyCycleConfig
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_CheckEvent
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_GetLastEvent
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_GetFlagStatus
                0x0000000000000000       0x32 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_ClearFlag
                0x0000000000000000        0xc ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_GetITStatus
                0x0000000000000000       0x20 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_ClearITPendingBit
                0x0000000000000000        0xc ./Peripheral/src/ch32v00x_i2c.o
 .debug_info    0x0000000000000000     0x1417 ./Peripheral/src/ch32v00x_i2c.o
 .debug_abbrev  0x0000000000000000      0x3a7 ./Peripheral/src/ch32v00x_i2c.o
 .debug_loc     0x0000000000000000      0x720 ./Peripheral/src/ch32v00x_i2c.o
 .debug_aranges
                0x0000000000000000      0x108 ./Peripheral/src/ch32v00x_i2c.o
 .debug_ranges  0x0000000000000000       0xf8 ./Peripheral/src/ch32v00x_i2c.o
 .debug_line    0x0000000000000000     0x10ee ./Peripheral/src/ch32v00x_i2c.o
 .debug_str     0x0000000000000000      0xac2 ./Peripheral/src/ch32v00x_i2c.o
 .comment       0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_i2c.o
 .debug_frame   0x0000000000000000      0x254 ./Peripheral/src/ch32v00x_i2c.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_iwdg.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_iwdg.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_iwdg.o
 .text.IWDG_WriteAccessCmd
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_iwdg.o
 .text.IWDG_SetPrescaler
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_iwdg.o
 .text.IWDG_SetReload
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_iwdg.o
 .text.IWDG_ReloadCounter
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_iwdg.o
 .text.IWDG_Enable
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_iwdg.o
 .text.IWDG_GetFlagStatus
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_iwdg.o
 .debug_info    0x0000000000000000      0xb46 ./Peripheral/src/ch32v00x_iwdg.o
 .debug_abbrev  0x0000000000000000      0x282 ./Peripheral/src/ch32v00x_iwdg.o
 .debug_loc     0x0000000000000000       0x68 ./Peripheral/src/ch32v00x_iwdg.o
 .debug_aranges
                0x0000000000000000       0x48 ./Peripheral/src/ch32v00x_iwdg.o
 .debug_ranges  0x0000000000000000       0x38 ./Peripheral/src/ch32v00x_iwdg.o
 .debug_line    0x0000000000000000      0x45f ./Peripheral/src/ch32v00x_iwdg.o
 .debug_str     0x0000000000000000      0x709 ./Peripheral/src/ch32v00x_iwdg.o
 .comment       0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_iwdg.o
 .debug_frame   0x0000000000000000       0x70 ./Peripheral/src/ch32v00x_iwdg.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_misc.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_misc.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_misc.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_opa.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_opa.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_opa.o
 .text.OPA_DeInit
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_opa.o
 .text.OPA_Init
                0x0000000000000000       0x22 ./Peripheral/src/ch32v00x_opa.o
 .text.OPA_StructInit
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_opa.o
 .text.OPA_Cmd  0x0000000000000000       0x2c ./Peripheral/src/ch32v00x_opa.o
 .debug_info    0x0000000000000000      0xaa5 ./Peripheral/src/ch32v00x_opa.o
 .debug_abbrev  0x0000000000000000      0x230 ./Peripheral/src/ch32v00x_opa.o
 .debug_loc     0x0000000000000000       0x35 ./Peripheral/src/ch32v00x_opa.o
 .debug_aranges
                0x0000000000000000       0x38 ./Peripheral/src/ch32v00x_opa.o
 .debug_ranges  0x0000000000000000       0x28 ./Peripheral/src/ch32v00x_opa.o
 .debug_line    0x0000000000000000      0x455 ./Peripheral/src/ch32v00x_opa.o
 .debug_str     0x0000000000000000      0x67d ./Peripheral/src/ch32v00x_opa.o
 .comment       0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_opa.o
 .debug_frame   0x0000000000000000       0x50 ./Peripheral/src/ch32v00x_opa.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_pwr.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_pwr.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_pwr.o
 .text.PWR_DeInit
                0x0000000000000000       0x2c ./Peripheral/src/ch32v00x_pwr.o
 .text.PWR_PVDCmd
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_pwr.o
 .text.PWR_PVDLevelConfig
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_pwr.o
 .text.PWR_AutoWakeUpCmd
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_pwr.o
 .text.PWR_AWU_SetPrescaler
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_pwr.o
 .text.PWR_AWU_SetWindowValue
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_pwr.o
 .text.PWR_EnterSTANDBYMode
                0x0000000000000000       0xfc ./Peripheral/src/ch32v00x_pwr.o
 .text.PWR_GetFlagStatus
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_pwr.o
 .debug_info    0x0000000000000000      0xf00 ./Peripheral/src/ch32v00x_pwr.o
 .debug_abbrev  0x0000000000000000      0x359 ./Peripheral/src/ch32v00x_pwr.o
 .debug_loc     0x0000000000000000      0x19a ./Peripheral/src/ch32v00x_pwr.o
 .debug_aranges
                0x0000000000000000       0x58 ./Peripheral/src/ch32v00x_pwr.o
 .debug_ranges  0x0000000000000000       0x90 ./Peripheral/src/ch32v00x_pwr.o
 .debug_line    0x0000000000000000      0x7b3 ./Peripheral/src/ch32v00x_pwr.o
 .debug_str     0x0000000000000000      0x80f ./Peripheral/src/ch32v00x_pwr.o
 .comment       0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_pwr.o
 .debug_frame   0x0000000000000000       0xb8 ./Peripheral/src/ch32v00x_pwr.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_rcc.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_rcc.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_HSEConfig
                0x0000000000000000       0x3c ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_DeInit
                0x0000000000000000       0x58 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_HSICmd
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_PLLConfig
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_PLLCmd
                0x0000000000000000       0x24 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_SYSCLKConfig
                0x0000000000000000       0x4e ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_GetSYSCLKSource
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_HCLKConfig
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_ITConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_ADCCLKConfig
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_LSICmd
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_AHBPeriphClockCmd
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_APB2PeriphResetCmd
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_APB1PeriphResetCmd
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_ClockSecuritySystemCmd
                0x0000000000000000       0x24 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_MCOConfig
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_GetFlagStatus
                0x0000000000000000       0x20 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_WaitForHSEStartUp
                0x0000000000000000       0x42 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_ClearFlag
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_GetITStatus
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_ClearITPendingBit
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_rcc.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_spi.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_spi.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_I2S_DeInit
                0x0000000000000000       0x32 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_Init
                0x0000000000000000       0x32 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_StructInit
                0x0000000000000000       0x22 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_Cmd  0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_I2S_ITConfig
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_I2S_DMACmd
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_I2S_SendData
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_I2S_ReceiveData
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_NSSInternalSoftwareConfig
                0x0000000000000000       0x24 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_SSOutputCmd
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_DataSizeConfig
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_TransmitCRC
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_CalculateCRC
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_GetCRC
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_GetCRCPolynomial
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_BiDirectionalLineConfig
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_I2S_GetFlagStatus
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_I2S_ClearFlag
                0x0000000000000000        0xc ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_I2S_GetITStatus
                0x0000000000000000       0x28 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_I2S_ClearITPendingBit
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_spi.o
 .debug_info    0x0000000000000000     0x1068 ./Peripheral/src/ch32v00x_spi.o
 .debug_abbrev  0x0000000000000000      0x303 ./Peripheral/src/ch32v00x_spi.o
 .debug_loc     0x0000000000000000      0x429 ./Peripheral/src/ch32v00x_spi.o
 .debug_aranges
                0x0000000000000000       0xb8 ./Peripheral/src/ch32v00x_spi.o
 .debug_ranges  0x0000000000000000       0xa8 ./Peripheral/src/ch32v00x_spi.o
 .debug_line    0x0000000000000000      0xa41 ./Peripheral/src/ch32v00x_spi.o
 .debug_str     0x0000000000000000      0x949 ./Peripheral/src/ch32v00x_spi.o
 .comment       0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_spi.o
 .debug_frame   0x0000000000000000      0x15c ./Peripheral/src/ch32v00x_spi.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_tim.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_tim.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_tim.o
 .text.TI1_Config
                0x0000000000000000       0x5a ./Peripheral/src/ch32v00x_tim.o
 .text.TI2_Config
                0x0000000000000000       0x70 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_DeInit
                0x0000000000000000       0x5c ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC1Init
                0x0000000000000000       0x6a ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC2Init
                0x0000000000000000       0x96 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC3Init
                0x0000000000000000       0x94 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC4Init
                0x0000000000000000       0x6e ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_BDTRConfig
                0x0000000000000000       0x20 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_TimeBaseStructInit
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OCStructInit
                0x0000000000000000       0x22 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ICStructInit
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_BDTRStructInit
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_CtrlPWMOutputs
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_GenerateEvent
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_DMAConfig
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_DMACmd
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_InternalClockConfig
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ITRxExternalClockConfig
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_TIxExternalClockConfig
                0x0000000000000000       0x48 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ETRConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ETRClockMode1Config
                0x0000000000000000       0x2a ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ETRClockMode2Config
                0x0000000000000000       0x22 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_PrescalerConfig
                0x0000000000000000        0x6 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_CounterModeConfig
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SelectInputTrigger
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_EncoderInterfaceConfig
                0x0000000000000000       0x3c ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ForcedOC1Config
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ForcedOC2Config
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ForcedOC3Config
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ForcedOC4Config
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ARRPreloadConfig
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SelectCOM
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SelectCCDMA
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_CCPreloadControl
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC1PreloadConfig
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC2PreloadConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC3PreloadConfig
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC4PreloadConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC1FastConfig
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC2FastConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC3FastConfig
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC4FastConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ClearOC1Ref
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ClearOC2Ref
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ClearOC3Ref
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ClearOC4Ref
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC1PolarityConfig
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC1NPolarityConfig
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC2PolarityConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC2NPolarityConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC3PolarityConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC3NPolarityConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC4PolarityConfig
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_CCxCmd
                0x0000000000000000       0x20 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_CCxNCmd
                0x0000000000000000       0x20 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SelectOCxM
                0x0000000000000000       0x4c ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_UpdateDisableConfig
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_UpdateRequestConfig
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SelectHallSensor
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SelectOnePulseMode
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SelectOutputTrigger
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SelectSlaveMode
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SelectMasterSlaveMode
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SetCounter
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SetAutoreload
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SetCompare2
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SetCompare3
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SetCompare4
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SetIC1Prescaler
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SetIC2Prescaler
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_PWMIConfig
                0x0000000000000000       0xa4 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SetIC3Prescaler
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SetIC4Prescaler
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ICInit
                0x0000000000000000      0x148 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SetClockDivision
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_GetCapture1
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_GetCapture2
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_GetCapture3
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_GetCapture4
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_GetCounter
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_GetPrescaler
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_GetFlagStatus
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ClearFlag
                0x0000000000000000        0xc ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_IndicateCaptureLevelCmd
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_tim.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_usart.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_usart.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_DeInit
                0x0000000000000000       0x36 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_StructInit
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_ClockInit
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_usart.o
 .text.USART_ClockStructInit
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_ITConfig
                0x0000000000000000       0x36 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_DMACmd
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_SetAddress
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_WakeUpConfig
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_ReceiverWakeUpCmd
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_LINBreakDetectLengthConfig
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_LINCmd
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_ReceiveData
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_SendBreak
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_usart.o
 .text.USART_SetGuardTime
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_SetPrescaler
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_SmartCardCmd
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_usart.o
 .text.USART_SmartCardNACKCmd
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_HalfDuplexCmd
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_OverSampling8Cmd
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_OneBitMethodCmd
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_usart.o
 .text.USART_IrDAConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_IrDACmd
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_ClearFlag
                0x0000000000000000        0xc ./Peripheral/src/ch32v00x_usart.o
 .text.USART_GetITStatus
                0x0000000000000000       0x3c ./Peripheral/src/ch32v00x_usart.o
 .text.USART_ClearITPendingBit
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_usart.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_wwdg.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_wwdg.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_wwdg.o
 .text.WWDG_DeInit
                0x0000000000000000       0x2e ./Peripheral/src/ch32v00x_wwdg.o
 .text.WWDG_SetPrescaler
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_wwdg.o
 .text.WWDG_SetWindowValue
                0x0000000000000000       0x26 ./Peripheral/src/ch32v00x_wwdg.o
 .text.WWDG_EnableIT
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_wwdg.o
 .text.WWDG_SetCounter
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_wwdg.o
 .text.WWDG_Enable
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_wwdg.o
 .text.WWDG_GetFlagStatus
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_wwdg.o
 .text.WWDG_ClearFlag
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_wwdg.o
 .debug_info    0x0000000000000000      0xb27 ./Peripheral/src/ch32v00x_wwdg.o
 .debug_abbrev  0x0000000000000000      0x29d ./Peripheral/src/ch32v00x_wwdg.o
 .debug_loc     0x0000000000000000       0xae ./Peripheral/src/ch32v00x_wwdg.o
 .debug_aranges
                0x0000000000000000       0x58 ./Peripheral/src/ch32v00x_wwdg.o
 .debug_ranges  0x0000000000000000       0x48 ./Peripheral/src/ch32v00x_wwdg.o
 .debug_line    0x0000000000000000      0x503 ./Peripheral/src/ch32v00x_wwdg.o
 .debug_str     0x0000000000000000      0x6c2 ./Peripheral/src/ch32v00x_wwdg.o
 .comment       0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_wwdg.o
 .debug_frame   0x0000000000000000       0xa4 ./Peripheral/src/ch32v00x_wwdg.o
 .text          0x0000000000000000        0x0 ./Debug/debug.o
 .data          0x0000000000000000        0x0 ./Debug/debug.o
 .bss           0x0000000000000000        0x0 ./Debug/debug.o
 .text.Delay_Us
                0x0000000000000000       0x4a ./Debug/debug.o
 .text.SDI_Printf_Enable
                0x0000000000000000       0x2a ./Debug/debug.o
 .text._sbrk    0x0000000000000000       0x2e ./Debug/debug.o
 .data.curbrk.4091
                0x0000000000000000        0x4 ./Debug/debug.o
 .text          0x0000000000000000        0x0 ./Core/core_riscv.o
 .data          0x0000000000000000        0x0 ./Core/core_riscv.o
 .bss           0x0000000000000000        0x0 ./Core/core_riscv.o
 .text.__get_MSTATUS
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__set_MSTATUS
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__get_MISA
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__set_MISA
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__get_MTVEC
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__set_MTVEC
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__get_MSCRATCH
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__set_MSCRATCH
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__get_MEPC
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__set_MEPC
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__get_MCAUSE
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__set_MCAUSE
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__get_MVENDORID
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__get_MARCHID
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__get_MIMPID
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__get_MHARTID
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__get_SP
                0x0000000000000000        0x4 ./Core/core_riscv.o
 .highcode      0x0000000000000000        0xa ./Core/core_riscv.o
 .debug_info    0x0000000000000000      0x349 ./Core/core_riscv.o
 .debug_abbrev  0x0000000000000000      0x10d ./Core/core_riscv.o
 .debug_aranges
                0x0000000000000000       0xa8 ./Core/core_riscv.o
 .debug_ranges  0x0000000000000000       0x98 ./Core/core_riscv.o
 .debug_line    0x0000000000000000      0x460 ./Core/core_riscv.o
 .debug_str     0x0000000000000000      0x294 ./Core/core_riscv.o
 .comment       0x0000000000000000       0x34 ./Core/core_riscv.o
 .debug_frame   0x0000000000000000      0x130 ./Core/core_riscv.o
 .text          0x0000000000000000        0x0 /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
 .data          0x0000000000000000        0x0 /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
 .bss           0x0000000000000000        0x0 /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
 .text.sprintf  0x0000000000000000       0x2c /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
 .text.snprintf
                0x0000000000000000       0x2e /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
 .text.putchar  0x0000000000000000       0x1c /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
 .data          0x0000000000000000        0x0 /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(save-restore.o)
 .bss           0x0000000000000000        0x0 /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(save-restore.o)
 .eh_frame      0x0000000000000000       0x68 /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(save-restore.o)
 .data          0x0000000000000000        0x0 /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(muldi3.o)
 .bss           0x0000000000000000        0x0 /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(muldi3.o)
 .data          0x0000000000000000        0x0 /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(div.o)
 .bss           0x0000000000000000        0x0 /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(div.o)
 .text          0x0000000000000000        0x0 /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libg_nano.a(lib_a-memcpy.o)
 .data          0x0000000000000000        0x0 /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libg_nano.a(lib_a-memcpy.o)
 .bss           0x0000000000000000        0x0 /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libg_nano.a(lib_a-memcpy.o)
 .text          0x0000000000000000       0xa8 /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libg_nano.a(lib_a-memset.o)
 .data          0x0000000000000000        0x0 /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libg_nano.a(lib_a-memset.o)
 .bss           0x0000000000000000        0x0 /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libg_nano.a(lib_a-memset.o)

Memory Configuration

Name             Origin             Length             Attributes
FLASH            0x0000000000000000 0x0000000000004000 xr
RAM              0x0000000020000000 0x0000000000000800 xrw
*default*        0x0000000000000000 0xffffffffffffffff

Linker script and memory map

LOAD ./User/adc_config.o
LOAD ./User/adc_display.o
LOAD ./User/ch32v00x_it.o
LOAD ./User/display_control.o
LOAD ./User/main.o
LOAD ./User/pwm_config.o
LOAD ./User/st7735.o
LOAD ./User/system_ch32v00x.o
LOAD ./User/touch_button.o
LOAD ./Startup/startup_ch32v00x.o
LOAD ./Peripheral/src/ch32v00x_adc.o
LOAD ./Peripheral/src/ch32v00x_dbgmcu.o
LOAD ./Peripheral/src/ch32v00x_dma.o
LOAD ./Peripheral/src/ch32v00x_exti.o
LOAD ./Peripheral/src/ch32v00x_flash.o
LOAD ./Peripheral/src/ch32v00x_gpio.o
LOAD ./Peripheral/src/ch32v00x_i2c.o
LOAD ./Peripheral/src/ch32v00x_iwdg.o
LOAD ./Peripheral/src/ch32v00x_misc.o
LOAD ./Peripheral/src/ch32v00x_opa.o
LOAD ./Peripheral/src/ch32v00x_pwr.o
LOAD ./Peripheral/src/ch32v00x_rcc.o
LOAD ./Peripheral/src/ch32v00x_spi.o
LOAD ./Peripheral/src/ch32v00x_tim.o
LOAD ./Peripheral/src/ch32v00x_usart.o
LOAD ./Peripheral/src/ch32v00x_wwdg.o
LOAD ./Debug/debug.o
LOAD ./Core/core_riscv.o
LOAD /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a
LOAD /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a
LOAD /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libg_nano.a
LOAD /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libc_nano.a
LOAD /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a
START GROUP
LOAD /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a
LOAD /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libc_nano.a
LOAD /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libnosys.a
END GROUP
START GROUP
LOAD /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a
LOAD /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libc_nano.a
LOAD /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libnosys.a
END GROUP
                0x0000000000000100                __stack_size = 0x100
                [!provide]                        PROVIDE (_stack_size = __stack_size)

.init           0x0000000000000000       0xa0
                0x0000000000000000                _sinit = .
                0x0000000000000000                . = ALIGN (0x4)
 *(SORT_NONE(.init))
 .init          0x0000000000000000       0x9e ./Startup/startup_ch32v00x.o
                0x0000000000000000                _start
                0x00000000000000a0                . = ALIGN (0x4)
 *fill*         0x000000000000009e        0x2 
                0x00000000000000a0                _einit = .

.highcodelalign
                0x00000000000000a0        0x0
                0x00000000000000a0                . = ALIGN (0x4)
                0x00000000000000a0                PROVIDE (_highcode_lma = .)

.highcode       0x0000000020000000        0x0 load address 0x00000000000000a0
                0x0000000020000000                . = ALIGN (0x4)
                0x0000000020000000                PROVIDE (_highcode_vma_start = .)
 *(.highcode)
 *(.highcode.*)
                0x0000000020000000                . = ALIGN (0x4)
                0x0000000020000000                PROVIDE (_highcode_vma_end = .)

.text           0x00000000000000a0     0x2034
                0x00000000000000a0                . = ALIGN (0x4)
 *(.text)
 .text          0x00000000000000a0       0x14 /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(save-restore.o)
                0x00000000000000a0                __riscv_save_1
                0x00000000000000a0                __riscv_save_2
                0x00000000000000a0                __riscv_save_0
                0x00000000000000aa                __riscv_restore_2
                0x00000000000000aa                __riscv_restore_0
                0x00000000000000aa                __riscv_restore_1
 .text          0x00000000000000b4       0x16 /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(muldi3.o)
                0x00000000000000b4                __mulsi3
 *fill*         0x00000000000000ca        0x2 
 .text          0x00000000000000cc       0x7e /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(div.o)
                0x00000000000000cc                __divsi3
                0x00000000000000d4                __udivsi3
                0x0000000000000100                __umodsi3
                0x0000000000000124                __modsi3
 *(.text.*)
 .text.ADC_Display_Draw_Header
                0x000000000000014a       0x28 ./User/adc_display.o
                0x000000000000014a                ADC_Display_Draw_Header
 .text.ADC_Display_Draw_Channel_Labels
                0x0000000000000172       0x5c ./User/adc_display.o
                0x0000000000000172                ADC_Display_Draw_Channel_Labels
 .text.ADC_Display_Init
                0x00000000000001ce       0x26 ./User/adc_display.o
                0x00000000000001ce                ADC_Display_Init
 .text.NMI_Handler
                0x00000000000001f4        0x2 ./User/ch32v00x_it.o
                0x00000000000001f4                NMI_Handler
 .text.HardFault_Handler
                0x00000000000001f6       0x10 ./User/ch32v00x_it.o
                0x00000000000001f6                HardFault_Handler
 .text.EXTI7_0_IRQHandler
                0x0000000000000206        0x8 ./User/ch32v00x_it.o
                0x0000000000000206                EXTI7_0_IRQHandler
 .text.TIM2_IRQHandler
                0x000000000000020e       0x24 ./User/ch32v00x_it.o
                0x000000000000020e                TIM2_IRQHandler
 .text.Display_Control_Turn_On
                0x0000000000000232       0x16 ./User/display_control.o
                0x0000000000000232                Display_Control_Turn_On
 .text.Display_Control_Turn_Off
                0x0000000000000248       0x1a ./User/display_control.o
                0x0000000000000248                Display_Control_Turn_Off
 .text.Display_Control_Toggle
                0x0000000000000262       0x18 ./User/display_control.o
                0x0000000000000262                Display_Control_Toggle
 .text.Display_Control_Clear_Screen
                0x000000000000027a       0x18 ./User/display_control.o
                0x000000000000027a                Display_Control_Clear_Screen
 .text.Display_Control_Show_Startup_Message
                0x0000000000000292       0x64 ./User/display_control.o
                0x0000000000000292                Display_Control_Show_Startup_Message
 .text.Display_Control_Init
                0x00000000000002f6       0x24 ./User/display_control.o
                0x00000000000002f6                Display_Control_Init
 .text.Display_Control_Show_Off_Message
                0x000000000000031a       0x26 ./User/display_control.o
                0x000000000000031a                Display_Control_Show_Off_Message
 .text.Display_Control_Update
                0x0000000000000340       0x3e ./User/display_control.o
                0x0000000000000340                Display_Control_Update
 .text.System_Init
                0x000000000000037e       0x5c ./User/main.o
                0x000000000000037e                System_Init
 .text.startup.main
                0x00000000000003da       0xa2 ./User/main.o
                0x00000000000003da                main
 .text.PWM_Set_Brightness
                0x000000000000047c       0x2a ./User/pwm_config.o
                0x000000000000047c                PWM_Set_Brightness
 .text.PWM_Update_Fade
                0x00000000000004a6       0x50 ./User/pwm_config.o
                0x00000000000004a6                PWM_Update_Fade
 .text.PWM_Turn_On
                0x00000000000004f6       0x10 ./User/pwm_config.o
                0x00000000000004f6                PWM_Turn_On
 .text.PWM_Turn_Off
                0x0000000000000506        0xe ./User/pwm_config.o
                0x0000000000000506                PWM_Turn_Off
 .text.PWM_Get_Brightness
                0x0000000000000514        0x6 ./User/pwm_config.o
                0x0000000000000514                PWM_Get_Brightness
 .text.SPI_send_DMA
                0x000000000000051a       0x3e ./User/st7735.o
 .text.SPI_send
                0x0000000000000558       0x12 ./User/st7735.o
 .text.write_command_8
                0x000000000000056a       0x14 ./User/st7735.o
 .text.write_data_16
                0x000000000000057e       0x1e ./User/st7735.o
 .text.tft_set_window
                0x000000000000059c       0x3a ./User/st7735.o
 .text.tft_init
                0x00000000000005d6      0x190 ./User/st7735.o
                0x00000000000005d6                tft_init
 .text.tft_set_cursor
                0x0000000000000766        0xe ./User/st7735.o
                0x0000000000000766                tft_set_cursor
 .text.tft_set_color
                0x0000000000000774        0xa ./User/st7735.o
                0x0000000000000774                tft_set_color
 .text.tft_set_background_color
                0x000000000000077e        0x6 ./User/st7735.o
                0x000000000000077e                tft_set_background_color
 .text.tft_print_char
                0x0000000000000784       0xe4 ./User/st7735.o
                0x0000000000000784                tft_print_char
 .text.tft_print
                0x0000000000000868       0x20 ./User/st7735.o
                0x0000000000000868                tft_print
 .text.tft_print_number
                0x0000000000000888       0xaa ./User/st7735.o
                0x0000000000000888                tft_print_number
 .text.tft_fill_rect
                0x0000000000000932       0x8a ./User/st7735.o
                0x0000000000000932                tft_fill_rect
 .text.SystemInit
                0x00000000000009bc      0x134 ./User/system_ch32v00x.o
                0x00000000000009bc                SystemInit
 .text.SystemCoreClockUpdate
                0x0000000000000af0       0x6c ./User/system_ch32v00x.o
                0x0000000000000af0                SystemCoreClockUpdate
 .text.Touch_Button_GPIO_Config
                0x0000000000000b5c       0x30 ./User/touch_button.o
                0x0000000000000b5c                Touch_Button_GPIO_Config
 .text.Touch_Button_Timer_Init
                0x0000000000000b8c       0x56 ./User/touch_button.o
                0x0000000000000b8c                Touch_Button_Timer_Init
 .text.Touch_Button_EXTI_Config
                0x0000000000000be2       0x42 ./User/touch_button.o
                0x0000000000000be2                Touch_Button_EXTI_Config
 .text.Touch_Button_Init
                0x0000000000000c24       0x2a ./User/touch_button.o
                0x0000000000000c24                Touch_Button_Init
 .text.Touch_Button_Update
                0x0000000000000c4e       0x72 ./User/touch_button.o
                0x0000000000000c4e                Touch_Button_Update
 .text.Touch_Button_Get_Event
                0x0000000000000cc0        0xc ./User/touch_button.o
                0x0000000000000cc0                Touch_Button_Get_Event
 .text.Touch_Button_IRQ_Handler
                0x0000000000000ccc       0x46 ./User/touch_button.o
                0x0000000000000ccc                Touch_Button_IRQ_Handler
 .text.vector_handler
                0x0000000000000d12        0x2 ./Startup/startup_ch32v00x.o
                0x0000000000000d12                TIM1_CC_IRQHandler
                0x0000000000000d12                SysTick_Handler
                0x0000000000000d12                PVD_IRQHandler
                0x0000000000000d12                SPI1_IRQHandler
                0x0000000000000d12                AWU_IRQHandler
                0x0000000000000d12                DMA1_Channel4_IRQHandler
                0x0000000000000d12                ADC1_IRQHandler
                0x0000000000000d12                DMA1_Channel7_IRQHandler
                0x0000000000000d12                I2C1_EV_IRQHandler
                0x0000000000000d12                DMA1_Channel6_IRQHandler
                0x0000000000000d12                RCC_IRQHandler
                0x0000000000000d12                TIM1_TRG_COM_IRQHandler
                0x0000000000000d12                DMA1_Channel1_IRQHandler
                0x0000000000000d12                DMA1_Channel5_IRQHandler
                0x0000000000000d12                DMA1_Channel3_IRQHandler
                0x0000000000000d12                TIM1_UP_IRQHandler
                0x0000000000000d12                WWDG_IRQHandler
                0x0000000000000d12                SW_Handler
                0x0000000000000d12                TIM1_BRK_IRQHandler
                0x0000000000000d12                DMA1_Channel2_IRQHandler
                0x0000000000000d12                FLASH_IRQHandler
                0x0000000000000d12                USART1_IRQHandler
                0x0000000000000d12                I2C1_ER_IRQHandler
 .text.handle_reset
                0x0000000000000d14       0xb0 ./Startup/startup_ch32v00x.o
                0x0000000000000d14                handle_reset
 .text.DBGMCU_GetCHIPID
                0x0000000000000dc4        0xa ./Peripheral/src/ch32v00x_dbgmcu.o
                0x0000000000000dc4                DBGMCU_GetCHIPID
 .text.EXTI_Init
                0x0000000000000dce       0x6a ./Peripheral/src/ch32v00x_exti.o
                0x0000000000000dce                EXTI_Init
 .text.EXTI_GetITStatus
                0x0000000000000e38       0x1e ./Peripheral/src/ch32v00x_exti.o
                0x0000000000000e38                EXTI_GetITStatus
 .text.EXTI_ClearITPendingBit
                0x0000000000000e56        0xa ./Peripheral/src/ch32v00x_exti.o
                0x0000000000000e56                EXTI_ClearITPendingBit
 .text.GPIO_Init
                0x0000000000000e60       0x7c ./Peripheral/src/ch32v00x_gpio.o
                0x0000000000000e60                GPIO_Init
 .text.GPIO_ReadInputDataBit
                0x0000000000000edc        0xa ./Peripheral/src/ch32v00x_gpio.o
                0x0000000000000edc                GPIO_ReadInputDataBit
 .text.GPIO_EXTILineConfig
                0x0000000000000ee6       0x22 ./Peripheral/src/ch32v00x_gpio.o
                0x0000000000000ee6                GPIO_EXTILineConfig
 .text.NVIC_PriorityGroupConfig
                0x0000000000000f08        0x6 ./Peripheral/src/ch32v00x_misc.o
                0x0000000000000f08                NVIC_PriorityGroupConfig
 .text.NVIC_Init
                0x0000000000000f0e       0x62 ./Peripheral/src/ch32v00x_misc.o
                0x0000000000000f0e                NVIC_Init
 .text.RCC_AdjustHSICalibrationValue
                0x0000000000000f70       0x12 ./Peripheral/src/ch32v00x_rcc.o
                0x0000000000000f70                RCC_AdjustHSICalibrationValue
 .text.RCC_GetClocksFreq
                0x0000000000000f82       0xa2 ./Peripheral/src/ch32v00x_rcc.o
                0x0000000000000f82                RCC_GetClocksFreq
 .text.RCC_APB2PeriphClockCmd
                0x0000000000001024       0x1e ./Peripheral/src/ch32v00x_rcc.o
                0x0000000000001024                RCC_APB2PeriphClockCmd
 .text.RCC_APB1PeriphClockCmd
                0x0000000000001042       0x1e ./Peripheral/src/ch32v00x_rcc.o
                0x0000000000001042                RCC_APB1PeriphClockCmd
 .text.TIM_TimeBaseInit
                0x0000000000001060       0x4e ./Peripheral/src/ch32v00x_tim.o
                0x0000000000001060                TIM_TimeBaseInit
 .text.TIM_Cmd  0x00000000000010ae       0x18 ./Peripheral/src/ch32v00x_tim.o
                0x00000000000010ae                TIM_Cmd
 .text.TIM_ITConfig
                0x00000000000010c6       0x12 ./Peripheral/src/ch32v00x_tim.o
                0x00000000000010c6                TIM_ITConfig
 .text.TIM_SetCompare1
                0x00000000000010d8        0x4 ./Peripheral/src/ch32v00x_tim.o
                0x00000000000010d8                TIM_SetCompare1
 .text.TIM_GetITStatus
                0x00000000000010dc       0x18 ./Peripheral/src/ch32v00x_tim.o
                0x00000000000010dc                TIM_GetITStatus
 .text.TIM_ClearITPendingBit
                0x00000000000010f4        0xc ./Peripheral/src/ch32v00x_tim.o
                0x00000000000010f4                TIM_ClearITPendingBit
 .text.USART_Init
                0x0000000000001100       0xcc ./Peripheral/src/ch32v00x_usart.o
                0x0000000000001100                USART_Init
 .text.USART_Cmd
                0x00000000000011cc       0x16 ./Peripheral/src/ch32v00x_usart.o
                0x00000000000011cc                USART_Cmd
 .text.USART_SendData
                0x00000000000011e2        0x8 ./Peripheral/src/ch32v00x_usart.o
                0x00000000000011e2                USART_SendData
 .text.USART_GetFlagStatus
                0x00000000000011ea        0xa ./Peripheral/src/ch32v00x_usart.o
                0x00000000000011ea                USART_GetFlagStatus
 .text.Delay_Init
                0x00000000000011f4       0x34 ./Debug/debug.o
                0x00000000000011f4                Delay_Init
 .text.Delay_Ms
                0x0000000000001228       0x3a ./Debug/debug.o
                0x0000000000001228                Delay_Ms
 .text.USART_Printf_Init
                0x0000000000001262       0x56 ./Debug/debug.o
                0x0000000000001262                USART_Printf_Init
 .text._write   0x00000000000012b8       0x4c ./Debug/debug.o
                0x00000000000012b8                _write
 .text.printchar
                0x0000000000001304       0x48 /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
                0x0000000000001304                printchar
 .text.prints   0x000000000000134c       0xe6 /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
                0x000000000000134c                prints
 .text.printInt
                0x0000000000001432      0x10e /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
                0x0000000000001432                printInt
 .text.printLongLongInt
                0x0000000000001540        0x4 /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
                0x0000000000001540                printLongLongInt
 .text.printDouble
                0x0000000000001544        0x4 /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
                0x0000000000001544                printDouble
 .text.print    0x0000000000001548      0x358 /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
                0x0000000000001548                print
 .text.printf   0x00000000000018a0       0x24 /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
                0x00000000000018a0                printf
 .text.puts     0x00000000000018c4       0x4a /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
                0x00000000000018c4                puts
 .text.memcpy   0x000000000000190e       0xd2 /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libg_nano.a(lib_a-memcpy.o)
                0x000000000000190e                memcpy
 *(.rodata)
 *fill*         0x00000000000019e0        0x0 
 .rodata        0x00000000000019e0       0x20 ./User/st7735.o
 *(.rodata*)
 .rodata.ADC_Display_Draw_Channel_Labels.str1.4
                0x0000000000001a00        0x6 ./User/adc_display.o
 *fill*         0x0000000000001a06        0x2 
 .rodata.ADC_Display_Draw_Header.str1.4
                0x0000000000001a08        0xc ./User/adc_display.o
 .rodata.CSWTCH.2
                0x0000000000001a14        0x8 ./User/adc_display.o
 .rodata.Display_Control_Show_Off_Message.str1.4
                0x0000000000001a1c        0xc ./User/display_control.o
 .rodata.Display_Control_Show_Startup_Message.str1.4
                0x0000000000001a28       0x3d ./User/display_control.o
 *fill*         0x0000000000001a65        0x3 
 .rodata.System_Init.str1.4
                0x0000000000001a68       0xa0 ./User/main.o
 .rodata.main.str1.4
                0x0000000000001b08       0xc2 ./User/main.o
 *fill*         0x0000000000001bca        0x2 
 .rodata.font   0x0000000000001bcc      0x500 ./User/st7735.o
 .rodata.print.str1.4
                0x00000000000020cc        0x8 /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
                                          0x7 (size before relaxing)
 *(.gnu.linkonce.t.*)
                0x00000000000020d4                . = ALIGN (0x4)

.rela.dyn       0x00000000000020d4        0x0
 .rela.text.startup.main
                0x00000000000020d4        0x0 ./User/adc_display.o
 .rela.init     0x00000000000020d4        0x0 ./User/adc_display.o
 .rela.text.handle_reset
                0x00000000000020d4        0x0 ./User/adc_display.o
 .rela.text.prints
                0x00000000000020d4        0x0 ./User/adc_display.o
 .rela.text.printInt
                0x00000000000020d4        0x0 ./User/adc_display.o
 .rela.text.print
                0x00000000000020d4        0x0 ./User/adc_display.o
 .rela.text.printf
                0x00000000000020d4        0x0 ./User/adc_display.o

.fini           0x00000000000020d4        0x0
 *(SORT_NONE(.fini))
                0x00000000000020d4                . = ALIGN (0x4)
                [!provide]                        PROVIDE (_etext = .)
                [!provide]                        PROVIDE (_eitcm = .)

.preinit_array  0x00000000000020d4        0x0
                [!provide]                        PROVIDE (__preinit_array_start = .)
 *(.preinit_array)
                [!provide]                        PROVIDE (__preinit_array_end = .)

.init_array     0x00000000000020d4        0x0
                [!provide]                        PROVIDE (__init_array_start = .)
 *(SORT_BY_INIT_PRIORITY(.init_array.*) SORT_BY_INIT_PRIORITY(.ctors.*))
 *(.init_array EXCLUDE_FILE(*crtend?.o *crtend.o *crtbegin?.o *crtbegin.o) .ctors)
                [!provide]                        PROVIDE (__init_array_end = .)

.fini_array     0x00000000000020d4        0x0
                [!provide]                        PROVIDE (__fini_array_start = .)
 *(SORT_BY_INIT_PRIORITY(.fini_array.*) SORT_BY_INIT_PRIORITY(.dtors.*))
 *(.fini_array EXCLUDE_FILE(*crtend?.o *crtend.o *crtbegin?.o *crtbegin.o) .dtors)
                [!provide]                        PROVIDE (__fini_array_end = .)

.ctors
 *crtbegin.o(.ctors)
 *crtbegin?.o(.ctors)
 *(EXCLUDE_FILE(*crtend?.o *crtend.o) .ctors)
 *(SORT_BY_NAME(.ctors.*))
 *(.ctors)

.dtors
 *crtbegin.o(.dtors)
 *crtbegin?.o(.dtors)
 *(EXCLUDE_FILE(*crtend?.o *crtend.o) .dtors)
 *(SORT_BY_NAME(.dtors.*))
 *(.dtors)

.dalign         0x0000000020000000        0x0 load address 0x00000000000020d4
                0x0000000020000000                . = ALIGN (0x4)
                0x0000000020000000                PROVIDE (_data_vma = .)

.dlalign        0x00000000000020d4        0x0
                0x00000000000020d4                . = ALIGN (0x4)
                0x00000000000020d4                PROVIDE (_data_lma = .)

.data           0x0000000020000000       0x40 load address 0x00000000000020d4
                0x0000000020000000                . = ALIGN (0x4)
 *(.gnu.linkonce.r.*)
 *(.data .data.*)
 .data._color   0x0000000020000000        0x2 ./User/st7735.o
 *fill*         0x0000000020000002        0x2 
 .data.AHBPrescTable
                0x0000000020000004       0x10 ./User/system_ch32v00x.o
                0x0000000020000004                AHBPrescTable
 .data.SystemCoreClock
                0x0000000020000014        0x4 ./User/system_ch32v00x.o
                0x0000000020000014                SystemCoreClock
 .data.ADCPrescTable
                0x0000000020000018       0x14 ./Peripheral/src/ch32v00x_rcc.o
 .data.APBAHBPrescTable
                0x000000002000002c       0x10 ./Peripheral/src/ch32v00x_rcc.o
 *(.gnu.linkonce.d.*)
                0x0000000020000040                . = ALIGN (0x8)
 *fill*         0x000000002000003c        0x4 
                0x0000000020000840                PROVIDE (__global_pointer$ = (. + 0x800))
 *(.sdata .sdata.*)
 *(.sdata2*)
 *(.gnu.linkonce.s.*)
                0x0000000020000040                . = ALIGN (0x8)
 *(.srodata.cst16)
 *(.srodata.cst8)
 *(.srodata.cst4)
 *(.srodata.cst2)
 *(.srodata .srodata.*)
                0x0000000020000040                . = ALIGN (0x4)
                0x0000000020000040                PROVIDE (_edata = .)

.bss            0x0000000020000040      0x194 load address 0x0000000000002114
                0x0000000020000040                . = ALIGN (0x4)
                0x0000000020000040                PROVIDE (_sbss = .)
 *(.sbss*)
 *(.gnu.linkonce.sb.*)
 *(.bss*)
 .bss.adc_display_config
                0x0000000020000040        0xc ./User/adc_display.o
                0x0000000020000040                adc_display_config
 .bss.display_control
                0x000000002000004c        0xc ./User/display_control.o
                0x000000002000004c                display_control
 .bss.system_initialized
                0x0000000020000058        0x1 ./User/main.o
                0x0000000020000058                system_initialized
 *fill*         0x0000000020000059        0x3 
 .bss.pwm_control
                0x000000002000005c        0x6 ./User/pwm_config.o
                0x000000002000005c                pwm_control
 .bss._bg_color
                0x0000000020000062        0x2 ./User/st7735.o
 .bss._buffer   0x0000000020000064      0x140 ./User/st7735.o
 .bss._cursor_x
                0x00000000200001a4        0x2 ./User/st7735.o
 .bss._cursor_y
                0x00000000200001a6        0x2 ./User/st7735.o
 .bss.str.4169  0x00000000200001a8        0xc ./User/st7735.o
 .bss.system_tick_ms
                0x00000000200001b4        0x4 ./User/touch_button.o
                0x00000000200001b4                system_tick_ms
 .bss.touch_button
                0x00000000200001b8       0x14 ./User/touch_button.o
                0x00000000200001b8                touch_button
 .bss.NVIC_Priority_Group
                0x00000000200001cc        0x4 ./Peripheral/src/ch32v00x_misc.o
                0x00000000200001cc                NVIC_Priority_Group
 .bss.p_ms      0x00000000200001d0        0x2 ./Debug/debug.o
 .bss.p_us      0x00000000200001d2        0x1 ./Debug/debug.o
 *(.gnu.linkonce.b.*)
 *(COMMON*)
                0x00000000200001d4                . = ALIGN (0x4)
 *fill*         0x00000000200001d3        0x1 
                0x00000000200001d4                PROVIDE (_ebss = .)
                0x00000000200001d4                PROVIDE (_end = _ebss)
                [!provide]                        PROVIDE (end = .)

.stack          0x0000000020000700      0x100
                0x0000000020000700                PROVIDE (_heap_end = .)
                0x0000000020000700                . = ALIGN (0x4)
                [!provide]                        PROVIDE (_susrstack = .)
                0x0000000020000800                . = (. + __stack_size)
 *fill*         0x0000000020000700      0x100 
                0x0000000020000800                PROVIDE (_eusrstack = .)
OUTPUT(CH32V003F4U6.elf elf32-littleriscv)

.debug_info     0x0000000000000000    0x11cbb
 .debug_info    0x0000000000000000     0x12fe ./User/adc_display.o
 .debug_info    0x00000000000012fe      0xf8e ./User/ch32v00x_it.o
 .debug_info    0x000000000000228c      0xe8f ./User/display_control.o
 .debug_info    0x000000000000311b      0xf0b ./User/main.o
 .debug_info    0x0000000000004026     0x11b1 ./User/pwm_config.o
 .debug_info    0x00000000000051d7     0x1969 ./User/st7735.o
 .debug_info    0x0000000000006b40      0xc94 ./User/system_ch32v00x.o
 .debug_info    0x00000000000077d4     0x13a0 ./User/touch_button.o
 .debug_info    0x0000000000008b74       0x22 ./Startup/startup_ch32v00x.o
 .debug_info    0x0000000000008b96      0xa94 ./Peripheral/src/ch32v00x_dbgmcu.o
 .debug_info    0x000000000000962a      0xc05 ./Peripheral/src/ch32v00x_exti.o
 .debug_info    0x000000000000a22f     0x1034 ./Peripheral/src/ch32v00x_gpio.o
 .debug_info    0x000000000000b263      0xdc5 ./Peripheral/src/ch32v00x_misc.o
 .debug_info    0x000000000000c028     0x108c ./Peripheral/src/ch32v00x_rcc.o
 .debug_info    0x000000000000d0b4     0x297f ./Peripheral/src/ch32v00x_tim.o
 .debug_info    0x000000000000fa33     0x1351 ./Peripheral/src/ch32v00x_usart.o
 .debug_info    0x0000000000010d84      0xf37 ./Debug/debug.o

.debug_abbrev   0x0000000000000000     0x32a6
 .debug_abbrev  0x0000000000000000      0x383 ./User/adc_display.o
 .debug_abbrev  0x0000000000000383      0x2b5 ./User/ch32v00x_it.o
 .debug_abbrev  0x0000000000000638      0x274 ./User/display_control.o
 .debug_abbrev  0x00000000000008ac      0x28b ./User/main.o
 .debug_abbrev  0x0000000000000b37      0x34e ./User/pwm_config.o
 .debug_abbrev  0x0000000000000e85      0x45d ./User/st7735.o
 .debug_abbrev  0x00000000000012e2      0x2ee ./User/system_ch32v00x.o
 .debug_abbrev  0x00000000000015d0      0x346 ./User/touch_button.o
 .debug_abbrev  0x0000000000001916       0x12 ./Startup/startup_ch32v00x.o
 .debug_abbrev  0x0000000000001928      0x2f7 ./Peripheral/src/ch32v00x_dbgmcu.o
 .debug_abbrev  0x0000000000001c1f      0x2da ./Peripheral/src/ch32v00x_exti.o
 .debug_abbrev  0x0000000000001ef9      0x309 ./Peripheral/src/ch32v00x_gpio.o
 .debug_abbrev  0x0000000000002202      0x2e1 ./Peripheral/src/ch32v00x_misc.o
 .debug_abbrev  0x00000000000024e3      0x389 ./Peripheral/src/ch32v00x_rcc.o
 .debug_abbrev  0x000000000000286c      0x408 ./Peripheral/src/ch32v00x_tim.o
 .debug_abbrev  0x0000000000002c74      0x323 ./Peripheral/src/ch32v00x_usart.o
 .debug_abbrev  0x0000000000002f97      0x30f ./Debug/debug.o

.debug_loc      0x0000000000000000     0x45d5
 .debug_loc     0x0000000000000000      0x53e ./User/adc_display.o
 .debug_loc     0x000000000000053e       0x29 ./User/main.o
 .debug_loc     0x0000000000000567      0x178 ./User/pwm_config.o
 .debug_loc     0x00000000000006df      0xdf3 ./User/st7735.o
 .debug_loc     0x00000000000014d2       0x55 ./User/system_ch32v00x.o
 .debug_loc     0x0000000000001527       0x9e ./User/touch_button.o
 .debug_loc     0x00000000000015c5       0x7c ./Peripheral/src/ch32v00x_dbgmcu.o
 .debug_loc     0x0000000000001641      0x181 ./Peripheral/src/ch32v00x_exti.o
 .debug_loc     0x00000000000017c2      0x5e2 ./Peripheral/src/ch32v00x_gpio.o
 .debug_loc     0x0000000000001da4       0x47 ./Peripheral/src/ch32v00x_misc.o
 .debug_loc     0x0000000000001deb      0x5e3 ./Peripheral/src/ch32v00x_rcc.o
 .debug_loc     0x00000000000023ce     0x1936 ./Peripheral/src/ch32v00x_tim.o
 .debug_loc     0x0000000000003d04      0x735 ./Peripheral/src/ch32v00x_usart.o
 .debug_loc     0x0000000000004439      0x19c ./Debug/debug.o

.debug_aranges  0x0000000000000000      0x980
 .debug_aranges
                0x0000000000000000       0x78 ./User/adc_display.o
 .debug_aranges
                0x0000000000000078       0x38 ./User/ch32v00x_it.o
 .debug_aranges
                0x00000000000000b0       0x70 ./User/display_control.o
 .debug_aranges
                0x0000000000000120       0x28 ./User/main.o
 .debug_aranges
                0x0000000000000148       0x68 ./User/pwm_config.o
 .debug_aranges
                0x00000000000001b0       0xb0 ./User/st7735.o
 .debug_aranges
                0x0000000000000260       0x28 ./User/system_ch32v00x.o
 .debug_aranges
                0x0000000000000288       0x68 ./User/touch_button.o
 .debug_aranges
                0x00000000000002f0       0x30 ./Startup/startup_ch32v00x.o
 .debug_aranges
                0x0000000000000320       0x48 ./Peripheral/src/ch32v00x_dbgmcu.o
 .debug_aranges
                0x0000000000000368       0x50 ./Peripheral/src/ch32v00x_exti.o
 .debug_aranges
                0x00000000000003b8       0x98 ./Peripheral/src/ch32v00x_gpio.o
 .debug_aranges
                0x0000000000000450       0x28 ./Peripheral/src/ch32v00x_misc.o
 .debug_aranges
                0x0000000000000478       0xe0 ./Peripheral/src/ch32v00x_rcc.o
 .debug_aranges
                0x0000000000000558      0x2d8 ./Peripheral/src/ch32v00x_tim.o
 .debug_aranges
                0x0000000000000830      0x100 ./Peripheral/src/ch32v00x_usart.o
 .debug_aranges
                0x0000000000000930       0x50 ./Debug/debug.o

.debug_ranges   0x0000000000000000      0xad8
 .debug_ranges  0x0000000000000000       0xf0 ./User/adc_display.o
 .debug_ranges  0x00000000000000f0       0x28 ./User/ch32v00x_it.o
 .debug_ranges  0x0000000000000118       0x60 ./User/display_control.o
 .debug_ranges  0x0000000000000178       0x38 ./User/main.o
 .debug_ranges  0x00000000000001b0       0x70 ./User/pwm_config.o
 .debug_ranges  0x0000000000000220      0x158 ./User/st7735.o
 .debug_ranges  0x0000000000000378       0x50 ./User/system_ch32v00x.o
 .debug_ranges  0x00000000000003c8       0xa8 ./User/touch_button.o
 .debug_ranges  0x0000000000000470       0x28 ./Startup/startup_ch32v00x.o
 .debug_ranges  0x0000000000000498       0x50 ./Peripheral/src/ch32v00x_dbgmcu.o
 .debug_ranges  0x00000000000004e8       0x40 ./Peripheral/src/ch32v00x_exti.o
 .debug_ranges  0x0000000000000528       0x88 ./Peripheral/src/ch32v00x_gpio.o
 .debug_ranges  0x00000000000005b0       0x30 ./Peripheral/src/ch32v00x_misc.o
 .debug_ranges  0x00000000000005e0       0xd0 ./Peripheral/src/ch32v00x_rcc.o
 .debug_ranges  0x00000000000006b0      0x2f8 ./Peripheral/src/ch32v00x_tim.o
 .debug_ranges  0x00000000000009a8       0xf0 ./Peripheral/src/ch32v00x_usart.o
 .debug_ranges  0x0000000000000a98       0x40 ./Debug/debug.o

.debug_line     0x0000000000000000     0xb967
 .debug_line    0x0000000000000000      0x93f ./User/adc_display.o
 .debug_line    0x000000000000093f      0x40c ./User/ch32v00x_it.o
 .debug_line    0x0000000000000d4b      0x64b ./User/display_control.o
 .debug_line    0x0000000000001396      0x52c ./User/main.o
 .debug_line    0x00000000000018c2      0x832 ./User/pwm_config.o
 .debug_line    0x00000000000020f4     0x1457 ./User/st7735.o
 .debug_line    0x000000000000354b      0x740 ./User/system_ch32v00x.o
 .debug_line    0x0000000000003c8b      0xa28 ./User/touch_button.o
 .debug_line    0x00000000000046b3      0x148 ./Startup/startup_ch32v00x.o
 .debug_line    0x00000000000047fb      0x3e7 ./Peripheral/src/ch32v00x_dbgmcu.o
 .debug_line    0x0000000000004be2      0x640 ./Peripheral/src/ch32v00x_exti.o
 .debug_line    0x0000000000005222      0xc67 ./Peripheral/src/ch32v00x_gpio.o
 .debug_line    0x0000000000005e89      0x44e ./Peripheral/src/ch32v00x_misc.o
 .debug_line    0x00000000000062d7      0xe6e ./Peripheral/src/ch32v00x_rcc.o
 .debug_line    0x0000000000007145     0x30c9 ./Peripheral/src/ch32v00x_tim.o
 .debug_line    0x000000000000a20e      0xf78 ./Peripheral/src/ch32v00x_usart.o
 .debug_line    0x000000000000b186      0x7e1 ./Debug/debug.o

.debug_str      0x0000000000000000     0x2ec7
 .debug_str     0x0000000000000000      0x998 ./User/adc_display.o
                                        0xa44 (size before relaxing)
 .debug_str     0x0000000000000998      0x22e ./User/ch32v00x_it.o
                                        0x92e (size before relaxing)
 .debug_str     0x0000000000000bc6      0x27f ./User/display_control.o
                                        0x9d3 (size before relaxing)
 .debug_str     0x0000000000000e45      0x104 ./User/main.o
                                        0xa3a (size before relaxing)
 .debug_str     0x0000000000000f49      0x3f9 ./User/pwm_config.o
                                        0xb7b (size before relaxing)
 .debug_str     0x0000000000001342      0x240 ./User/st7735.o
                                        0x98f (size before relaxing)
 .debug_str     0x0000000000001582       0xcd ./User/system_ch32v00x.o
                                        0x758 (size before relaxing)
 .debug_str     0x000000000000164f      0x42e ./User/touch_button.o
                                        0xee9 (size before relaxing)
 .debug_str     0x0000000000001a7d       0x2a ./Startup/startup_ch32v00x.o
                                         0x64 (size before relaxing)
 .debug_str     0x0000000000001aa7       0x87 ./Peripheral/src/ch32v00x_dbgmcu.o
                                        0x656 (size before relaxing)
 .debug_str     0x0000000000001b2e       0xc6 ./Peripheral/src/ch32v00x_exti.o
                                        0x79d (size before relaxing)
 .debug_str     0x0000000000001bf4      0x1b9 ./Peripheral/src/ch32v00x_gpio.o
                                        0x953 (size before relaxing)
 .debug_str     0x0000000000001dad       0x9c ./Peripheral/src/ch32v00x_misc.o
                                        0x91b (size before relaxing)
 .debug_str     0x0000000000001e49      0x2b0 ./Peripheral/src/ch32v00x_rcc.o
                                        0x9c1 (size before relaxing)
 .debug_str     0x00000000000020f9      0x92e ./Peripheral/src/ch32v00x_tim.o
                                       0x127b (size before relaxing)
 .debug_str     0x0000000000002a27      0x410 ./Peripheral/src/ch32v00x_usart.o
                                        0xb10 (size before relaxing)
 .debug_str     0x0000000000002e37       0x90 ./Debug/debug.o
                                        0x911 (size before relaxing)

.comment        0x0000000000000000       0x33
 .comment       0x0000000000000000       0x33 ./User/adc_display.o
                                         0x34 (size before relaxing)
 .comment       0x0000000000000033       0x34 ./User/ch32v00x_it.o
 .comment       0x0000000000000033       0x34 ./User/display_control.o
 .comment       0x0000000000000033       0x34 ./User/main.o
 .comment       0x0000000000000033       0x34 ./User/pwm_config.o
 .comment       0x0000000000000033       0x34 ./User/st7735.o
 .comment       0x0000000000000033       0x34 ./User/system_ch32v00x.o
 .comment       0x0000000000000033       0x34 ./User/touch_button.o
 .comment       0x0000000000000033       0x34 ./Peripheral/src/ch32v00x_dbgmcu.o
 .comment       0x0000000000000033       0x34 ./Peripheral/src/ch32v00x_exti.o
 .comment       0x0000000000000033       0x34 ./Peripheral/src/ch32v00x_gpio.o
 .comment       0x0000000000000033       0x34 ./Peripheral/src/ch32v00x_misc.o
 .comment       0x0000000000000033       0x34 ./Peripheral/src/ch32v00x_rcc.o
 .comment       0x0000000000000033       0x34 ./Peripheral/src/ch32v00x_tim.o
 .comment       0x0000000000000033       0x34 ./Peripheral/src/ch32v00x_usart.o
 .comment       0x0000000000000033       0x34 ./Debug/debug.o
 .comment       0x0000000000000033       0x34 /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)

.debug_frame    0x0000000000000000     0x15a4
 .debug_frame   0x0000000000000000      0x180 ./User/adc_display.o
 .debug_frame   0x0000000000000180       0x50 ./User/ch32v00x_it.o
 .debug_frame   0x00000000000001d0      0x12c ./User/display_control.o
 .debug_frame   0x00000000000002fc       0x44 ./User/main.o
 .debug_frame   0x0000000000000340      0x108 ./User/pwm_config.o
 .debug_frame   0x0000000000000448      0x260 ./User/st7735.o
 .debug_frame   0x00000000000006a8       0x58 ./User/system_ch32v00x.o
 .debug_frame   0x0000000000000700      0x100 ./User/touch_button.o
 .debug_frame   0x0000000000000800       0x70 ./Peripheral/src/ch32v00x_dbgmcu.o
 .debug_frame   0x0000000000000870       0x90 ./Peripheral/src/ch32v00x_exti.o
 .debug_frame   0x0000000000000900      0x154 ./Peripheral/src/ch32v00x_gpio.o
 .debug_frame   0x0000000000000a54       0x30 ./Peripheral/src/ch32v00x_misc.o
 .debug_frame   0x0000000000000a84      0x1dc ./Peripheral/src/ch32v00x_rcc.o
 .debug_frame   0x0000000000000c60      0x624 ./Peripheral/src/ch32v00x_tim.o
 .debug_frame   0x0000000000001284      0x204 ./Peripheral/src/ch32v00x_usart.o
 .debug_frame   0x0000000000001488       0xe4 ./Debug/debug.o
 .debug_frame   0x000000000000156c       0x38 /opt/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libg_nano.a(lib_a-memcpy.o)
