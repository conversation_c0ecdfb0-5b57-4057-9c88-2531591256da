Archive member included to satisfy reference by file (symbol)

/home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
                              ./User/main.o (printf)
/home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(save-restore.o)
                              ./User/main.o (__riscv_save_2)
/home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(muldi3.o)
                              ./User/st7735.o (__mulsi3)
/home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(div.o)
                              ./User/st7735.o (__divsi3)
/home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libg_nano.a(lib_a-memcpy.o)
                              ./User/st7735.o (memcpy)

Discarded input sections

 .text          0x0000000000000000        0x0 ./User/ch32v00x_it.o
 .data          0x0000000000000000        0x0 ./User/ch32v00x_it.o
 .bss           0x0000000000000000        0x0 ./User/ch32v00x_it.o
 .text          0x0000000000000000        0x0 ./User/main.o
 .data          0x0000000000000000        0x0 ./User/main.o
 .bss           0x0000000000000000        0x0 ./User/main.o
 .text          0x0000000000000000        0x0 ./User/st7735.o
 .data          0x0000000000000000        0x0 ./User/st7735.o
 .bss           0x0000000000000000        0x0 ./User/st7735.o
 .text.SPI_send_DMA
                0x0000000000000000       0x3e ./User/st7735.o
 .text.SPI_send
                0x0000000000000000       0x12 ./User/st7735.o
 .text.write_command_8
                0x0000000000000000       0x24 ./User/st7735.o
 .text.write_data_16
                0x0000000000000000       0x34 ./User/st7735.o
 .text.tft_set_window
                0x0000000000000000       0x6e ./User/st7735.o
 .text._tft_draw_fast_v_line
                0x0000000000000000       0x94 ./User/st7735.o
 .text._tft_draw_fast_h_line
                0x0000000000000000       0x92 ./User/st7735.o
 .text.tft_init
                0x0000000000000000      0x202 ./User/st7735.o
 .text.tft_set_cursor
                0x0000000000000000       0x16 ./User/st7735.o
 .text.tft_set_color
                0x0000000000000000        0xa ./User/st7735.o
 .text.tft_set_background_color
                0x0000000000000000        0xa ./User/st7735.o
 .text.tft_print_char
                0x0000000000000000      0x10a ./User/st7735.o
 .text.tft_print
                0x0000000000000000       0x32 ./User/st7735.o
 .text.tft_print_number
                0x0000000000000000       0xc8 ./User/st7735.o
 .text.tft_draw_pixel
                0x0000000000000000       0x4c ./User/st7735.o
 .text.tft_fill_rect
                0x0000000000000000       0xa6 ./User/st7735.o
 .text.tft_draw_bitmap
                0x0000000000000000       0x82 ./User/st7735.o
 .text.tft_draw_rect
                0x0000000000000000       0x98 ./User/st7735.o
 .text.tft_draw_line
                0x0000000000000000      0x178 ./User/st7735.o
 .rodata        0x0000000000000000       0x20 ./User/st7735.o
 .bss._bg_color
                0x0000000000000000        0x2 ./User/st7735.o
 .bss._buffer   0x0000000000000000      0x140 ./User/st7735.o
 .bss._cursor_x
                0x0000000000000000        0x2 ./User/st7735.o
 .bss._cursor_y
                0x0000000000000000        0x2 ./User/st7735.o
 .bss.str.4169  0x0000000000000000        0xc ./User/st7735.o
 .data._color   0x0000000000000000        0x2 ./User/st7735.o
 .rodata.font   0x0000000000000000      0x500 ./User/st7735.o
 .debug_info    0x0000000000000000     0x196b ./User/st7735.o
 .debug_abbrev  0x0000000000000000      0x46e ./User/st7735.o
 .debug_loc     0x0000000000000000      0xdf3 ./User/st7735.o
 .debug_aranges
                0x0000000000000000       0xb0 ./User/st7735.o
 .debug_ranges  0x0000000000000000      0x158 ./User/st7735.o
 .debug_line    0x0000000000000000     0x1493 ./User/st7735.o
 .debug_str     0x0000000000000000      0x98f ./User/st7735.o
 .comment       0x0000000000000000       0x34 ./User/st7735.o
 .debug_frame   0x0000000000000000      0x260 ./User/st7735.o
 .text          0x0000000000000000        0x0 ./User/system_ch32v00x.o
 .data          0x0000000000000000        0x0 ./User/system_ch32v00x.o
 .bss           0x0000000000000000        0x0 ./User/system_ch32v00x.o
 .text          0x0000000000000000        0x0 ./Startup/startup_ch32v00x.o
 .data          0x0000000000000000        0x0 ./Startup/startup_ch32v00x.o
 .bss           0x0000000000000000        0x0 ./Startup/startup_ch32v00x.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_adc.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_adc.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_DeInit
                0x0000000000000000       0x3a ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_Init
                0x0000000000000000       0x4e ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_StructInit
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_Cmd  0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_DMACmd
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_ITConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_ResetCalibration
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_GetResetCalibrationStatus
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_StartCalibration
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_GetCalibrationStatus
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_SoftwareStartConvCmd
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_GetSoftwareStartConvStatus
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_DiscModeChannelCountConfig
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_DiscModeCmd
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_RegularChannelConfig
                0x0000000000000000       0xba ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_ExternalTrigConvCmd
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_GetConversionValue
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_AutoInjectedConvCmd
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_InjectedDiscModeCmd
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_ExternalTrigInjectedConvConfig
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_ExternalTrigInjectedConvCmd
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_SoftwareStartInjectedConvCmd
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_GetSoftwareStartInjectedConvCmdStatus
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_InjectedChannelConfig
                0x0000000000000000       0x7a ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_InjectedSequencerLengthConfig
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_SetInjectedOffset
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_GetInjectedConversionValue
                0x0000000000000000       0x1c ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_AnalogWatchdogCmd
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_AnalogWatchdogThresholdsConfig
                0x0000000000000000        0x6 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_AnalogWatchdogSingleChannelConfig
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_GetFlagStatus
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_ClearFlag
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_GetITStatus
                0x0000000000000000       0x1c ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_ClearITPendingBit
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_Calibration_Vol
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_adc.o
 .text.ADC_ExternalTrig_DLY
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_adc.o
 .debug_info    0x0000000000000000     0x1526 ./Peripheral/src/ch32v00x_adc.o
 .debug_abbrev  0x0000000000000000      0x307 ./Peripheral/src/ch32v00x_adc.o
 .debug_loc     0x0000000000000000      0x9fa ./Peripheral/src/ch32v00x_adc.o
 .debug_aranges
                0x0000000000000000      0x138 ./Peripheral/src/ch32v00x_adc.o
 .debug_ranges  0x0000000000000000      0x128 ./Peripheral/src/ch32v00x_adc.o
 .debug_line    0x0000000000000000     0x14e8 ./Peripheral/src/ch32v00x_adc.o
 .debug_str     0x0000000000000000      0xb9a ./Peripheral/src/ch32v00x_adc.o
 .comment       0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_adc.o
 .debug_frame   0x0000000000000000      0x26c ./Peripheral/src/ch32v00x_adc.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_dbgmcu.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_dbgmcu.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_dbgmcu.o
 .text.DBGMCU_GetREVID
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_dbgmcu.o
 .text.DBGMCU_GetDEVID
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_dbgmcu.o
 .text.__get_DEBUG_CR
                0x0000000000000000        0x6 ./Peripheral/src/ch32v00x_dbgmcu.o
 .text.__set_DEBUG_CR
                0x0000000000000000        0x6 ./Peripheral/src/ch32v00x_dbgmcu.o
 .text.DBGMCU_Config
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_dbgmcu.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_dma.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_dma.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_DeInit
                0x0000000000000000       0x92 ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_Init
                0x0000000000000000       0x38 ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_StructInit
                0x0000000000000000       0x2e ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_Cmd  0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_ITConfig
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_SetCurrDataCounter
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_GetCurrDataCounter
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_GetFlagStatus
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_ClearFlag
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_GetITStatus
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_dma.o
 .text.DMA_ClearITPendingBit
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_dma.o
 .debug_info    0x0000000000000000      0xd17 ./Peripheral/src/ch32v00x_dma.o
 .debug_abbrev  0x0000000000000000      0x302 ./Peripheral/src/ch32v00x_dma.o
 .debug_loc     0x0000000000000000      0x14a ./Peripheral/src/ch32v00x_dma.o
 .debug_aranges
                0x0000000000000000       0x60 ./Peripheral/src/ch32v00x_dma.o
 .debug_ranges  0x0000000000000000       0x50 ./Peripheral/src/ch32v00x_dma.o
 .debug_line    0x0000000000000000      0x843 ./Peripheral/src/ch32v00x_dma.o
 .debug_str     0x0000000000000000      0x803 ./Peripheral/src/ch32v00x_dma.o
 .comment       0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_dma.o
 .debug_frame   0x0000000000000000       0xcc ./Peripheral/src/ch32v00x_dma.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_exti.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_exti.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_exti.o
 .text.EXTI_DeInit
                0x0000000000000000       0x22 ./Peripheral/src/ch32v00x_exti.o
 .text.EXTI_Init
                0x0000000000000000       0x6a ./Peripheral/src/ch32v00x_exti.o
 .text.EXTI_StructInit
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_exti.o
 .text.EXTI_GenerateSWInterrupt
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_exti.o
 .text.EXTI_GetFlagStatus
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_exti.o
 .text.EXTI_ClearFlag
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_exti.o
 .text.EXTI_GetITStatus
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_exti.o
 .text.EXTI_ClearITPendingBit
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_exti.o
 .debug_info    0x0000000000000000      0xc05 ./Peripheral/src/ch32v00x_exti.o
 .debug_abbrev  0x0000000000000000      0x2da ./Peripheral/src/ch32v00x_exti.o
 .debug_loc     0x0000000000000000      0x181 ./Peripheral/src/ch32v00x_exti.o
 .debug_aranges
                0x0000000000000000       0x50 ./Peripheral/src/ch32v00x_exti.o
 .debug_ranges  0x0000000000000000       0x40 ./Peripheral/src/ch32v00x_exti.o
 .debug_line    0x0000000000000000      0x67c ./Peripheral/src/ch32v00x_exti.o
 .debug_str     0x0000000000000000      0x79d ./Peripheral/src/ch32v00x_exti.o
 .comment       0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_exti.o
 .debug_frame   0x0000000000000000       0x90 ./Peripheral/src/ch32v00x_exti.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_flash.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_flash.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_flash.o
 .text.ROM_ERASE
                0x0000000000000000       0x60 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_SetLatency
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_Unlock
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_Lock
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_GetUserOptionByte
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_GetWriteProtectionOptionByte
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_GetReadOutProtectionStatus
                0x0000000000000000        0xc ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ITConfig
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_GetFlagStatus
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ClearFlag
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_GetStatus
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_GetBank1Status
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_WaitForLastOperation
                0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ErasePage
                0x0000000000000000       0x4c ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_EraseAllPages
                0x0000000000000000       0x48 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_EraseOptionBytes
                0x0000000000000000       0x9a ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ProgramWord
                0x0000000000000000       0x6e ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ProgramHalfWord
                0x0000000000000000       0x48 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ProgramOptionByteData
                0x0000000000000000       0x66 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_EnableWriteProtection
                0x0000000000000000       0x9e ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ReadOutProtection
                0x0000000000000000       0xae ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_UserOptionByteConfig
                0x0000000000000000       0x7a ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_WaitForLastBank1Operation
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_Unlock_Fast
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_Lock_Fast
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_BufReset
                0x0000000000000000       0x28 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_BufLoad
                0x0000000000000000       0x36 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ErasePage_Fast
                0x0000000000000000       0x36 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ProgramPage_Fast
                0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_flash.o
 .text.SystemReset_StartMode
                0x0000000000000000       0x56 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ROM_ERASE
                0x0000000000000000      0x140 ./Peripheral/src/ch32v00x_flash.o
 .text.FLASH_ROM_WRITE
                0x0000000000000000       0xf6 ./Peripheral/src/ch32v00x_flash.o
 .debug_info    0x0000000000000000     0x1481 ./Peripheral/src/ch32v00x_flash.o
 .debug_abbrev  0x0000000000000000      0x3fa ./Peripheral/src/ch32v00x_flash.o
 .debug_loc     0x0000000000000000      0xad0 ./Peripheral/src/ch32v00x_flash.o
 .debug_aranges
                0x0000000000000000      0x110 ./Peripheral/src/ch32v00x_flash.o
 .debug_ranges  0x0000000000000000      0x100 ./Peripheral/src/ch32v00x_flash.o
 .debug_line    0x0000000000000000     0x1cd5 ./Peripheral/src/ch32v00x_flash.o
 .debug_str     0x0000000000000000      0xac5 ./Peripheral/src/ch32v00x_flash.o
 .comment       0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_flash.o
 .debug_frame   0x0000000000000000      0x324 ./Peripheral/src/ch32v00x_flash.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_gpio.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_gpio.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_DeInit
                0x0000000000000000       0x68 ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_AFIODeInit
                0x0000000000000000       0x28 ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_StructInit
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_ReadInputDataBit
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_ReadInputData
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_ReadOutputDataBit
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_ReadOutputData
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_SetBits
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_ResetBits
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_WriteBit
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_Write
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_PinLockConfig
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_PinRemapConfig
                0x0000000000000000       0xc4 ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_EXTILineConfig
                0x0000000000000000       0x22 ./Peripheral/src/ch32v00x_gpio.o
 .text.GPIO_IPD_Unused
                0x0000000000000000       0xa4 ./Peripheral/src/ch32v00x_gpio.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_i2c.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_i2c.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_DeInit
                0x0000000000000000       0x3a ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_Init
                0x0000000000000000      0x100 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_StructInit
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_Cmd  0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_DMACmd
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_DMALastTransferCmd
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_GenerateSTART
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_GenerateSTOP
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_AcknowledgeConfig
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_OwnAddress2Config
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_DualAddressCmd
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_GeneralCallCmd
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_ITConfig
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_SendData
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_ReceiveData
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_Send7bitAddress
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_ReadRegister
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_SoftwareResetCmd
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_NACKPositionConfig
                0x0000000000000000       0x22 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_TransmitPEC
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_PECPositionConfig
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_CalculatePEC
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_GetPEC
                0x0000000000000000        0x6 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_ARPCmd
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_StretchClockCmd
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_FastModeDutyCycleConfig
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_CheckEvent
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_GetLastEvent
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_GetFlagStatus
                0x0000000000000000       0x32 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_ClearFlag
                0x0000000000000000        0xc ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_GetITStatus
                0x0000000000000000       0x20 ./Peripheral/src/ch32v00x_i2c.o
 .text.I2C_ClearITPendingBit
                0x0000000000000000        0xc ./Peripheral/src/ch32v00x_i2c.o
 .debug_info    0x0000000000000000     0x1417 ./Peripheral/src/ch32v00x_i2c.o
 .debug_abbrev  0x0000000000000000      0x3a7 ./Peripheral/src/ch32v00x_i2c.o
 .debug_loc     0x0000000000000000      0x720 ./Peripheral/src/ch32v00x_i2c.o
 .debug_aranges
                0x0000000000000000      0x108 ./Peripheral/src/ch32v00x_i2c.o
 .debug_ranges  0x0000000000000000       0xf8 ./Peripheral/src/ch32v00x_i2c.o
 .debug_line    0x0000000000000000     0x112a ./Peripheral/src/ch32v00x_i2c.o
 .debug_str     0x0000000000000000      0xac2 ./Peripheral/src/ch32v00x_i2c.o
 .comment       0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_i2c.o
 .debug_frame   0x0000000000000000      0x254 ./Peripheral/src/ch32v00x_i2c.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_iwdg.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_iwdg.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_iwdg.o
 .text.IWDG_WriteAccessCmd
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_iwdg.o
 .text.IWDG_SetPrescaler
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_iwdg.o
 .text.IWDG_SetReload
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_iwdg.o
 .text.IWDG_ReloadCounter
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_iwdg.o
 .text.IWDG_Enable
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_iwdg.o
 .text.IWDG_GetFlagStatus
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_iwdg.o
 .debug_info    0x0000000000000000      0xb46 ./Peripheral/src/ch32v00x_iwdg.o
 .debug_abbrev  0x0000000000000000      0x282 ./Peripheral/src/ch32v00x_iwdg.o
 .debug_loc     0x0000000000000000       0x68 ./Peripheral/src/ch32v00x_iwdg.o
 .debug_aranges
                0x0000000000000000       0x48 ./Peripheral/src/ch32v00x_iwdg.o
 .debug_ranges  0x0000000000000000       0x38 ./Peripheral/src/ch32v00x_iwdg.o
 .debug_line    0x0000000000000000      0x49b ./Peripheral/src/ch32v00x_iwdg.o
 .debug_str     0x0000000000000000      0x709 ./Peripheral/src/ch32v00x_iwdg.o
 .comment       0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_iwdg.o
 .debug_frame   0x0000000000000000       0x70 ./Peripheral/src/ch32v00x_iwdg.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_misc.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_misc.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_misc.o
 .text.NVIC_Init
                0x0000000000000000       0x66 ./Peripheral/src/ch32v00x_misc.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_opa.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_opa.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_opa.o
 .text.OPA_DeInit
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_opa.o
 .text.OPA_Init
                0x0000000000000000       0x22 ./Peripheral/src/ch32v00x_opa.o
 .text.OPA_StructInit
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_opa.o
 .text.OPA_Cmd  0x0000000000000000       0x2c ./Peripheral/src/ch32v00x_opa.o
 .debug_info    0x0000000000000000      0xaa5 ./Peripheral/src/ch32v00x_opa.o
 .debug_abbrev  0x0000000000000000      0x230 ./Peripheral/src/ch32v00x_opa.o
 .debug_loc     0x0000000000000000       0x35 ./Peripheral/src/ch32v00x_opa.o
 .debug_aranges
                0x0000000000000000       0x38 ./Peripheral/src/ch32v00x_opa.o
 .debug_ranges  0x0000000000000000       0x28 ./Peripheral/src/ch32v00x_opa.o
 .debug_line    0x0000000000000000      0x491 ./Peripheral/src/ch32v00x_opa.o
 .debug_str     0x0000000000000000      0x67d ./Peripheral/src/ch32v00x_opa.o
 .comment       0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_opa.o
 .debug_frame   0x0000000000000000       0x50 ./Peripheral/src/ch32v00x_opa.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_pwr.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_pwr.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_pwr.o
 .text.PWR_DeInit
                0x0000000000000000       0x2c ./Peripheral/src/ch32v00x_pwr.o
 .text.PWR_PVDCmd
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_pwr.o
 .text.PWR_PVDLevelConfig
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_pwr.o
 .text.PWR_AutoWakeUpCmd
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_pwr.o
 .text.PWR_AWU_SetPrescaler
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_pwr.o
 .text.PWR_AWU_SetWindowValue
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_pwr.o
 .text.PWR_EnterSTANDBYMode
                0x0000000000000000       0xfc ./Peripheral/src/ch32v00x_pwr.o
 .text.PWR_GetFlagStatus
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_pwr.o
 .debug_info    0x0000000000000000      0xf00 ./Peripheral/src/ch32v00x_pwr.o
 .debug_abbrev  0x0000000000000000      0x359 ./Peripheral/src/ch32v00x_pwr.o
 .debug_loc     0x0000000000000000      0x19a ./Peripheral/src/ch32v00x_pwr.o
 .debug_aranges
                0x0000000000000000       0x58 ./Peripheral/src/ch32v00x_pwr.o
 .debug_ranges  0x0000000000000000       0x90 ./Peripheral/src/ch32v00x_pwr.o
 .debug_line    0x0000000000000000      0x7ef ./Peripheral/src/ch32v00x_pwr.o
 .debug_str     0x0000000000000000      0x80f ./Peripheral/src/ch32v00x_pwr.o
 .comment       0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_pwr.o
 .debug_frame   0x0000000000000000       0xb8 ./Peripheral/src/ch32v00x_pwr.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_rcc.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_rcc.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_HSEConfig
                0x0000000000000000       0x3c ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_DeInit
                0x0000000000000000       0x58 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_HSICmd
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_PLLConfig
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_PLLCmd
                0x0000000000000000       0x24 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_SYSCLKConfig
                0x0000000000000000       0x4e ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_GetSYSCLKSource
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_HCLKConfig
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_ITConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_ADCCLKConfig
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_LSICmd
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_AHBPeriphClockCmd
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_APB1PeriphClockCmd
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_APB2PeriphResetCmd
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_APB1PeriphResetCmd
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_ClockSecuritySystemCmd
                0x0000000000000000       0x24 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_MCOConfig
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_GetFlagStatus
                0x0000000000000000       0x20 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_WaitForHSEStartUp
                0x0000000000000000       0x42 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_ClearFlag
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_GetITStatus
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_rcc.o
 .text.RCC_ClearITPendingBit
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_rcc.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_spi.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_spi.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_I2S_DeInit
                0x0000000000000000       0x32 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_Init
                0x0000000000000000       0x32 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_StructInit
                0x0000000000000000       0x22 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_Cmd  0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_I2S_ITConfig
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_I2S_DMACmd
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_I2S_SendData
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_I2S_ReceiveData
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_NSSInternalSoftwareConfig
                0x0000000000000000       0x24 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_SSOutputCmd
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_DataSizeConfig
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_TransmitCRC
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_CalculateCRC
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_GetCRC
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_GetCRCPolynomial
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_BiDirectionalLineConfig
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_I2S_GetFlagStatus
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_I2S_ClearFlag
                0x0000000000000000        0xc ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_I2S_GetITStatus
                0x0000000000000000       0x28 ./Peripheral/src/ch32v00x_spi.o
 .text.SPI_I2S_ClearITPendingBit
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_spi.o
 .debug_info    0x0000000000000000     0x1068 ./Peripheral/src/ch32v00x_spi.o
 .debug_abbrev  0x0000000000000000      0x303 ./Peripheral/src/ch32v00x_spi.o
 .debug_loc     0x0000000000000000      0x429 ./Peripheral/src/ch32v00x_spi.o
 .debug_aranges
                0x0000000000000000       0xb8 ./Peripheral/src/ch32v00x_spi.o
 .debug_ranges  0x0000000000000000       0xa8 ./Peripheral/src/ch32v00x_spi.o
 .debug_line    0x0000000000000000      0xa7d ./Peripheral/src/ch32v00x_spi.o
 .debug_str     0x0000000000000000      0x949 ./Peripheral/src/ch32v00x_spi.o
 .comment       0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_spi.o
 .debug_frame   0x0000000000000000      0x15c ./Peripheral/src/ch32v00x_spi.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_tim.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_tim.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_tim.o
 .text.TI1_Config
                0x0000000000000000       0x5a ./Peripheral/src/ch32v00x_tim.o
 .text.TI2_Config
                0x0000000000000000       0x70 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_DeInit
                0x0000000000000000       0x5c ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_TimeBaseInit
                0x0000000000000000       0x4e ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC1Init
                0x0000000000000000       0x6a ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC2Init
                0x0000000000000000       0x96 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC3Init
                0x0000000000000000       0x94 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC4Init
                0x0000000000000000       0x6e ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_BDTRConfig
                0x0000000000000000       0x20 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_TimeBaseStructInit
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OCStructInit
                0x0000000000000000       0x22 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ICStructInit
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_BDTRStructInit
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_Cmd  0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_CtrlPWMOutputs
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ITConfig
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_GenerateEvent
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_DMAConfig
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_DMACmd
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_InternalClockConfig
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ITRxExternalClockConfig
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_TIxExternalClockConfig
                0x0000000000000000       0x48 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ETRConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ETRClockMode1Config
                0x0000000000000000       0x2a ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ETRClockMode2Config
                0x0000000000000000       0x22 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_PrescalerConfig
                0x0000000000000000        0x6 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_CounterModeConfig
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SelectInputTrigger
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_EncoderInterfaceConfig
                0x0000000000000000       0x3c ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ForcedOC1Config
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ForcedOC2Config
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ForcedOC3Config
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ForcedOC4Config
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ARRPreloadConfig
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SelectCOM
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SelectCCDMA
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_CCPreloadControl
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC1PreloadConfig
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC2PreloadConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC3PreloadConfig
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC4PreloadConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC1FastConfig
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC2FastConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC3FastConfig
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC4FastConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ClearOC1Ref
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ClearOC2Ref
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ClearOC3Ref
                0x0000000000000000       0x10 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ClearOC4Ref
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC1PolarityConfig
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC1NPolarityConfig
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC2PolarityConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC2NPolarityConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC3PolarityConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC3NPolarityConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_OC4PolarityConfig
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_CCxCmd
                0x0000000000000000       0x20 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_CCxNCmd
                0x0000000000000000       0x20 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SelectOCxM
                0x0000000000000000       0x4c ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_UpdateDisableConfig
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_UpdateRequestConfig
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SelectHallSensor
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SelectOnePulseMode
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SelectOutputTrigger
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SelectSlaveMode
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SelectMasterSlaveMode
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SetCounter
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SetAutoreload
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SetCompare1
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SetCompare2
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SetCompare3
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SetCompare4
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SetIC1Prescaler
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SetIC2Prescaler
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_PWMIConfig
                0x0000000000000000       0xa4 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SetIC3Prescaler
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SetIC4Prescaler
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ICInit
                0x0000000000000000      0x148 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_SetClockDivision
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_GetCapture1
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_GetCapture2
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_GetCapture3
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_GetCapture4
                0x0000000000000000        0x8 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_GetCounter
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_GetPrescaler
                0x0000000000000000        0x4 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_GetFlagStatus
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ClearFlag
                0x0000000000000000        0xc ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_GetITStatus
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_ClearITPendingBit
                0x0000000000000000        0xc ./Peripheral/src/ch32v00x_tim.o
 .text.TIM_IndicateCaptureLevelCmd
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_tim.o
 .debug_info    0x0000000000000000     0x297f ./Peripheral/src/ch32v00x_tim.o
 .debug_abbrev  0x0000000000000000      0x408 ./Peripheral/src/ch32v00x_tim.o
 .debug_loc     0x0000000000000000     0x1936 ./Peripheral/src/ch32v00x_tim.o
 .debug_aranges
                0x0000000000000000      0x2d8 ./Peripheral/src/ch32v00x_tim.o
 .debug_ranges  0x0000000000000000      0x2f8 ./Peripheral/src/ch32v00x_tim.o
 .debug_line    0x0000000000000000     0x3105 ./Peripheral/src/ch32v00x_tim.o
 .debug_str     0x0000000000000000     0x127b ./Peripheral/src/ch32v00x_tim.o
 .comment       0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_tim.o
 .debug_frame   0x0000000000000000      0x624 ./Peripheral/src/ch32v00x_tim.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_usart.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_usart.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_DeInit
                0x0000000000000000       0x36 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_StructInit
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_ClockInit
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_usart.o
 .text.USART_ClockStructInit
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_ITConfig
                0x0000000000000000       0x36 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_DMACmd
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_SetAddress
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_WakeUpConfig
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_ReceiverWakeUpCmd
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_LINBreakDetectLengthConfig
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_LINCmd
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_SendBreak
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_usart.o
 .text.USART_SetGuardTime
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_SetPrescaler
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_SmartCardCmd
                0x0000000000000000       0x1a ./Peripheral/src/ch32v00x_usart.o
 .text.USART_SmartCardNACKCmd
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_HalfDuplexCmd
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_OverSampling8Cmd
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_OneBitMethodCmd
                0x0000000000000000       0x1e ./Peripheral/src/ch32v00x_usart.o
 .text.USART_IrDAConfig
                0x0000000000000000       0x16 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_IrDACmd
                0x0000000000000000       0x18 ./Peripheral/src/ch32v00x_usart.o
 .text.USART_ClearFlag
                0x0000000000000000        0xc ./Peripheral/src/ch32v00x_usart.o
 .text.USART_GetITStatus
                0x0000000000000000       0x3c ./Peripheral/src/ch32v00x_usart.o
 .text.USART_ClearITPendingBit
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_usart.o
 .text          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_wwdg.o
 .data          0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_wwdg.o
 .bss           0x0000000000000000        0x0 ./Peripheral/src/ch32v00x_wwdg.o
 .text.WWDG_DeInit
                0x0000000000000000       0x2e ./Peripheral/src/ch32v00x_wwdg.o
 .text.WWDG_SetPrescaler
                0x0000000000000000       0x14 ./Peripheral/src/ch32v00x_wwdg.o
 .text.WWDG_SetWindowValue
                0x0000000000000000       0x26 ./Peripheral/src/ch32v00x_wwdg.o
 .text.WWDG_EnableIT
                0x0000000000000000       0x12 ./Peripheral/src/ch32v00x_wwdg.o
 .text.WWDG_SetCounter
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_wwdg.o
 .text.WWDG_Enable
                0x0000000000000000        0xe ./Peripheral/src/ch32v00x_wwdg.o
 .text.WWDG_GetFlagStatus
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_wwdg.o
 .text.WWDG_ClearFlag
                0x0000000000000000        0xa ./Peripheral/src/ch32v00x_wwdg.o
 .debug_info    0x0000000000000000      0xb27 ./Peripheral/src/ch32v00x_wwdg.o
 .debug_abbrev  0x0000000000000000      0x29d ./Peripheral/src/ch32v00x_wwdg.o
 .debug_loc     0x0000000000000000       0xae ./Peripheral/src/ch32v00x_wwdg.o
 .debug_aranges
                0x0000000000000000       0x58 ./Peripheral/src/ch32v00x_wwdg.o
 .debug_ranges  0x0000000000000000       0x48 ./Peripheral/src/ch32v00x_wwdg.o
 .debug_line    0x0000000000000000      0x53f ./Peripheral/src/ch32v00x_wwdg.o
 .debug_str     0x0000000000000000      0x6c2 ./Peripheral/src/ch32v00x_wwdg.o
 .comment       0x0000000000000000       0x34 ./Peripheral/src/ch32v00x_wwdg.o
 .debug_frame   0x0000000000000000       0xa4 ./Peripheral/src/ch32v00x_wwdg.o
 .text          0x0000000000000000        0x0 ./Debug/debug.o
 .data          0x0000000000000000        0x0 ./Debug/debug.o
 .bss           0x0000000000000000        0x0 ./Debug/debug.o
 .text.Delay_Us
                0x0000000000000000       0x4a ./Debug/debug.o
 .text.Delay_Ms
                0x0000000000000000       0x4a ./Debug/debug.o
 .text.SDI_Printf_Enable
                0x0000000000000000       0x2a ./Debug/debug.o
 .text._sbrk    0x0000000000000000       0x2e ./Debug/debug.o
 .data.curbrk.4091
                0x0000000000000000        0x4 ./Debug/debug.o
 .text          0x0000000000000000        0x0 ./Core/core_riscv.o
 .data          0x0000000000000000        0x0 ./Core/core_riscv.o
 .bss           0x0000000000000000        0x0 ./Core/core_riscv.o
 .text.__get_MSTATUS
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__set_MSTATUS
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__get_MISA
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__set_MISA
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__get_MTVEC
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__set_MTVEC
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__get_MSCRATCH
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__set_MSCRATCH
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__get_MEPC
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__set_MEPC
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__get_MCAUSE
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__set_MCAUSE
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__get_MVENDORID
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__get_MARCHID
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__get_MIMPID
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__get_MHARTID
                0x0000000000000000        0x6 ./Core/core_riscv.o
 .text.__get_SP
                0x0000000000000000        0x4 ./Core/core_riscv.o
 .highcode      0x0000000000000000        0xa ./Core/core_riscv.o
 .debug_info    0x0000000000000000      0x349 ./Core/core_riscv.o
 .debug_abbrev  0x0000000000000000      0x10d ./Core/core_riscv.o
 .debug_aranges
                0x0000000000000000       0xa8 ./Core/core_riscv.o
 .debug_ranges  0x0000000000000000       0x98 ./Core/core_riscv.o
 .debug_line    0x0000000000000000      0x488 ./Core/core_riscv.o
 .debug_str     0x0000000000000000      0x294 ./Core/core_riscv.o
 .comment       0x0000000000000000       0x34 ./Core/core_riscv.o
 .debug_frame   0x0000000000000000      0x130 ./Core/core_riscv.o
 .text          0x0000000000000000        0x0 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
 .data          0x0000000000000000        0x0 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
 .text.sprintf  0x0000000000000000       0x2c /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
 .text.snprintf
                0x0000000000000000       0x2e /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
 .text.puts     0x0000000000000000       0x52 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
 .text.putchar  0x0000000000000000       0x1c /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
 .data          0x0000000000000000        0x0 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(save-restore.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(save-restore.o)
 .eh_frame      0x0000000000000000       0x68 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(save-restore.o)
 .text          0x0000000000000000       0x18 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(muldi3.o)
 .data          0x0000000000000000        0x0 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(muldi3.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(muldi3.o)
 .data          0x0000000000000000        0x0 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(div.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(div.o)
 .text          0x0000000000000000        0x0 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libg_nano.a(lib_a-memcpy.o)
 .data          0x0000000000000000        0x0 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libg_nano.a(lib_a-memcpy.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libg_nano.a(lib_a-memcpy.o)
 .text.memcpy   0x0000000000000000       0xd2 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libg_nano.a(lib_a-memcpy.o)
 .debug_frame   0x0000000000000000       0x38 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libg_nano.a(lib_a-memcpy.o)

Memory Configuration

Name             Origin             Length             Attributes
FLASH            0x0000000000000000 0x0000000000004000 xr
RAM              0x0000000020000000 0x0000000000000800 xrw
*default*        0x0000000000000000 0xffffffffffffffff

Linker script and memory map

LOAD ./User/ch32v00x_it.o
LOAD ./User/main.o
LOAD ./User/st7735.o
LOAD ./User/system_ch32v00x.o
LOAD ./Startup/startup_ch32v00x.o
LOAD ./Peripheral/src/ch32v00x_adc.o
LOAD ./Peripheral/src/ch32v00x_dbgmcu.o
LOAD ./Peripheral/src/ch32v00x_dma.o
LOAD ./Peripheral/src/ch32v00x_exti.o
LOAD ./Peripheral/src/ch32v00x_flash.o
LOAD ./Peripheral/src/ch32v00x_gpio.o
LOAD ./Peripheral/src/ch32v00x_i2c.o
LOAD ./Peripheral/src/ch32v00x_iwdg.o
LOAD ./Peripheral/src/ch32v00x_misc.o
LOAD ./Peripheral/src/ch32v00x_opa.o
LOAD ./Peripheral/src/ch32v00x_pwr.o
LOAD ./Peripheral/src/ch32v00x_rcc.o
LOAD ./Peripheral/src/ch32v00x_spi.o
LOAD ./Peripheral/src/ch32v00x_tim.o
LOAD ./Peripheral/src/ch32v00x_usart.o
LOAD ./Peripheral/src/ch32v00x_wwdg.o
LOAD ./Debug/debug.o
LOAD ./Core/core_riscv.o
LOAD /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a
LOAD /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a
LOAD /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libg_nano.a
LOAD /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libc_nano.a
LOAD /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a
START GROUP
LOAD /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a
LOAD /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libc_nano.a
LOAD /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libnosys.a
END GROUP
START GROUP
LOAD /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a
LOAD /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libc_nano.a
LOAD /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libnosys.a
END GROUP
                0x0000000000000100                __stack_size = 0x100
                [!provide]                        PROVIDE (_stack_size = __stack_size)

.init           0x0000000000000000       0xa0
                0x0000000000000000                _sinit = .
                0x0000000000000000                . = ALIGN (0x4)
 *(SORT_NONE(.init))
 .init          0x0000000000000000       0x9e ./Startup/startup_ch32v00x.o
                0x0000000000000000                _start
                0x00000000000000a0                . = ALIGN (0x4)
 *fill*         0x000000000000009e        0x2 
                0x00000000000000a0                _einit = .

.highcodelalign
                0x00000000000000a0        0x0
                0x00000000000000a0                . = ALIGN (0x4)
                0x00000000000000a0                PROVIDE (_highcode_lma = .)

.highcode       0x0000000020000000        0x0 load address 0x00000000000000a0
                0x0000000020000000                . = ALIGN (0x4)
                0x0000000020000000                PROVIDE (_highcode_vma_start = .)
 *(.highcode)
 *(.highcode.*)
                0x0000000020000000                . = ALIGN (0x4)
                0x0000000020000000                PROVIDE (_highcode_vma_end = .)

.text           0x00000000000000a0      0xcec
                0x00000000000000a0                . = ALIGN (0x4)
 *(.text)
 .text          0x00000000000000a0       0x14 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(save-restore.o)
                0x00000000000000a0                __riscv_save_1
                0x00000000000000a0                __riscv_save_2
                0x00000000000000a0                __riscv_save_0
                0x00000000000000aa                __riscv_restore_2
                0x00000000000000aa                __riscv_restore_0
                0x00000000000000aa                __riscv_restore_1
 .text          0x00000000000000b4       0x7e /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/rv32ecxw/ilp32e/libgcc.a(div.o)
                0x00000000000000b4                __divsi3
                0x00000000000000bc                __udivsi3
                0x00000000000000e8                __umodsi3
                0x000000000000010c                __modsi3
 *(.text.*)
 .text.NMI_Handler
                0x0000000000000132        0x2 ./User/ch32v00x_it.o
                0x0000000000000132                NMI_Handler
 .text.HardFault_Handler
                0x0000000000000134       0x10 ./User/ch32v00x_it.o
                0x0000000000000134                HardFault_Handler
 .text.USARTx_CFG
                0x0000000000000144       0x76 ./User/main.o
                0x0000000000000144                USARTx_CFG
 .text.startup.main
                0x00000000000001ba       0x8c ./User/main.o
                0x00000000000001ba                main
 .text.SystemInit
                0x0000000000000246      0x132 ./User/system_ch32v00x.o
                0x0000000000000246                SystemInit
 .text.SystemCoreClockUpdate
                0x0000000000000378       0x68 ./User/system_ch32v00x.o
                0x0000000000000378                SystemCoreClockUpdate
 .text.vector_handler
                0x00000000000003e0        0x2 ./Startup/startup_ch32v00x.o
                0x00000000000003e0                TIM1_CC_IRQHandler
                0x00000000000003e0                SysTick_Handler
                0x00000000000003e0                PVD_IRQHandler
                0x00000000000003e0                SPI1_IRQHandler
                0x00000000000003e0                AWU_IRQHandler
                0x00000000000003e0                EXTI7_0_IRQHandler
                0x00000000000003e0                DMA1_Channel4_IRQHandler
                0x00000000000003e0                ADC1_IRQHandler
                0x00000000000003e0                DMA1_Channel7_IRQHandler
                0x00000000000003e0                I2C1_EV_IRQHandler
                0x00000000000003e0                DMA1_Channel6_IRQHandler
                0x00000000000003e0                RCC_IRQHandler
                0x00000000000003e0                TIM1_TRG_COM_IRQHandler
                0x00000000000003e0                DMA1_Channel1_IRQHandler
                0x00000000000003e0                DMA1_Channel5_IRQHandler
                0x00000000000003e0                DMA1_Channel3_IRQHandler
                0x00000000000003e0                TIM1_UP_IRQHandler
                0x00000000000003e0                WWDG_IRQHandler
                0x00000000000003e0                TIM2_IRQHandler
                0x00000000000003e0                SW_Handler
                0x00000000000003e0                TIM1_BRK_IRQHandler
                0x00000000000003e0                DMA1_Channel2_IRQHandler
                0x00000000000003e0                FLASH_IRQHandler
                0x00000000000003e0                USART1_IRQHandler
                0x00000000000003e0                I2C1_ER_IRQHandler
 .text.handle_reset
                0x00000000000003e2       0xb0 ./Startup/startup_ch32v00x.o
                0x00000000000003e2                handle_reset
 .text.DBGMCU_GetCHIPID
                0x0000000000000492        0xa ./Peripheral/src/ch32v00x_dbgmcu.o
                0x0000000000000492                DBGMCU_GetCHIPID
 .text.GPIO_Init
                0x000000000000049c       0x7c ./Peripheral/src/ch32v00x_gpio.o
                0x000000000000049c                GPIO_Init
 .text.NVIC_PriorityGroupConfig
                0x0000000000000518        0xa ./Peripheral/src/ch32v00x_misc.o
                0x0000000000000518                NVIC_PriorityGroupConfig
 .text.RCC_AdjustHSICalibrationValue
                0x0000000000000522       0x12 ./Peripheral/src/ch32v00x_rcc.o
                0x0000000000000522                RCC_AdjustHSICalibrationValue
 .text.RCC_GetClocksFreq
                0x0000000000000534       0x9c ./Peripheral/src/ch32v00x_rcc.o
                0x0000000000000534                RCC_GetClocksFreq
 .text.RCC_APB2PeriphClockCmd
                0x00000000000005d0       0x1e ./Peripheral/src/ch32v00x_rcc.o
                0x00000000000005d0                RCC_APB2PeriphClockCmd
 .text.USART_Init
                0x00000000000005ee       0xc0 ./Peripheral/src/ch32v00x_usart.o
                0x00000000000005ee                USART_Init
 .text.USART_Cmd
                0x00000000000006ae       0x16 ./Peripheral/src/ch32v00x_usart.o
                0x00000000000006ae                USART_Cmd
 .text.USART_SendData
                0x00000000000006c4        0x8 ./Peripheral/src/ch32v00x_usart.o
                0x00000000000006c4                USART_SendData
 .text.USART_ReceiveData
                0x00000000000006cc        0x8 ./Peripheral/src/ch32v00x_usart.o
                0x00000000000006cc                USART_ReceiveData
 .text.USART_GetFlagStatus
                0x00000000000006d4        0xa ./Peripheral/src/ch32v00x_usart.o
                0x00000000000006d4                USART_GetFlagStatus
 .text.Delay_Init
                0x00000000000006de       0x30 ./Debug/debug.o
                0x00000000000006de                Delay_Init
 .text.USART_Printf_Init
                0x000000000000070e       0x52 ./Debug/debug.o
                0x000000000000070e                USART_Printf_Init
 .text._write   0x0000000000000760       0x4c ./Debug/debug.o
                0x0000000000000760                _write
 .text.printchar
                0x00000000000007ac       0x48 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
                0x00000000000007ac                printchar
 .text.prints   0x00000000000007f4       0xe6 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
                0x00000000000007f4                prints
 .text.printInt
                0x00000000000008da      0x10e /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
                0x00000000000008da                printInt
 .text.printLongLongInt
                0x00000000000009e8        0x4 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
                0x00000000000009e8                printLongLongInt
 .text.printDouble
                0x00000000000009ec        0x4 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
                0x00000000000009ec                printDouble
 .text.print    0x00000000000009f0      0x352 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
                0x00000000000009f0                print
 .text.printf   0x0000000000000d42       0x22 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
                0x0000000000000d42                printf
 *(.rodata)
 *(.rodata*)
 *fill*         0x0000000000000d64        0x0 
 .rodata.main.str1.4
                0x0000000000000d64       0x1e ./User/main.o
 *fill*         0x0000000000000d82        0x2 
 .rodata.print.str1.4
                0x0000000000000d84        0x8 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)
                                          0x7 (size before relaxing)
 *(.gnu.linkonce.t.*)
                0x0000000000000d8c                . = ALIGN (0x4)

.rela.dyn       0x0000000000000d8c        0x0
 .rela.text.startup.main
                0x0000000000000d8c        0x0 ./User/ch32v00x_it.o
 .rela.init     0x0000000000000d8c        0x0 ./User/ch32v00x_it.o
 .rela.text.handle_reset
                0x0000000000000d8c        0x0 ./User/ch32v00x_it.o
 .rela.text.prints
                0x0000000000000d8c        0x0 ./User/ch32v00x_it.o
 .rela.text.printInt
                0x0000000000000d8c        0x0 ./User/ch32v00x_it.o
 .rela.text.print
                0x0000000000000d8c        0x0 ./User/ch32v00x_it.o
 .rela.text.printf
                0x0000000000000d8c        0x0 ./User/ch32v00x_it.o

.fini           0x0000000000000d8c        0x0
 *(SORT_NONE(.fini))
                0x0000000000000d8c                . = ALIGN (0x4)
                [!provide]                        PROVIDE (_etext = .)
                [!provide]                        PROVIDE (_eitcm = .)

.preinit_array  0x0000000000000d8c        0x0
                [!provide]                        PROVIDE (__preinit_array_start = .)
 *(.preinit_array)
                [!provide]                        PROVIDE (__preinit_array_end = .)

.init_array     0x0000000000000d8c        0x0
                [!provide]                        PROVIDE (__init_array_start = .)
 *(SORT_BY_INIT_PRIORITY(.init_array.*) SORT_BY_INIT_PRIORITY(.ctors.*))
 *(.init_array EXCLUDE_FILE(*crtend?.o *crtend.o *crtbegin?.o *crtbegin.o) .ctors)
                [!provide]                        PROVIDE (__init_array_end = .)

.fini_array     0x0000000000000d8c        0x0
                [!provide]                        PROVIDE (__fini_array_start = .)
 *(SORT_BY_INIT_PRIORITY(.fini_array.*) SORT_BY_INIT_PRIORITY(.dtors.*))
 *(.fini_array EXCLUDE_FILE(*crtend?.o *crtend.o *crtbegin?.o *crtbegin.o) .dtors)
                [!provide]                        PROVIDE (__fini_array_end = .)

.ctors
 *crtbegin.o(.ctors)
 *crtbegin?.o(.ctors)
 *(EXCLUDE_FILE(*crtend?.o *crtend.o) .ctors)
 *(SORT_BY_NAME(.ctors.*))
 *(.ctors)

.dtors
 *crtbegin.o(.dtors)
 *crtbegin?.o(.dtors)
 *(EXCLUDE_FILE(*crtend?.o *crtend.o) .dtors)
 *(SORT_BY_NAME(.dtors.*))
 *(.dtors)

.dalign         0x0000000020000000        0x0 load address 0x0000000000000d8c
                0x0000000020000000                . = ALIGN (0x4)
                0x0000000020000000                PROVIDE (_data_vma = .)

.dlalign        0x0000000000000d8c        0x0
                0x0000000000000d8c                . = ALIGN (0x4)
                0x0000000000000d8c                PROVIDE (_data_lma = .)

.data           0x0000000020000000       0x38 load address 0x0000000000000d8c
                0x0000000020000000                . = ALIGN (0x4)
 *(.gnu.linkonce.r.*)
 *(.data .data.*)
 .data.AHBPrescTable
                0x0000000020000000       0x10 ./User/system_ch32v00x.o
                0x0000000020000000                AHBPrescTable
 .data.SystemCoreClock
                0x0000000020000010        0x4 ./User/system_ch32v00x.o
                0x0000000020000010                SystemCoreClock
 .data.ADCPrescTable
                0x0000000020000014       0x14 ./Peripheral/src/ch32v00x_rcc.o
 .data.APBAHBPrescTable
                0x0000000020000028       0x10 ./Peripheral/src/ch32v00x_rcc.o
 *(.gnu.linkonce.d.*)
                0x0000000020000038                . = ALIGN (0x8)
                0x0000000020000838                PROVIDE (__global_pointer$ = (. + 0x800))
 *(.sdata .sdata.*)
 *(.sdata2*)
 *(.gnu.linkonce.s.*)
                0x0000000020000038                . = ALIGN (0x8)
 *(.srodata.cst16)
 *(.srodata.cst8)
 *(.srodata.cst4)
 *(.srodata.cst2)
 *(.srodata .srodata.*)
                0x0000000020000038                . = ALIGN (0x4)
                0x0000000020000038                PROVIDE (_edata = .)

.bss            0x0000000020000038        0xc load address 0x0000000000000dc4
                0x0000000020000038                . = ALIGN (0x4)
                0x0000000020000038                PROVIDE (_sbss = .)
 *(.sbss*)
 *(.gnu.linkonce.sb.*)
 *(.bss*)
 .bss.val       0x0000000020000038        0x1 ./User/main.o
                0x0000000020000038                val
 *fill*         0x0000000020000039        0x3 
 .bss.NVIC_Priority_Group
                0x000000002000003c        0x4 ./Peripheral/src/ch32v00x_misc.o
                0x000000002000003c                NVIC_Priority_Group
 .bss.p_ms      0x0000000020000040        0x2 ./Debug/debug.o
 .bss.p_us      0x0000000020000042        0x1 ./Debug/debug.o
 *(.gnu.linkonce.b.*)
 *(COMMON*)
                0x0000000020000044                . = ALIGN (0x4)
 *fill*         0x0000000020000043        0x1 
                0x0000000020000044                PROVIDE (_ebss = .)
                0x0000000020000044                PROVIDE (_end = _ebss)
                [!provide]                        PROVIDE (end = .)

.stack          0x0000000020000700      0x100
                0x0000000020000700                PROVIDE (_heap_end = .)
                0x0000000020000700                . = ALIGN (0x4)
                [!provide]                        PROVIDE (_susrstack = .)
                0x0000000020000800                . = (. + __stack_size)
 *fill*         0x0000000020000700      0x100 
                0x0000000020000800                PROVIDE (_eusrstack = .)
OUTPUT(CH32V003F4U6.elf elf32-littleriscv)

.debug_info     0x0000000000000000     0x8291
 .debug_info    0x0000000000000000      0xbc0 ./User/ch32v00x_it.o
 .debug_info    0x0000000000000bc0      0xe7a ./User/main.o
 .debug_info    0x0000000000001a3a      0xc94 ./User/system_ch32v00x.o
 .debug_info    0x00000000000026ce       0x22 ./Startup/startup_ch32v00x.o
 .debug_info    0x00000000000026f0      0xa94 ./Peripheral/src/ch32v00x_dbgmcu.o
 .debug_info    0x0000000000003184     0x1034 ./Peripheral/src/ch32v00x_gpio.o
 .debug_info    0x00000000000041b8      0xdc5 ./Peripheral/src/ch32v00x_misc.o
 .debug_info    0x0000000000004f7d     0x108c ./Peripheral/src/ch32v00x_rcc.o
 .debug_info    0x0000000000006009     0x1351 ./Peripheral/src/ch32v00x_usart.o
 .debug_info    0x000000000000735a      0xf37 ./Debug/debug.o

.debug_abbrev   0x0000000000000000     0x1a69
 .debug_abbrev  0x0000000000000000      0x22c ./User/ch32v00x_it.o
 .debug_abbrev  0x000000000000022c      0x2a1 ./User/main.o
 .debug_abbrev  0x00000000000004cd      0x2ee ./User/system_ch32v00x.o
 .debug_abbrev  0x00000000000007bb       0x12 ./Startup/startup_ch32v00x.o
 .debug_abbrev  0x00000000000007cd      0x2f7 ./Peripheral/src/ch32v00x_dbgmcu.o
 .debug_abbrev  0x0000000000000ac4      0x309 ./Peripheral/src/ch32v00x_gpio.o
 .debug_abbrev  0x0000000000000dcd      0x2e1 ./Peripheral/src/ch32v00x_misc.o
 .debug_abbrev  0x00000000000010ae      0x389 ./Peripheral/src/ch32v00x_rcc.o
 .debug_abbrev  0x0000000000001437      0x323 ./Peripheral/src/ch32v00x_usart.o
 .debug_abbrev  0x000000000000175a      0x30f ./Debug/debug.o

.debug_aranges  0x0000000000000000      0x3e0
 .debug_aranges
                0x0000000000000000       0x28 ./User/ch32v00x_it.o
 .debug_aranges
                0x0000000000000028       0x28 ./User/main.o
 .debug_aranges
                0x0000000000000050       0x28 ./User/system_ch32v00x.o
 .debug_aranges
                0x0000000000000078       0x30 ./Startup/startup_ch32v00x.o
 .debug_aranges
                0x00000000000000a8       0x48 ./Peripheral/src/ch32v00x_dbgmcu.o
 .debug_aranges
                0x00000000000000f0       0x98 ./Peripheral/src/ch32v00x_gpio.o
 .debug_aranges
                0x0000000000000188       0x28 ./Peripheral/src/ch32v00x_misc.o
 .debug_aranges
                0x00000000000001b0       0xe0 ./Peripheral/src/ch32v00x_rcc.o
 .debug_aranges
                0x0000000000000290      0x100 ./Peripheral/src/ch32v00x_usart.o
 .debug_aranges
                0x0000000000000390       0x50 ./Debug/debug.o

.debug_ranges   0x0000000000000000      0x3b0
 .debug_ranges  0x0000000000000000       0x18 ./User/ch32v00x_it.o
 .debug_ranges  0x0000000000000018       0x18 ./User/main.o
 .debug_ranges  0x0000000000000030       0x50 ./User/system_ch32v00x.o
 .debug_ranges  0x0000000000000080       0x28 ./Startup/startup_ch32v00x.o
 .debug_ranges  0x00000000000000a8       0x50 ./Peripheral/src/ch32v00x_dbgmcu.o
 .debug_ranges  0x00000000000000f8       0x88 ./Peripheral/src/ch32v00x_gpio.o
 .debug_ranges  0x0000000000000180       0x30 ./Peripheral/src/ch32v00x_misc.o
 .debug_ranges  0x00000000000001b0       0xd0 ./Peripheral/src/ch32v00x_rcc.o
 .debug_ranges  0x0000000000000280       0xf0 ./Peripheral/src/ch32v00x_usart.o
 .debug_ranges  0x0000000000000370       0x40 ./Debug/debug.o

.debug_line     0x0000000000000000     0x4e90
 .debug_line    0x0000000000000000      0x348 ./User/ch32v00x_it.o
 .debug_line    0x0000000000000348      0x6b9 ./User/main.o
 .debug_line    0x0000000000000a01      0x77c ./User/system_ch32v00x.o
 .debug_line    0x000000000000117d      0x148 ./Startup/startup_ch32v00x.o
 .debug_line    0x00000000000012c5      0x423 ./Peripheral/src/ch32v00x_dbgmcu.o
 .debug_line    0x00000000000016e8      0xca3 ./Peripheral/src/ch32v00x_gpio.o
 .debug_line    0x000000000000238b      0x48a ./Peripheral/src/ch32v00x_misc.o
 .debug_line    0x0000000000002815      0xeaa ./Peripheral/src/ch32v00x_rcc.o
 .debug_line    0x00000000000036bf      0xfb4 ./Peripheral/src/ch32v00x_usart.o
 .debug_line    0x0000000000004673      0x81d ./Debug/debug.o

.debug_str      0x0000000000000000     0x160c
 .debug_str     0x0000000000000000      0x62c ./User/ch32v00x_it.o
                                        0x695 (size before relaxing)
 .debug_str     0x000000000000062c      0x2fc ./User/main.o
                                        0x8f5 (size before relaxing)
 .debug_str     0x0000000000000928      0x11d ./User/system_ch32v00x.o
                                        0x758 (size before relaxing)
 .debug_str     0x0000000000000a45       0x2a ./Startup/startup_ch32v00x.o
                                         0x64 (size before relaxing)
 .debug_str     0x0000000000000a6f       0x97 ./Peripheral/src/ch32v00x_dbgmcu.o
                                        0x656 (size before relaxing)
 .debug_str     0x0000000000000b06      0x1ed ./Peripheral/src/ch32v00x_gpio.o
                                        0x953 (size before relaxing)
 .debug_str     0x0000000000000cf3      0x28e ./Peripheral/src/ch32v00x_misc.o
                                        0x91b (size before relaxing)
 .debug_str     0x0000000000000f81      0x2c7 ./Peripheral/src/ch32v00x_rcc.o
                                        0x9c1 (size before relaxing)
 .debug_str     0x0000000000001248      0x33f ./Peripheral/src/ch32v00x_usart.o
                                        0xb10 (size before relaxing)
 .debug_str     0x0000000000001587       0x85 ./Debug/debug.o
                                        0x911 (size before relaxing)

.comment        0x0000000000000000       0x33
 .comment       0x0000000000000000       0x33 ./User/ch32v00x_it.o
                                         0x34 (size before relaxing)
 .comment       0x0000000000000033       0x34 ./User/main.o
 .comment       0x0000000000000033       0x34 ./User/system_ch32v00x.o
 .comment       0x0000000000000033       0x34 ./Peripheral/src/ch32v00x_dbgmcu.o
 .comment       0x0000000000000033       0x34 ./Peripheral/src/ch32v00x_gpio.o
 .comment       0x0000000000000033       0x34 ./Peripheral/src/ch32v00x_misc.o
 .comment       0x0000000000000033       0x34 ./Peripheral/src/ch32v00x_rcc.o
 .comment       0x0000000000000033       0x34 ./Peripheral/src/ch32v00x_usart.o
 .comment       0x0000000000000033       0x34 ./Debug/debug.o
 .comment       0x0000000000000033       0x34 /home/<USER>/Downloads/MRS-linux-x64/resources/app/resources/linux/components/WCH/Toolchain/RISC-V Embedded GCC/bin/../lib/gcc/riscv-none-embed/8.2.0/../../../../riscv-none-embed/lib/rv32ecxw/ilp32e/libprintf.a(wchprintf.o)

.debug_frame    0x0000000000000000      0x790
 .debug_frame   0x0000000000000000       0x30 ./User/ch32v00x_it.o
 .debug_frame   0x0000000000000030       0x50 ./User/main.o
 .debug_frame   0x0000000000000080       0x58 ./User/system_ch32v00x.o
 .debug_frame   0x00000000000000d8       0x70 ./Peripheral/src/ch32v00x_dbgmcu.o
 .debug_frame   0x0000000000000148      0x154 ./Peripheral/src/ch32v00x_gpio.o
 .debug_frame   0x000000000000029c       0x30 ./Peripheral/src/ch32v00x_misc.o
 .debug_frame   0x00000000000002cc      0x1dc ./Peripheral/src/ch32v00x_rcc.o
 .debug_frame   0x00000000000004a8      0x204 ./Peripheral/src/ch32v00x_usart.o
 .debug_frame   0x00000000000006ac       0xe4 ./Debug/debug.o

.debug_loc      0x0000000000000000     0x15ae
 .debug_loc     0x0000000000000000       0x55 ./User/system_ch32v00x.o
 .debug_loc     0x0000000000000055       0x7c ./Peripheral/src/ch32v00x_dbgmcu.o
 .debug_loc     0x00000000000000d1      0x5e2 ./Peripheral/src/ch32v00x_gpio.o
 .debug_loc     0x00000000000006b3       0x47 ./Peripheral/src/ch32v00x_misc.o
 .debug_loc     0x00000000000006fa      0x5e3 ./Peripheral/src/ch32v00x_rcc.o
 .debug_loc     0x0000000000000cdd      0x735 ./Peripheral/src/ch32v00x_usart.o
 .debug_loc     0x0000000000001412      0x19c ./Debug/debug.o
