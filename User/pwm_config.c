/**
 * @file pwm_config.c
 * @brief PWM Configuration Implementation for Display Brightness Control
 * <AUTHOR> for CH32V003 Project
 * @date 2025
 */

#include "pwm_config.h"
#include "debug.h"

// Global PWM control instance
PWM_Control_t pwm_control = {0};

/**
 * @brief Initialize PWM system for display brightness control
 */
void PWM_Config_Init(void)
{
    // Initialize PWM control structure
    pwm_control.current_brightness = 0;
    pwm_control.target_brightness = PWM_DEFAULT_BRIGHTNESS;
    pwm_control.fade_enabled = 0;
    pwm_control.fade_step = 10;
    
    // Configure GPIO and Timer
    PWM_GPIO_Config();
    PWM_Timer_Config();
    
    // Set default brightness
    PWM_Set_Brightness(0); // Start with display off
}

/**
 * @brief Configure GPIO for PWM output
 */
void PWM_GPIO_Config(void)
{
    GPIO_InitTypeDef GPIO_InitStructure = {0};
    
    // Enable GPIOA clock
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);
    
    // Configure PA8 as alternate function push-pull for TIM1_CH1
    GPIO_InitStructure.GPIO_Pin = PWM_GPIO_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_30MHz;
    GPIO_Init(PWM_GPIO_PORT, &GPIO_InitStructure);
}

/**
 * @brief Configure Timer for PWM generation
 */
void PWM_Timer_Config(void)
{
    TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure = {0};
    TIM_OCInitTypeDef TIM_OCInitStructure = {0};
    
    // Enable Timer1 clock
    RCC_APB2PeriphClockCmd(PWM_TIMER_RCC, ENABLE);
    
    // Calculate prescaler for desired frequency
    // PWM_FREQUENCY = SystemCoreClock / ((Prescaler + 1) * (Period + 1))
    uint16_t prescaler = (SystemCoreClock / (PWM_FREQUENCY * PWM_PERIOD)) - 1;
    
    // Configure Timer1 time base
    TIM_TimeBaseStructure.TIM_Period = PWM_PERIOD - 1;
    TIM_TimeBaseStructure.TIM_Prescaler = prescaler;
    TIM_TimeBaseStructure.TIM_ClockDivision = TIM_CKD_DIV1;
    TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;
    TIM_TimeBaseInit(PWM_TIMER, &TIM_TimeBaseStructure);
    
    // Configure PWM mode
    TIM_OCInitStructure.TIM_OCMode = TIM_OCMode_PWM1;
    TIM_OCInitStructure.TIM_OutputState = TIM_OutputState_Enable;
    TIM_OCInitStructure.TIM_Pulse = 0; // Start with 0% duty cycle
    TIM_OCInitStructure.TIM_OCPolarity = TIM_OCPolarity_High;
    TIM_OC1Init(PWM_TIMER, &TIM_OCInitStructure);
    
    // Enable preload
    TIM_OC1PreloadConfig(PWM_TIMER, TIM_OCPreload_Enable);
    TIM_ARRPreloadConfig(PWM_TIMER, ENABLE);
    
    // Enable Timer1
    TIM_Cmd(PWM_TIMER, ENABLE);
    
    // Enable main output (required for TIM1)
    TIM_CtrlPWMOutputs(PWM_TIMER, ENABLE);
}

/**
 * @brief Set PWM brightness value
 * @param brightness Brightness value (0-PWM_PERIOD)
 */
void PWM_Set_Brightness(uint16_t brightness)
{
    // Clamp brightness to valid range
    if (brightness > PWM_MAX_BRIGHTNESS) {
        brightness = PWM_MAX_BRIGHTNESS;
    }
    
    // Update PWM duty cycle
    TIM_SetCompare1(PWM_TIMER, brightness);
    pwm_control.current_brightness = brightness;
}

/**
 * @brief Set PWM brightness as percentage
 * @param percent Brightness percentage (0-100)
 */
void PWM_Set_Brightness_Percent(uint8_t percent)
{
    if (percent > 100) percent = 100;
    
    uint16_t brightness = (uint16_t)((uint32_t)percent * PWM_MAX_BRIGHTNESS / 100);
    PWM_Set_Brightness(brightness);
}

/**
 * @brief Start fading to target brightness
 * @param target_brightness Target brightness value
 */
void PWM_Fade_To_Brightness(uint16_t target_brightness)
{
    if (target_brightness > PWM_MAX_BRIGHTNESS) {
        target_brightness = PWM_MAX_BRIGHTNESS;
    }
    
    pwm_control.target_brightness = target_brightness;
    pwm_control.fade_enabled = 1;
}

/**
 * @brief Update fade effect (call periodically)
 */
void PWM_Update_Fade(void)
{
    if (!pwm_control.fade_enabled) return;
    
    if (pwm_control.current_brightness < pwm_control.target_brightness) {
        // Fade up
        uint16_t new_brightness = pwm_control.current_brightness + pwm_control.fade_step;
        if (new_brightness >= pwm_control.target_brightness) {
            new_brightness = pwm_control.target_brightness;
            pwm_control.fade_enabled = 0;
        }
        PWM_Set_Brightness(new_brightness);
    } else if (pwm_control.current_brightness > pwm_control.target_brightness) {
        // Fade down
        uint16_t new_brightness;
        if (pwm_control.current_brightness >= pwm_control.fade_step) {
            new_brightness = pwm_control.current_brightness - pwm_control.fade_step;
        } else {
            new_brightness = 0;
        }
        
        if (new_brightness <= pwm_control.target_brightness) {
            new_brightness = pwm_control.target_brightness;
            pwm_control.fade_enabled = 0;
        }
        PWM_Set_Brightness(new_brightness);
    } else {
        // Target reached
        pwm_control.fade_enabled = 0;
    }
}

/**
 * @brief Turn on display with default brightness
 */
void PWM_Turn_On(void)
{
    PWM_Fade_To_Brightness(PWM_DEFAULT_BRIGHTNESS);
}

/**
 * @brief Turn off display
 */
void PWM_Turn_Off(void)
{
    PWM_Fade_To_Brightness(BRIGHTNESS_OFF);
}

/**
 * @brief Get current brightness value
 * @return Current brightness (0-PWM_PERIOD)
 */
uint16_t PWM_Get_Brightness(void)
{
    return pwm_control.current_brightness;
}
