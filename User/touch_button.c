/**
 * @file touch_button.c
 * @brief TP223 Touch Button Implementation for CH32V003
 * <AUTHOR> for CH32V003 Project
 * @date 2025
 */

#include "touch_button.h"
#include "debug.h"

// Global touch button instance
TouchButton_t touch_button = {0};

// Global variables for timing (accessible from interrupt handler)
volatile uint32_t system_tick_ms = 0;

/**
 * @brief Initialize touch button system
 */
void Touch_Button_Init(void)
{
    // Initialize touch button structure
    touch_button.state = TOUCH_STATE_IDLE;
    touch_button.last_event = TOUCH_EVENT_NONE;
    touch_button.press_start_time = 0;
    touch_button.last_activity_time = 0;
    touch_button.is_pressed = 0;
    touch_button.screen_on = 0;
    touch_button.screen_toggle_mode = 0;

    // Configure GPIO and EXTI
    Touch_Button_GPIO_Config();
    Touch_Button_EXTI_Config();

    // Initialize Timer2 for system timing (1ms tick)
    Touch_Button_Timer_Init();
}

/**
 * @brief Configure GPIO for touch button
 */
void Touch_Button_GPIO_Config(void)
{
    GPIO_InitTypeDef GPIO_InitStructure = {0};
    
    // Enable GPIOD clock
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOD, ENABLE);
    
    // Configure PD7 as input with pull-up
    GPIO_InitStructure.GPIO_Pin = TOUCH_GPIO_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;
    GPIO_Init(TOUCH_GPIO_PORT, &GPIO_InitStructure);
}

/**
 * @brief Configure EXTI for touch button interrupt
 */
void Touch_Button_EXTI_Config(void)
{
    EXTI_InitTypeDef EXTI_InitStructure = {0};
    NVIC_InitTypeDef NVIC_InitStructure = {0};
    
    // Enable AFIO clock
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_AFIO, ENABLE);
    
    // Connect EXTI line to GPIO pin
    GPIO_EXTILineConfig(GPIO_PortSourceGPIOD, TOUCH_GPIO_SOURCE);
    
    // Configure EXTI line
    EXTI_InitStructure.EXTI_Line = TOUCH_EXTI_LINE;
    EXTI_InitStructure.EXTI_Mode = EXTI_Mode_Interrupt;
    EXTI_InitStructure.EXTI_Trigger = EXTI_Trigger_Rising_Falling;
    EXTI_InitStructure.EXTI_LineCmd = ENABLE;
    EXTI_Init(&EXTI_InitStructure);
    
    // Configure NVIC
    NVIC_InitStructure.NVIC_IRQChannel = TOUCH_EXTI_IRQ;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);
}

/**
 * @brief Update touch button state machine (call in main loop)
 */
void Touch_Button_Update(void)
{
    uint32_t current_time = Touch_Button_Get_Time_Ms();
    uint32_t press_duration = current_time - touch_button.press_start_time;
    uint32_t idle_time = current_time - touch_button.last_activity_time;
    
    switch (touch_button.state) {
        case TOUCH_STATE_IDLE:
            // Check for screen timeout
            if (touch_button.screen_on && !touch_button.screen_toggle_mode) {
                if (idle_time >= SCREEN_TIMEOUT_TIME) {
                    touch_button.screen_on = 0;
                    touch_button.last_event = TOUCH_EVENT_TIMEOUT;
                }
            }
            break;
            
        case TOUCH_STATE_PRESSED:
            // Check for long press
            if (press_duration >= TOUCH_HOLD_TIME) {
                touch_button.state = TOUCH_STATE_HELD;
                touch_button.last_event = TOUCH_EVENT_LONG_PRESS;
                touch_button.screen_toggle_mode = !touch_button.screen_toggle_mode;
                touch_button.screen_on = 1;
                touch_button.last_activity_time = current_time;
            }
            break;
            
        case TOUCH_STATE_HELD:
            // Wait for release
            break;
            
        case TOUCH_STATE_RELEASED:
            // Process the release event
            if (press_duration < TOUCH_HOLD_TIME) {
                // Short press - turn on screen
                touch_button.last_event = TOUCH_EVENT_SHORT_PRESS;
                touch_button.screen_on = 1;
                touch_button.last_activity_time = current_time;
            }
            touch_button.state = TOUCH_STATE_IDLE;
            break;
    }
}

/**
 * @brief Get the last touch event and clear it
 * @return Last touch event
 */
TouchEvent_t Touch_Button_Get_Event(void)
{
    TouchEvent_t event = touch_button.last_event;
    touch_button.last_event = TOUCH_EVENT_NONE;
    return event;
}

/**
 * @brief Check if screen should be on
 * @return 1 if screen should be on, 0 if off
 */
uint8_t Touch_Button_Is_Screen_On(void)
{
    return touch_button.screen_on;
}

/**
 * @brief Reset the screen timeout timer
 */
void Touch_Button_Reset_Timeout(void)
{
    touch_button.last_activity_time = Touch_Button_Get_Time_Ms();
}

/**
 * @brief Get current system time in milliseconds
 * @return Current time in ms
 */
uint32_t Touch_Button_Get_Time_Ms(void)
{
    return system_tick_ms;
}

/**
 * @brief Touch button interrupt handler
 */
void Touch_Button_IRQ_Handler(void)
{
    if (EXTI_GetITStatus(TOUCH_EXTI_LINE) != RESET) {
        uint32_t current_time = Touch_Button_Get_Time_Ms();
        uint8_t pin_state = GPIO_ReadInputDataBit(TOUCH_GPIO_PORT, TOUCH_GPIO_PIN);
        
        if (pin_state) {
            // Rising edge - button pressed
            if (touch_button.state == TOUCH_STATE_IDLE) {
                touch_button.state = TOUCH_STATE_PRESSED;
                touch_button.press_start_time = current_time;
                touch_button.is_pressed = 1;
            }
        } else {
            // Falling edge - button released
            if (touch_button.state == TOUCH_STATE_PRESSED || 
                touch_button.state == TOUCH_STATE_HELD) {
                touch_button.state = TOUCH_STATE_RELEASED;
                touch_button.is_pressed = 0;
            }
        }
        
        EXTI_ClearITPendingBit(TOUCH_EXTI_LINE);
    }
}


