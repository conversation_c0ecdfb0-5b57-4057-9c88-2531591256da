/********************************** (C) COPYRIGHT *******************************
 * File Name          : main.c
 * Author             : WCH / Modified for ADC Monitor Project
 * Version            : V2.0.0
 * Date               : 2025/08/25
 * Description        : CH32V003 ADC Monitor with ST7735 Display and Touch Control
 *********************************************************************************
 * Copyright (c) 2021 Nanjing Qinheng Microelectronics Co., Ltd.
 * Attention: This software (modified or not) and binary are used for
 * microcontroller manufactured by Nanjing Qinheng Microelectronics.
 *******************************************************************************/

/*
 * @Note
 * CH32V003 ADC Monitor Project:
 * - Measures 4 ADC channels (PA0-PA3)
 * - Displays data on ST7735 80x160 TFT screen
 * - Touch button (TP223) control:
 *   * Touch: Turn on screen (auto-off after 2s)
 *   * Hold 1s: Toggle always-on mode
 *
 * Hardware Connections:
 * ST7735 Display:
 *   PC2 - RESET, PC3 - DC, PC4 - CS, PC5 - SCLK, PC6 - MOSI
 *   PA8 - LEDA (PWM brightness control)
 * Touch Button:
 *   PD7 - TP223 output
 * ADC Inputs:
 *   PA0 - ADC Channel 1, PA1 - ADC Channel 2
 *   PA2 - ADC Channel 3, PA3 - ADC Channel 4
 */

#include "debug.h"
#include "st7735.h"
#include "adc_config.h"
#include "touch_button.h"
#include "pwm_config.h"
#include "display_control.h"
#include "adc_display.h"

/* Global Variables */
ADC_Data_t adc_data = {0};
uint32_t last_adc_read_time = 0;
uint8_t system_initialized = 0;

/*********************************************************************
 * @fn      System_Init
 *
 * @brief   Initialize all system components
 *
 * @return  none
 */
void System_Init(void)
{
    // Initialize ADC system
    ADC_Config_Init();
    printf("ADC initialized\r\n");

    // Initialize PWM for display brightness
    // PWM_Config_Init();
    printf("PWM initialized\r\n");

    // Initialize touch button
    Touch_Button_Init();
    printf("Touch button initialized\r\n");

    // Initialize display control
    Display_Control_Init();
    printf("Display control initialized\r\n");

    // Initialize ADC display interface
    ADC_Display_Init();
    printf("ADC display initialized\r\n");

    // System is ready
    system_initialized = 1;
    printf("System initialization complete\r\n");
}

/*********************************************************************
 * @fn      main
 *
 * @brief   Main program - ADC Monitor with Touch Control
 *
 * @return  none
 */
int main(void)
{
    // Basic system initialization
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_1);
    SystemCoreClockUpdate();
    Delay_Init();

#if (SDI_PRINT == SDI_PR_OPEN)
    SDI_Printf_Enable();
#else
    USART_Printf_Init(115200);
#endif

    printf("\r\n=== CH32V003 ADC Monitor ===\r\n");
    printf("SystemClk: %d Hz\r\n", SystemCoreClock);
    printf("ChipID: %08x\r\n", DBGMCU_GetCHIPID());

    // Initialize all system components
    System_Init();

    // Main application loop
    while(1)
    {
        // Update touch button state machine
        Touch_Button_Update();

        // Handle touch button events
        TouchEvent_t touch_event = Touch_Button_Get_Event();
        switch (touch_event) {
            case TOUCH_EVENT_SHORT_PRESS:
                printf("Touch: Short press - turning on display\r\n");
                Display_Control_Turn_On();
                break;

            case TOUCH_EVENT_LONG_PRESS:
                printf("Touch: Long press - toggling display mode\r\n");
                Display_Control_Toggle();
                break;

            case TOUCH_EVENT_TIMEOUT:
                printf("Touch: Timeout - turning off display\r\n");
                Display_Control_Turn_Off();
                break;

            default:
                break;
        }

        // Update display control
        Display_Control_Update();

        // Read ADC data periodically (every 100ms)
        uint32_t current_time = Touch_Button_Get_Time_Ms();
        if (current_time - last_adc_read_time >= 100) {
            ADC_Read_All_Channels(&adc_data);
            last_adc_read_time = current_time;

            // Update display if screen is on
            if (Display_Control_Is_On()) {
                ADC_Display_Update(&adc_data, ADC_DISPLAY_MODE_VALUES_ONLY);
            }
        }

        // Small delay to prevent excessive CPU usage
        Delay_Ms(1);
    }
}
