/**
 * @file adc_config.c
 * @brief ADC Configuration Implementation for CH32V003
 * <AUTHOR> for CH32V003 Project
 * @date 2025
 */

#include "adc_config.h"
#include "debug.h"

/**
 * @brief Initialize ADC1 peripheral
 */
void ADC_Config_Init(void)
{
    ADC_InitTypeDef ADC_InitStructure = {0};
    
    // Enable ADC1 and GPIOA clocks
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_ADC1 | RCC_APB2Periph_GPIOA, ENABLE);
    
    // Configure GPIO pins for ADC
    ADC_GPIO_Config();
    
    // Reset ADC1 calibration
    ADC_ResetCalibration(ADC1);
    while(ADC_GetResetCalibrationStatus(ADC1));
    
    // Start ADC1 calibration
    ADC_StartCalibration(ADC1);
    while(ADC_GetCalibrationStatus(ADC1));
    
    // Configure ADC1
    ADC_InitStructure.ADC_Mode = ADC_Mode_Independent;
    ADC_InitStructure.ADC_ScanConvMode = DISABLE;
    ADC_InitStructure.ADC_ContinuousConvMode = DISABLE;
    ADC_InitStructure.ADC_ExternalTrigConv = ADC_ExternalTrigConv_None;
    ADC_InitStructure.ADC_DataAlign = ADC_DataAlign_Right;
    ADC_InitStructure.ADC_NbrOfChannel = 1;
    
    ADC_Init(ADC1, &ADC_InitStructure);
    
    // Enable ADC1
    ADC_Cmd(ADC1, ENABLE);
    
    // Wait for ADC to be ready
    Delay_Ms(10);
}

/**
 * @brief Configure GPIO pins for ADC inputs
 */
void ADC_GPIO_Config(void)
{
    GPIO_InitTypeDef GPIO_InitStructure = {0};
    
    // Configure PA0, PA1, PA2, PA3 as analog inputs
    GPIO_InitStructure.GPIO_Pin = ADC_PIN_1 | ADC_PIN_2 | ADC_PIN_3 | ADC_PIN_4;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AIN;
    GPIO_Init(ADC_GPIO_PORT, &GPIO_InitStructure);
}

/**
 * @brief Read ADC value from specified channel
 * @param channel ADC channel number (0-3)
 * @return Raw ADC value (0-4095)
 */
uint16_t ADC_Read_Channel(uint8_t channel)
{
    uint16_t adc_value = 0;
    
    // Configure the channel
    ADC_RegularChannelConfig(ADC1, channel, 1, ADC_SampleTime_241Cycles);
    
    // Start conversion
    ADC_SoftwareStartConvCmd(ADC1, ENABLE);
    
    // Wait for conversion to complete
    while(!ADC_GetFlagStatus(ADC1, ADC_FLAG_EOC));
    
    // Read the converted value
    adc_value = ADC_GetConversionValue(ADC1);
    
    // Clear the EOC flag
    ADC_ClearFlag(ADC1, ADC_FLAG_EOC);
    
    return adc_value;
}

/**
 * @brief Read all ADC channels and update data structure
 * @param adc_data Pointer to ADC data structure
 */
void ADC_Read_All_Channels(ADC_Data_t* adc_data)
{
    if (adc_data == NULL) return;
    
    // Read all 4 channels
    adc_data->raw_values[0] = ADC_Read_Channel(ADC_CHANNEL_1);
    adc_data->raw_values[1] = ADC_Read_Channel(ADC_CHANNEL_2);
    adc_data->raw_values[2] = ADC_Read_Channel(ADC_CHANNEL_3);
    adc_data->raw_values[3] = ADC_Read_Channel(ADC_CHANNEL_4);
    
    // Convert to voltage values
    for (int i = 0; i < ADC_NUM_CHANNELS; i++) {
        adc_data->voltage_mv[i] = ADC_Convert_To_Voltage(adc_data->raw_values[i]);
        adc_data->channel_ready[i] = 1;
    }
}

/**
 * @brief Convert raw ADC value to voltage in millivolts
 * @param raw_value Raw ADC value (0-4095)
 * @return Voltage in millivolts
 */
uint16_t ADC_Convert_To_Voltage(uint16_t raw_value)
{
    return (uint16_t)((uint32_t)raw_value * ADC_VREF_MV / ADC_RESOLUTION);
}

/**
 * @brief Start ADC conversion for specified channel (non-blocking)
 * @param channel ADC channel number (0-3)
 */
void ADC_Start_Conversion(uint8_t channel)
{
    // Configure the channel
    ADC_RegularChannelConfig(ADC1, channel, 1, ADC_SampleTime_241Cycles);
    
    // Start conversion
    ADC_SoftwareStartConvCmd(ADC1, ENABLE);
}

/**
 * @brief Check if ADC conversion is complete
 * @return 1 if conversion is done, 0 if still converting
 */
uint8_t ADC_Is_Conversion_Done(void)
{
    return ADC_GetFlagStatus(ADC1, ADC_FLAG_EOC) ? 1 : 0;
}
