/**
 * @file touch_button.h
 * @brief TP223 Touch Button Interface for CH32V003
 * <AUTHOR> for CH32V003 Project
 * @date 2025
 */

#ifndef __TOUCH_BUTTON_H__
#define __TOUCH_BUTTON_H__

#include "ch32v00x_conf.h"

// Touch Button Configuration
#define TOUCH_GPIO_PORT         GPIOD
#define TOUCH_GPIO_PIN          GPIO_Pin_7      // PD7 for TP223 input
#define TOUCH_GPIO_SOURCE       GPIO_PinSource7
#define TOUCH_EXTI_LINE         EXTI_Line7
#define TOUCH_EXTI_IRQ          EXTI7_0_IRQn

// Touch Button Timing Constants (in milliseconds)
#define TOUCH_DEBOUNCE_TIME     50      // Debounce time in ms
#define TOUCH_HOLD_TIME         1000    // Hold time for toggle in ms
#define SCREEN_TIMEOUT_TIME     2000    // Screen auto-off time in ms

// Touch Button States
typedef enum {
    TOUCH_STATE_IDLE = 0,
    TOUCH_STATE_PRESSED,
    TOUCH_STATE_HELD,
    TOUCH_STATE_RELEASED
} TouchState_t;

// Touch Button Events
typedef enum {
    TOUCH_EVENT_NONE = 0,
    TOUCH_EVENT_SHORT_PRESS,    // Quick touch - turn on screen
    TOUCH_EVENT_LONG_PRESS,     // Hold for 1s - toggle screen mode
    TOUCH_EVENT_TIMEOUT         // Screen timeout after 2s
} TouchEvent_t;

// Touch Button Data Structure
typedef struct {
    TouchState_t state;
    TouchEvent_t last_event;
    uint32_t press_start_time;
    uint32_t last_activity_time;
    uint8_t is_pressed;
    uint8_t screen_on;
    uint8_t screen_toggle_mode;  // 0 = auto-off, 1 = always on
} TouchButton_t;

// Global touch button instance
extern TouchButton_t touch_button;

// Function Prototypes
void Touch_Button_Init(void);
void Touch_Button_GPIO_Config(void);
void Touch_Button_Timer_Init(void);
void Touch_Button_EXTI_Config(void);
void Touch_Button_Update(void);
TouchEvent_t Touch_Button_Get_Event(void);
uint8_t Touch_Button_Is_Screen_On(void);
void Touch_Button_Reset_Timeout(void);
uint32_t Touch_Button_Get_Time_Ms(void);

// Interrupt handler (to be called from EXTI interrupt)
void Touch_Button_IRQ_Handler(void);

#endif // __TOUCH_BUTTON_H__
