/**
 * @file adc_config.h
 * @brief ADC Configuration for CH32V003 - 4 Channel ADC Reading
 * <AUTHOR> for CH32V003 Project
 * @date 2025
 */

#ifndef __ADC_CONFIG_H__
#define __ADC_CONFIG_H__

#include "ch32v00x_conf.h"

// ADC Configuration Constants
#define ADC_NUM_CHANNELS    4
#define ADC_RESOLUTION      4096    // 12-bit ADC
#define ADC_VREF_MV         3300    // 3.3V reference voltage in mV

// ADC Channel Definitions (PA0-PA3 correspond to ADC channels 0-3)
#define ADC_CHANNEL_1       ADC_Channel_0   // PA0
#define ADC_CHANNEL_2       ADC_Channel_1   // PA1  
#define ADC_CHANNEL_3       ADC_Channel_2   // PA2
#define ADC_CHANNEL_4       ADC_Channel_3   // PA3

// GPIO Pin Definitions for ADC inputs
#define ADC_GPIO_PORT       GPIOA
#define ADC_PIN_1           GPIO_Pin_0      // ADC Channel 1
#define ADC_PIN_2           GPIO_Pin_1      // ADC Channel 2
#define ADC_PIN_3           GPIO_Pin_2      // ADC Channel 3
#define ADC_PIN_4           GPIO_Pin_3      // ADC Channel 4

// ADC Data Structure
typedef struct {
    uint16_t raw_values[ADC_NUM_CHANNELS];      // Raw ADC values (0-4095)
    uint16_t voltage_mv[ADC_NUM_CHANNELS];      // Converted voltage in mV
    uint8_t channel_ready[ADC_NUM_CHANNELS];    // Channel data ready flags
} ADC_Data_t;

// Function Prototypes
void ADC_Config_Init(void);
void ADC_GPIO_Config(void);
uint16_t ADC_Read_Channel(uint8_t channel);
void ADC_Read_All_Channels(ADC_Data_t* adc_data);
uint16_t ADC_Convert_To_Voltage(uint16_t raw_value);
void ADC_Start_Conversion(uint8_t channel);
uint8_t ADC_Is_Conversion_Done(void);

#endif // __ADC_CONFIG_H__
