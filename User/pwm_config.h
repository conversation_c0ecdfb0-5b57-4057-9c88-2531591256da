/**
 * @file pwm_config.h
 * @brief PWM Configuration for Display Brightness Control
 * <AUTHOR> for CH32V003 Project
 * @date 2025
 */

#ifndef __PWM_CONFIG_H__
#define __PWM_CONFIG_H__

#include "ch32v00x_conf.h"

// PWM Configuration for Display Backlight (LEDA pin)
// Using Timer1 Channel 1 on PA8 (if available) or Timer2
#define PWM_TIMER               TIM1
#define PWM_TIMER_RCC           RCC_APB2Periph_TIM1
#define PWM_GPIO_PORT           GPIOA
#define PWM_GPIO_PIN            GPIO_Pin_8
#define PWM_GPIO_SOURCE         GPIO_PinSource8
#define PWM_CHANNEL             TIM_Channel_1

// PWM Parameters
#define PWM_FREQUENCY           1000    // 1kHz PWM frequency
#define PWM_PERIOD              1000    // PWM period (0-1000 for 0.1% resolution)
#define PWM_MIN_BRIGHTNESS      50      // Minimum brightness (5%)
#define PWM_MAX_BRIGHTNESS      1000    // Maximum brightness (100%)
#define PWM_DEFAULT_BRIGHTNESS  500     // Default brightness (50%)

// Brightness levels
typedef enum {
    BRIGHTNESS_OFF = 0,
    BRIGHTNESS_LOW = 200,       // 20%
    BRIGHTNESS_MEDIUM = 500,    // 50%
    BRIGHTNESS_HIGH = 800,      // 80%
    BRIGHTNESS_MAX = 1000       // 100%
} BrightnessLevel_t;

// PWM Control Structure
typedef struct {
    uint16_t current_brightness;
    uint16_t target_brightness;
    uint8_t fade_enabled;
    uint8_t fade_step;
} PWM_Control_t;

// Global PWM control instance
extern PWM_Control_t pwm_control;

// Function Prototypes
void PWM_Config_Init(void);
void PWM_GPIO_Config(void);
void PWM_Timer_Config(void);
void PWM_Set_Brightness(uint16_t brightness);
void PWM_Set_Brightness_Percent(uint8_t percent);
void PWM_Fade_To_Brightness(uint16_t target_brightness);
void PWM_Update_Fade(void);
void PWM_Turn_On(void);
void PWM_Turn_Off(void);
uint16_t PWM_Get_Brightness(void);

#endif // __PWM_CONFIG_H__
